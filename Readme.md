
# Project Name

**OneCarNow-backendv2**

## Prerequisites

Before you begin, ensure you have met the following requirements:

### Node.js and NPM

- Install Node.js (v18.x or higher) and NPM from the [official Node.js website](https://nodejs.org/)
- Verify installation by running:
  ```bash
  node --version
  npm --version
  ```

### MongoDB

1. Install MongoDB Community Edition from the [official MongoDB documentation](https://www.mongodb.com/docs/manual/installation/)
2. Install MongoDB Compass (GUI Tool) from [MongoDB Compass download page](https://www.mongodb.com/try/download/compass)
3. Verify MongoDB installation by running:
   ```bash
   mongod --version
   ```

## Getting Started

Follow these steps to get your development environment running:

1. **Clone the repository**
   ```bash
   git clone https://github.com/username/project-name.git
   cd project-name
   ```

2. **Environment Setup**
   ```bash
   # Copy the sample environment file
   cp .env.example .env

   # Open .env and update the variables according to your setup
   # Example variables:
   # PORT=3000
   # MONGODB_URI=mongodb://localhost:27017/your-database
   # JWT_SECRET=your-secret-key
   ```

3. **Install Dependencies**
   ```bash
   # Install production dependencies
   npm install

   # Install development dependencies
   npm install --save-dev
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```
   The server will start on http://localhost:3000 (or the port specified in your .env)

## Available Scripts

- `npm run dev` - Start development server with hot-reload
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run lint` - Run linting
- `npm run build` - Build for production

## Contributing

1. Clone the repository
2. Create a new branch from `main` (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Merge and test your changes in `dev` branch
6. Open a Pull Request for `main`
   
