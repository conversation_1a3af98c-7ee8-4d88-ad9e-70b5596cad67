/* eslint-disable import/no-extraneous-dependencies */
import { replaceInFile } from 'replace-in-file';

const options = {
  files: 'build/**/*.js',
  from: [
    /require\(['"`]\.\.\/\.\.\/multer['"`]\)/g,
    /require\("\.\.\/multer"\)/g,
    /require\(['"`]\.\.\/\.\.\/express-validator['"`]\)/g,
    /require\("\.\.\/express-validator"\)/g,
    /require\(['"`]\.\.\/\.\.\/\.\.\/\.\.\/express-validator['"`]\)/g,
    /require\("\.\.\/\.\.\/\.\.\/\.\.\/express-validator"\)/g,
  ],
  to: [
    'require("multer")',
    'require("multer")',
    'require("express-validator")',
    'require("express-validator")',
    'require("express-validator")',
    'require("express-validator")',
  ],
};

// Ejecuta el reemplazo
replaceInFile(options)
  .then((results) => {
    results.forEach((result) => {
      if (result.hasChanged) {
        console.log(`File ${result.file} has been modified`);
      }
    });
  })
  .catch((error) => {
    console.error('Error occurred:', error);
  });
