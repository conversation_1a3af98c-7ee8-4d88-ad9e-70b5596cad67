import { OAuth2Client } from 'google-auth-library';
import { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET } from '../../constants';
import { google } from 'googleapis';
import User from '@/models/userSchema';
import { decodeString } from '@/controllers/common/hashing';
import { logger } from '@/clean/lib/logger';
import { MeetingLinkCreationException } from '@/clean/errors/exceptions';

const client = new OAuth2Client(GOOGLE_CLIENT_ID);

export async function verifyGoogleIdToken(idToken: string) {
  try {
    const ticket = await client.verifyIdToken({
      idToken,
      audience: GOOGLE_CLIENT_ID,
    });
    return ticket.getPayload();
  } catch (err) {
    throw new Error('User is not authorized');
  }
}

const oauth2Client = new google.auth.OAuth2(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET);

const initializeOAuth2Client = async (userId: string) => {
  try {
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('Invalid Id, User not found in users table');
    }
    const encodedRefreshToken = user.googleRefreshToken;
    if (!encodedRefreshToken) {
      throw new Error('User has not connected their Google account');
    }

    const decodedRefreshToken = decodeString(encodedRefreshToken);
    oauth2Client.setCredentials({
      refresh_token: decodedRefreshToken,
    });
    return user;
  } catch (err) {
    throw err;
  }
};

export async function getMeetingLink({ userId, summary, start, end, attendees }: any) {
  try {
    const user = await initializeOAuth2Client(userId);
    attendees.push(user.email);
    const calendar = google.calendar({ version: 'v3', auth: oauth2Client });

    const event = {
      summary: summary,
      start: { dateTime: start, timeZone: 'UTC' },
      end: { dateTime: end, timeZone: 'UTC' },
      attendees: attendees,
      conferenceData: {
        createRequest: {
          requestId: 'meet-' + Date.now(),
          conferenceSolutionKey: { type: 'hangoutsMeet' },
        },
      },
    };
    const res = await calendar.events.insert({
      calendarId: 'primary',
      requestBody: event,
      conferenceDataVersion: 1,
    });
    const meetingLink = res.data.conferenceData?.entryPoints?.[0]?.uri;

    /**
     * we are deleting the event because we don't want to store the event in the calendar
     * we just want the meeting link
     */
    try {
      await calendar.events.delete({
        calendarId: 'primary',
        eventId: res.data.id!,
      });
    } catch (error: any) {
      logger.error('[getMeetingLink] - Error occured while deleting the event', {
        message: error.message,
        stack: error.stack,
      });
    }
    return meetingLink;
  } catch (error: any) {
    logger.error(`[getMeetingLink] - Error occured while creating the google meet link, ${error?.message}`, {
      message: error.message,
      stack: error.stack,
    });
    throw new MeetingLinkCreationException({});
  }
}
