import axios from 'axios';
import { WIRE4_API_URL, WIRE4_SUCRIPTION, WIRE4_I80_SUCRIPTION, WIRE4_OLD_SUCRIPTION } from '../constants';
import { getWire4Token, i80GetWire4Token, oldGetWire4Token } from './getWire4Token';

export async function createWire4BankAccount(userData: Object) {
  const token = await getWire4Token();
  const response = await axios.post(
    `${WIRE4_API_URL}/subscriptions/${WIRE4_SUCRIPTION}/depositants`,
    userData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  );

  return response.data;
}

export async function createWire4I80BankAccount(userData: Object) {
  const token = await i80GetWire4Token();
  const response = await axios.post(
    `${WIRE4_API_URL}/subscriptions/${WIRE4_I80_SUCRIPTION}/depositants`,
    userData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  );

  return response.data;
}

export async function createWire4OldBankAccount(userData: Object) {
  const token = await oldGetWire4Token();
  const response = await axios.post(
    `${WIRE4_API_URL}/subscriptions/${WIRE4_OLD_SUCRIPTION}/depositants`,
    userData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  );

  return response.data;
}
