import { genericMessages } from '../constants';

interface CustomStatusError {
  customStatus: number;
}

export class CustomError extends Error implements CustomStatusError {
  public customStatus: number;

  constructor(message: string, customStatus: number) {
    super(message);
    this.name = this.constructor.name;
    this.customStatus = customStatus;
    Error.captureStackTrace(this, this.constructor);
  }

  toString() {
    return `${this.name}: ${this.message} (customStatus: ${this.customStatus})`;
  }
}

export function getCustomErrorStatusAndMessage(err: CustomError | any) {
  if (err instanceof CustomError) {
    return {
      status: err.customStatus,
      message: err.message,
    };
  } else
    return {
      status: 500,
      message: genericMessages.errors.somethingWentWrong,
    };
}
