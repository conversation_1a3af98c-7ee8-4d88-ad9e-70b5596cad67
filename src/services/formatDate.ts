/**
 * Function to format date from 'DD-MM-YYYY' to 'YYYY-MM-DD'
 * @param date - date in format 'YYYY-MM-DD'
 * @returns date in format 'YYYY-MM-DD'
 */
export function formatDate(date: string) {
  const [day, month, year] = date.split('-');
  return `${year}-${month}-${day}`;
}

/**
 * Function to revert date from 'YYYY-MM-DD' to 'DD-MM-YYYY'
 * @param date - date in format 'YYYY-MM-DD'
 * @returns date in format 'DD-MM-YYYY'
 */

export function revertDate(date: string) {
  const [year, month, day] = date.split('-');
  return `${day}-${month}-${year}`;
}
