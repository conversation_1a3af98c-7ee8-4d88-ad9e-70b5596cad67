import axios from 'axios';
import { WIRE4_API_URL, WIRE4_SUCRIPTION, WIRE4_I80_SUCRIPTION } from '../constants';
import { getWire4Token, i80GetWire4Token } from './getWire4Token';

export async function getWire4AllAccounts(suscription: string) {
  const checkSuscription = suscription === 'i80' ? WIRE4_I80_SUCRIPTION : WIRE4_SUCRIPTION;
  const token = suscription === 'i80' ? await i80GetWire4Token() : await getWire4Token();
  const response = await axios(`${WIRE4_API_URL}/subscriptions/${checkSuscription}/depositants`, {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  return response.data;
}
