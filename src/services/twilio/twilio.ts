import { logger } from '@/clean/lib/logger';
import { TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_SERVICE_ID } from '@/constants';
import { Twi<PERSON> } from 'twilio';

const client = new Twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);

interface ITwilioVerificationCode {
  toPhoneNumber: string;
}

interface ITwilioVerificationCodeMessage extends ITwilioVerificationCode {
  code: string;
}

export const sendVerificationCode = async ({ toPhoneNumber }: ITwilioVerificationCode): Promise<boolean> => {
  let isVerificationCodeSend = false;
  try {
    const response = await client.verify.v2.services(TWILIO_SERVICE_ID).verifications.create({
      channel: 'sms',
      to: toPhoneNumber,
    });
    logger.info(`Message sent to ${toPhoneNumber} and status is ${response.status}`);
    switch (response.status) {
      case 'pending':
        isVerificationCodeSend = true;
      case 'max_attempts_reached':
      case 'canceled':
      case 'failed':
      case 'deleted':
      case 'expired':
        isVerificationCodeSend = false;
    }
  } catch (error) {
    logger.error(`Unable to send verification code to number ${toPhoneNumber}`, error);
  }
  return isVerificationCodeSend;
};

export const requestCodeVerification = async ({
  toPhoneNumber,
  code,
}: ITwilioVerificationCodeMessage): Promise<boolean> => {
  try {
    const response = await client.verify.v2.services(TWILIO_SERVICE_ID).verificationChecks.create({
      code: code,
      to: toPhoneNumber,
    });
    logger.info(`OTP verification called to ${toPhoneNumber} and status is ${response.status}`);
    return response.status === 'approved';
  } catch (error) {
    logger.error(`Unable to verify code to number ${toPhoneNumber}`, error);
  }
  return false;
};
