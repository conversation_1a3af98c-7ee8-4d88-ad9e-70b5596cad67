import { STP_CONSTANTS } from '../constants';

export const createStpAccounts = (account: string) => {
  const preAccount = [...STP_CONSTANTS.accounts.preAccount];
  const multiply = STP_CONSTANTS.accounts.multiply;
  const accountToNumberArray = account.split('');

  accountToNumberArray.map((number) => {
    preAccount.push(parseInt(number));
  });

  const multiplyAccount = preAccount.map((number, index) => {
    return (number * multiply[index]) % 10;
  });

  const sum = multiplyAccount.reduce((a, b) => a + b, 0);
  const module = sum % 10;
  const result = 10 - module;
  return `${preAccount.join('')}${result}`;
};
