// import axios from 'axios';
import { associatePaymentsConsts } from '../constants';
import AssociatePayments from '../models/associatePayments';
// import { ValidRegion, tokenAssignGigstack } from './tokenAssignGigstack';
import { saveGigErrors } from './saveGigError';
// interface DataProp {}

export const updateWalletMonexGig = async (spei: any) => {
  try {
    const associatePayments = await AssociatePayments.findOne({ monexEmail: spei.depositant_email });
    if (!associatePayments) {
      await saveGigErrors('updateWalletMonexGig', spei, {
        error: 'no se encontró el email en la colección de asociados',
      });
      return { message: associatePaymentsConsts.errors.mainContract404 };
    }
    // const gigToken = tokenAssignGigstack(associatePayments.region as ValidRegion);
    // const config = {
    //   headers: {
    //     Authorization: `Bearer ${gigToken}`,
    //     'Content-Type': 'application/json',
    //   },
    // };
    associatePayments.balance += spei.amount;

    // if (spei.amount >= 100 && associatePayments.block) {
    //   const gigBodyBlock = {
    //     id: associatePayments.newPaymentsArr[associatePayments.newPaymentsArr.length - 1].blockPayment
    //       .paymentId,
    //     paymentForm: '03',
    //   };
    //   try {
    //     const response = await axios.put(
    //       'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/markaspaid',
    //       gigBodyBlock,
    //       config
    //     );
    //     console.log(response);
    //   } catch (error) {
    //     await saveGigErrors('updateWalletMonexGig', spei, error);
    //     console.error(error);
    //   }
    // }

    // const gigBody = {
    //   id: associatePayments.newPaymentsArr[associatePayments.newPaymentsArr.length - 1].paymentId,
    //   paymentForm: '03',
    // };

    const paymentToUpdate = associatePayments.newPaymentsArr[associatePayments.newPaymentsArr.length - 1];

    if (!paymentToUpdate) {
      await saveGigErrors('updateWalletMonexGig', spei, { error: 'No se encontró el payment to update' });
      return { message: associatePaymentsConsts.errors.payment404 };
    }
    // if (paymentToUpdate.weeklyCost && associatePayments.balance >= 0) {
    //   const { data } = await axios.put(
    //     'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/markaspaid',
    //     gigBody,
    //     config
    //   );
    //   await associatePayments.save();

    //   return data;
    // }

    await associatePayments.save();

    return { message: associatePaymentsConsts.success.paymentUpdated };
  } catch (error) {
    await saveGigErrors('updateWalletMonexGig', spei, error);
    console.error(error);
    return { message: associatePaymentsConsts.errors.error, error };
  }
};
