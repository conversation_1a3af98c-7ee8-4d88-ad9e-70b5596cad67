import { uploadFile } from '../aws/s3';
import Document from '../models/documentSchema';
import { removeEmptySpacesNameFile } from './removeEmptySpaces';

export const createDocAndUpload = async (file: Express.Multer.File, idModelReference: string) => {
  const removeEmptySpacesFilename = removeEmptySpacesNameFile(file);

  const doc = new Document({
    originalName: removeEmptySpacesFilename,
    path: `stock/${idModelReference}/` + removeEmptySpacesFilename,
  });
  await uploadFile(file, removeEmptySpacesFilename, `stock/${idModelReference}/`);
  await doc.save();
  return doc;
};
