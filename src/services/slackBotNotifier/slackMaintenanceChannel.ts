import { logger } from '@/clean/lib/logger';
import { isDev, SLACK_MAINTENANCE_CHANNEL_ID, SLACK_NOTIFIER_BOT_TOKEN } from '@/constants';
import { WebClient } from '@slack/web-api';
import { DateTime } from 'luxon';

const slackClient = new WebClient(SLACK_NOTIFIER_BOT_TOKEN);
const CHANNEL_ID = SLACK_MAINTENANCE_CHANNEL_ID;

interface UserData {
  firstName: string;
  lastName: string;
  phone: string;
}

interface VehicleData {
  _id: string;
  brand: string;
  model: string;
  year: string;
  version: string;
  vin: string;
  // carNumber: string;
  contractNumber: string;
}

const url = isDev ? 'https://develop.administrador.onecarnow.com' : 'https://administrador.onecarnow.com';

/**
 * Main function to send notifications to Slack
 */
export const sendSlackNotification = {
  /**
   * Notifies about a confirmed/created maintenance appointment
   */
  appointmentCreated: async (appointmentData: {
    userData: UserData;
    vehicleData: VehicleData;
    startTime: string;
    maintenanceType: string;
    maintenanceNumber: number;
    maintenanceKm: number;
    workshopName: string;
    adminCreatorEmail?: string;
  }) => {
    try {
      const { userData, vehicleData, startTime, maintenanceType, maintenanceNumber, maintenanceKm } =
        appointmentData;

      const formattedStartTime = DateTime.fromISO(startTime)
        .setLocale('es')
        .toFormat("dd 'de' LLLL 'de' yyyy 'a las' HH:mm 'hrs.'"); // Example: 15 de octubre de 2023 a las 10:30 hrs.

      // Format user info
      const userInfo = `${userData.firstName} ${userData.lastName}, con numero de telefono: ${userData.phone}`;

      // Format vehicle info
      const vehicleInfo = `Contrato: ${vehicleData.contractNumber}\n${vehicleData.brand} ${vehicleData.model} ${vehicleData.year} ${vehicleData.version} \nVIN: ${vehicleData.vin}`;

      const result = await slackClient.chat.postMessage({
        channel: CHANNEL_ID,
        text: 'Nueva cita de mantenimiento confirmada',
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              // check if adminCreatorEmail is provided, if so, add it to the text
              text: appointmentData.adminCreatorEmail
                ? `✅ Nueva cita de mantenimiento confirmada por ${appointmentData.adminCreatorEmail}`
                : '✅ Nueva cita de mantenimiento confirmada',
              emoji: true,
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Conductor:*\n${userInfo}`,
              },
              {
                type: 'mrkdwn',
                text: `*Vehiculo:*\n${vehicleInfo}`,
              },
              {
                type: 'mrkdwn',
                text: `*Fecha y hora:*\n${formattedStartTime}`,
              },
              {
                type: 'mrkdwn',
                text: `*Taller:*\n${appointmentData.workshopName}`,
              },
              {
                type: 'mrkdwn',
                text: `*Tipo de servicio:*\n${maintenanceType} (#${maintenanceNumber} - ${maintenanceKm}km)`,
              },
              {
                type: 'mrkdwn',
                text: `Ver mantenimiento en: <${url}/dashboard/flotilla/activos/${vehicleData._id}|OCN>`,
              },
            ],
          },
        ],
      });

      console.log('Maintenance appointment notification sent to Slack', result);
      logger.info('[sendSlackNotification] Notification of maintenance appointment sent to Slack', result);
      return true;
    } catch (error) {
      console.log('Error sending notification to Slack', error);
      logger.error('[sendSlackNotification] Error sending notification to Slack', error);
      return false;
    }
  },

  /**
   * Notifies about errors in maintenance requests
   */
  maintenanceError: async (errorData: {
    userData: UserData;
    vehicleData: VehicleData;
    errorType: string;
    errorDetails: string;
    adminCreatorEmail?: string;
  }) => {
    try {
      const { userData, vehicleData, errorType, errorDetails } = errorData;

      // Format user info
      const userInfo = `${userData.firstName} ${userData.lastName}, con numero de telefono: ${userData.phone}`;

      // Format vehicle info

      const vehicleInfo = `Contrato: ${vehicleData.contractNumber}\n${vehicleData.brand} ${vehicleData.model} ${vehicleData.year} ${vehicleData.version} \nVIN: ${vehicleData.vin}`;
      const result = await slackClient.chat.postMessage({
        channel: CHANNEL_ID,
        text: 'Error en solicitud de mantenimiento',
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              // text: '⚠️ Error en solicitud de mantenimiento',
              text: errorData.adminCreatorEmail
                ? `⚠️ Error en solicitud de mantenimiento desde el panel de admin, intentado por ${errorData.adminCreatorEmail}`
                : '⚠️ Error en solicitud de mantenimiento fuera del panel de admin',
              emoji: true,
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Conductor:*\n${userInfo}`,
              },
              {
                type: 'mrkdwn',
                text: `*Vehiculo:*\n${vehicleInfo}`,
              },
              {
                type: 'mrkdwn',
                text: `*Tipo de error:*\n${errorType}`,
              },
            ],
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Detalles:*\n${errorDetails}`,
            },
          },
        ],
      });

      console.log('Error notification sent to Slack');
      logger.info('[sendSlackNotification] Notification of maintenance error sent to Slack', result);
      return true;
    } catch (error: any) {
      logger.error('[sendSlackNotification] Error sending notification to Slack', error);
      return false;
    }
  },
};

// Usage example:

// sendSlackNotification.appointmentCreated({
//   userData: {
//     firstName: 'John',
//     lastName: 'Doe',
//     phone: '1234567890',
//   },
//   vehicleData: {
//     _id: '1234567890',
//     brand: 'Toyota',
//     model: 'Corolla',
//     year: '2022',
//     version: '1.0',
//     vin: '1234567890',
//     contractNumber: '1024-2',
//   },
//   startTime: '2023-10-15T10:30:00',
//   workshopName: 'Taller Central',
//   maintenanceType: 'Preventive',
//   maintenanceNumber: 1,
//   maintenanceKm: 10000,
// });

// sendSlackNotification.maintenanceError({
//   userData: {
//     firstName: 'John',
//     lastName: 'Doe',
//     phone: '1234567890',
//   },
//   vehicleData: {
//     _id: '1234567890',
//     brand: 'Toyota',
//     model: 'Corolla',
//     year: '2022',
//     version: '1.0',
//     vin: '1234567890',
//     contractNumber: '1024-2',
//   },
//   errorType: 'Kilometraje no alcanza el próximo mantenimiento requerido',
//   errorDetails:
//     'El kilometraje actual del vehículo no alcanza el próximo mantenimiento requerido. Kilometraje actual: 10000, Kilometraje requerido: 15000',
// });
