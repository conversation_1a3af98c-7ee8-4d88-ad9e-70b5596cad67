import { getUrlSingleFile } from '../aws/s3';
import Document from '../models/documentSchema';

/* Todas las funciones reemplazan la relación del Document con la información del documento con las sig props:
   - {url del documento}
   - {nombrePropiedad}DocId con el id del documento
   - {nombrePropiedad}OriginalName con el nombre original del documento
 */
// Retorna el objeto con todas las propiedades y reasigna la relación con el Document con la url y sus propiedades

export const mapReplaceObjectWithDocUrl = async <T extends Record<string, any>, K extends keyof T>(
  array: T[],
  propertyName: string
) => {
  const propName = propertyName as keyof T;

  const getPropertyWithUrls = await Promise.all(
    array.map(async (item) => {
      const document = await Document.findById(item[propertyName]);
      if (!document) return null;

      const documentPath = document.path;
      const documentUrl = await getUrlSingleFile(documentPath);
      const modifiedItem = { ...item };
      const nestedObject = {
        url: documentUrl as T[K],
        docId: document._id as T[K],
        originalName: document.originalName as T[K],
      };

      modifiedItem[propName] = { ...(modifiedItem[propName] as T), ...nestedObject } as any;
      return modifiedItem;
    })
  );
  return getPropertyWithUrls;
};

export const mapReplaceArrayOfDocsId = async <T extends Record<string, any>>(array?: T[]) => {
  if (!array || array.length === 0) return [];

  const getPropertyWithUrls = await Promise.all(
    array?.map(async (docRef) => {
      if (!docRef || !docRef._id) return null;
      const doc = await Document.findById(docRef._id);
      if (!doc) return null;
      const documentPath = doc.path;
      const documentUrl = await getUrlSingleFile(documentPath);
      return {
        docId: doc._id,
        originalName: doc.originalName,
        url: documentUrl,
      };
    })
  );
  return getPropertyWithUrls;
};

export const replaceSingleObjectWithDocUrl = async <T extends Record<string, any>, K extends keyof T>(
  obj: T | undefined,
  propertyNames: string[]
) => {
  if (!obj) return null;
  const modifiedItem = { ...obj };

  for (const propertyName of propertyNames) {
    const propName = propertyName as keyof T;

    const document = await Document.findById(obj[propertyName]);
    if (document) {
      const documentPath = document?.path || '';
      const documentUrl = await getUrlSingleFile(documentPath);

      const nestedObject = {
        url: documentUrl as T[K],
        docId: document?._id as T[K],
        originalName: document?.originalName as T[K],
      };

      modifiedItem[propName] = { ...modifiedItem[propName], ...nestedObject };
    } else {
      modifiedItem[propName] = null as any;
    }
  }

  return modifiedItem;
};

export const replaceSingleObjectWithDocUrlSureste = async <T extends Record<string, any>, K extends keyof T>(
  obj: T | undefined,
  propertyNames: string[]
) => {
  if (!obj) return null;
  const modifiedItem = { ...obj };

  for (const propertyName of propertyNames) {
    const propName = propertyName as keyof T;

    const document = await Document.findById(obj[propertyName]);
    if (document) {
      const documentPath = document?.path || '';
      const documentUrl = await getUrlSingleFile(documentPath);

      const nestedObject = {
        url: documentUrl as T[K],
        docId: document?._id as T[K],
        originalName: document?.originalName as T[K],
      };

      modifiedItem[propName] = { ...(modifiedItem[propName] as T), ...nestedObject } as any;
    } else {
      modifiedItem[propName] = null as any;
    }
  }

  return modifiedItem;
};

export const replaceArrayOfDocsObjectsWithDocUrl = async (arrOfObjs: any) => {
  const modifiedItem = [];
  for (const obj of arrOfObjs) {
    const document = await Document.findById(obj);
    if (document) {
      const documentPath = document?.path || '';
      const documentUrl = await getUrlSingleFile(documentPath);

      const nestedObject = {
        url: documentUrl,
        docId: document?._id,
        originalName: document?.originalName,
      };
      modifiedItem.push({ ...nestedObject });
    }
  }
  return modifiedItem;
};

export const replaceDocWithUrl = async (id: string | undefined) => {
  if (!id) return null;
  const document = await Document.findById(id);
  if (!document) return null;

  const documentPath = document.path;
  const documentUrl = await getUrlSingleFile(documentPath);

  const obj = {
    url: documentUrl,
    docId: document._id,
    originalName: document.originalName,
  };

  return obj;
};
