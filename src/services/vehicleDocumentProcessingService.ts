import { logger } from '../clean/lib/logger';
import { createHash } from 'crypto';
import { repoGetDocumentData, repoDeleteDocument } from '../clean/data/s3Repositories';
import {
  parseStructuredTextFromSource,
  StructuredSource,
  AllowedMimeType,
} from '../services/ocr/llmClients/geminiClient';
import StockVehicle from '../models/StockVehicleSchema';
import Document from '../models/documentSchema';
import {
  slackChannelNotifier,
  getSlackUserIdByEmail,
  createSlackSummaryNotification,
} from '../services/slackBotNotifier/slackBot';
import { vehicleDocumentsSlackTexts, SLACK_NOTIFIER_BOT_TOKEN, CountriesEnum } from '../constants';
import { Types } from 'mongoose';
import {
  VehicleDocumentBatchPayload,
  ProcessedDocumentResult,
  DocumentCategory,
  ProcessedDocumentStatus,
} from '../types&interfaces/vehicleDocuments';
import { ObjectSchema, Schema, SchemaType } from '@google/generative-ai';
import * as cheerio from 'cheerio';
import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { HttpsProxyAgent, HttpsProxyAgentOptions } from 'https-proxy-agent';
import { getCurrentDateTime } from './timestamps'; // Added import
import { checkAndUpdateVehicleStatus } from './stockVehicles/vehicleFunctions';

// Centralized error types for document processing
enum DocumentProcessingErrorType {
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  DUPLICATE_DOCUMENT = 'DUPLICATE_DOCUMENT',
  NOT_AUTHENTIC = 'NOT_AUTHENTIC',
  VALIDITY_EXPIRED = 'VALIDITY_EXPIRED',
  INVALID_VALIDITY_DATE = 'INVALID_VALIDITY_DATE',
  PROCESSING_ERROR = 'PROCESSING_ERROR',
  VEHICLE_NOT_FOUND = 'VEHICLE_NOT_FOUND',
  MISSING_FIELDS = 'MISSING_FIELDS',
}

// Custom error to carry processing type, status, and technical messages
class DocumentProcessingError extends Error {
  public type: DocumentProcessingErrorType;

  public status: string;

  public technicalMessage?: string;

  constructor(opts: {
    type: DocumentProcessingErrorType;
    message: string;
    status?: string;
    technicalMessage?: string;
  }) {
    super(opts.message);
    this.name = 'DocumentProcessingError';
    this.type = opts.type;
    this.status =
      opts.status ||
      (opts.type === 'FILE_TOO_LARGE'
        ? 'skipped'
        : opts.type === 'DUPLICATE_DOCUMENT'
          ? 'duplicate'
          : 'error');
    this.technicalMessage = opts.technicalMessage;
  }
}

// VIN validation and extraction regex constants
const VIN_ALLOWED_CHARS = '[A-HJ-NPR-Z0-9]';
const VIN_EXACT_REGEX = new RegExp(`^${VIN_ALLOWED_CHARS}{17}$`, 'i');
const VIN_WORD_REGEX = new RegExp(`\\b(${VIN_ALLOWED_CHARS}{17})\\b`, 'gi');

// TO USE PROXY WITH VERIFY=FALSE
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

/**
 * Parses HTML content to extract the VIN value.
 * @param {string} htmlContent The HTML content as a string.
 * @returns {string | null} The extracted VIN, or null if not found.
 */
function parseVinFromHtml(htmlContent: string): string | null {
  if (!htmlContent) {
    return null;
  }

  const $ = cheerio.load(htmlContent);

  // Find the list item containing "Número de serie:"
  let vinText: string = '';
  $('li').each((index, element) => {
    const strongTag = $(element).find('strong');
    if (strongTag.length && strongTag.text().trim() === 'Número de serie:') {
      // The VIN is the text immediately following the <strong> tag within the <li>
      let fullText = $(element).text();
      vinText = fullText.replace(strongTag.text(), '').replace(':', '').trim();
      if (VIN_EXACT_REGEX.test(vinText)) {
        // Found and validated
        return false; // Exit .each loop
      }
      vinText = ''; // Reset if not valid
    }
    return true; // Continue the loop
  });

  if (vinText) {
    return vinText.toUpperCase();
  }

  // Fallback: Try to match a VIN pattern in text if specific li not found or VIN invalid
  const text = $('body').text().replace(/\s+/g, ' '); // Get all text, normalize whitespace
  const vinRegex = VIN_WORD_REGEX;
  const match = vinRegex.exec(text);
  if (match && match[1]) {
    if (VIN_EXACT_REGEX.test(match[1])) {
      return match[1].toUpperCase();
    }
  }

  // Fallback: look for input named 'vin'
  let vinInput = $('input[name="vin"]').first();
  if (!vinInput.length) {
    vinInput = $('input[id="vin"]').first();
  }
  if (vinInput.length) {
    const vinValue = vinInput.val();
    if (typeof vinValue === 'string' && VIN_EXACT_REGEX.test(vinValue)) {
      return vinValue.toUpperCase();
    }
  }

  return null;
}

const createFileHash = (buffer: Buffer): string => {
  return createHash('sha256').update(buffer).digest('hex');
};

class VehicleDocumentProcessingService {
  private successCount: number;

  private errorCount: number;

  constructor() {
    this.successCount = 0;
    this.errorCount = 0;
  }

  private initializeProcessingState(): void {
    this.successCount = 0;
    this.errorCount = 0;
  }

  public async processVehicleDocumentBatch(payload: VehicleDocumentBatchPayload): Promise<void> {
    this.initializeProcessingState(); // Reset for the current batch

    const { documents, userId, userName, userEmail } = payload;
    logger.info(
      `[VehicleDocService] Starting batch processing for ${documents.length} docs. User: ${userName}`
    );

    // Resolve Slack user mention ID
    const slackUserIdOrNull = await getSlackUserIdByEmail({
      email: userEmail,
      BotToken: SLACK_NOTIFIER_BOT_TOKEN,
    });
    const slackUserId = slackUserIdOrNull || undefined; // Convert null to undefined

    const processingResults: ProcessedDocumentResult[] = [];
    let skippedCount = 0;
    let duplicateCount = 0;
    // Track number of documents per category
    const categoryCounts: Record<string, number> = {};

    // Process each document sequentially
    for (const docInfo of documents) {
      // Count this document's category
      categoryCounts[docInfo.documentCategory] = (categoryCounts[docInfo.documentCategory] || 0) + 1;
      let result: ProcessedDocumentResult = {
        originalFileName: docInfo.originalFileName,
        s3Key: docInfo.s3Key,
        status: 'error',
      };
      try {
        // 1. Fetch file from S3 and convert to Base64
        logger.info(
          `[VehicleDocService] Processing file: ${docInfo.originalFileName} (s3Key: ${docInfo.s3Key})`
        );
        const fileBase64 = await repoGetDocumentData(docInfo.s3Key);
        if (!fileBase64) {
          throw new DocumentProcessingError({
            type: DocumentProcessingErrorType.PROCESSING_ERROR,
            message: 'No se pudo obtener datos del archivo desde S3.',
          });
        }
        const fileBuffer = Buffer.from(fileBase64, 'base64');

        // File size check (5MB)
        if (fileBuffer.byteLength > 5 * 1024 * 1024) {
          throw new DocumentProcessingError({
            type: DocumentProcessingErrorType.FILE_TOO_LARGE,
            message: 'El archivo excede el límite de 5MB.',
            status: 'skipped',
          });
        }

        const fileHash = createFileHash(fileBuffer);

        // 2. OCR to extract VIN and category-specific data
        const ocrData = await this.extractOCRData(docInfo, fileBase64, payload.country);
        result.vin = ocrData?.vin?.trim() || null;
        logger.info(
          `[VehicleDocService] OCR/QR processing successful for ${docInfo.originalFileName}. Final VIN: ${result.vin}`
        );

        // 3. Match VIN or plates to StockVehicle
        let vehicle;
        if (
          docInfo.documentCategory === DocumentCategory.PLATES_FRONT ||
          docInfo.documentCategory === DocumentCategory.PLATES_BACK
        ) {
          const platesFromOCR = ocrData.plates?.trim() || null;
          if (!platesFromOCR) {
            throw new DocumentProcessingError({
              type: DocumentProcessingErrorType.MISSING_FIELDS,
              message: 'No se encontró número de placa en el OCR.',
            });
          }
          result.plates = platesFromOCR;
          vehicle = await StockVehicle.findOne({ 'carPlates.plates': platesFromOCR });
          if (!vehicle) {
            throw new DocumentProcessingError({
              type: DocumentProcessingErrorType.VEHICLE_NOT_FOUND,
              message: `Vehículo con placas ${platesFromOCR} no encontrado.`,
            });
          }
        } else {
          if (!result.vin) {
            throw new DocumentProcessingError({
              type: DocumentProcessingErrorType.MISSING_FIELDS,
              message: 'VIN no encontrado o vacío en OCR.',
            });
          }
          vehicle = await StockVehicle.findOne({ vin: result.vin });
          if (!vehicle) {
            throw new DocumentProcessingError({
              type: DocumentProcessingErrorType.VEHICLE_NOT_FOUND,
              message: `Vehículo con VIN ${result.vin} no encontrado.`,
            });
          }
        }
        result.vehicleCarNumber = vehicle.carNumber;

        // 4. Check for Duplicates
        const existingDocument = await Document.findOne({
          vehicleId: vehicle._id,
          documentCategory: docInfo.documentCategory,
          fileHash: fileHash,
        });
        if (existingDocument) {
          throw new DocumentProcessingError({
            type: DocumentProcessingErrorType.DUPLICATE_DOCUMENT,
            message: 'Documento duplicado para este vehículo y categoría.',
            status: 'duplicate',
          });
        }

        // 5. Create Document Record
        const newDocument = new Document({
          originalName: docInfo.originalFileName,
          path: docInfo.s3Key,
          mimeType: docInfo.contentType,
          vehicleId: vehicle._id,
          userId: new Types.ObjectId(userId),
          documentCategory: docInfo.documentCategory,
          vin: result.vin,
          fileHash: fileHash,
          status: 'processed',
        });
        await newDocument.save();
        result.documentId = newDocument._id.toString();

        // 6. Update StockVehicle
        const oldDocIdsToMove: Types.ObjectId[] = [];
        const updateSuccess = this.assignOcrDataToVehicle({
          vehicle,
          documentCategory: docInfo.documentCategory,
          newDocumentId: newDocument._id,
          ocrData,
          oldDocIdsToMove,
        });

        if (updateSuccess) {
          if (oldDocIdsToMove.length > 0) {
            const docsToMove = await Document.find({ _id: { $in: oldDocIdsToMove } });
            for (const oldDoc of docsToMove) {
              // Clear existing fileHash on the old document and save
              if (oldDoc.fileHash !== undefined) {
                oldDoc.fileHash = undefined;
                await oldDoc.save();
              }
              // Move old document reference into vehicle.oldDocuments
              vehicle.oldDocuments.push({ path: oldDoc.path, originalName: oldDoc.originalName });
            }
          }

          // Add to vehicle update history
          vehicle.updateHistory.push({
            userId: new Types.ObjectId(userId),
            step: 'DOCUMENTACIÓN',
            description: `Se asignó el documento ${docInfo.originalFileName} (${docInfo.documentCategory}).`,
            time: getCurrentDateTime(),
          });

          checkAndUpdateVehicleStatus(vehicle, userId); // Check and update vehicle status

          await vehicle.save(); // Save again to persist updateHistory changes

          logger.info(
            `[VehicleDocService] Successfully updated vehicle ${vehicle.carNumber} with document ${newDocument._id} for category ${docInfo.documentCategory}`
          );
          result.status = 'success';
          this.successCount++;
        }
        processingResults.push(result);
      } catch (err: any) {
        // Normalize any thrown into DocumentProcessingError
        const procError =
          err instanceof DocumentProcessingError
            ? err
            : new DocumentProcessingError({
                type: DocumentProcessingErrorType.PROCESSING_ERROR,
                message: err.message,
              });

        // Cleanup S3 and record result
        if (docInfo.s3Key) {
          await repoDeleteDocument(docInfo.s3Key);
        }

        result.status = procError.status as ProcessedDocumentStatus;
        result.error = {
          type: procError.type,
          message: procError.message,
          technicalMessage: procError.technicalMessage,
        };

        if (procError.status === 'skipped') {
          skippedCount++;
        } else if (procError.status === 'duplicate') {
          duplicateCount++;
        } else {
          this.errorCount++;
        }
        processingResults.push(result);
      }
    }

    // Send summary Slack notification, including per-category counts
    this.sendSummarySlackNotification(
      processingResults,
      payload,
      { successCount: this.successCount, errorCount: this.errorCount, skippedCount, duplicateCount },
      categoryCounts,
      slackUserId
    );
    logger.info(
      `[VehicleDocService] Batch processing finished for user ${userName}. Success: ${this.successCount}, Errors: ${this.errorCount}, Skipped: ${skippedCount}, Duplicates: ${duplicateCount}`
    );
  }

  /* eslint-disable max-params */
  private async sendSummarySlackNotification(
    results: ProcessedDocumentResult[],
    payload: VehicleDocumentBatchPayload,
    counts: { successCount: number; errorCount: number; skippedCount: number; duplicateCount: number },
    categoryCounts: Record<string, number>,
    slackUserId?: string // payload.slackChannelId is used internally
  ): Promise<void> {
    /* eslint-enable max-params */
    try {
      const { successCount, errorCount, skippedCount, duplicateCount } = counts;
      const totalFiles = results.length;
      const userNameForSlack = slackUserId ? `<@${slackUserId}>` : payload.userName;

      // build Spanish summary
      let summaryMessage = `Resumen de carga masiva de documentos de vehículo para ${userNameForSlack}:\n`;
      summaryMessage += `*Total de archivos procesados*: ${totalFiles}\n`;
      // Add breakdown per document category
      summaryMessage += `*Por categoría*:\n`;
      for (const [category, count] of Object.entries(categoryCounts)) {
        summaryMessage += `  - ${category}: ${count}\n`;
      }
      summaryMessage += `*Procesados con éxito*: ${successCount}\n`;
      summaryMessage += `*Errores*: ${errorCount}\n`;
      summaryMessage += `*Omitidos (p.ej., demasiado grandes)*: ${skippedCount}\n`;
      summaryMessage += `*Duplicados encontrados*: ${duplicateCount}\n`;

      const filesWithErrors = results.filter(
        (r) => r.status === 'error' || r.status === 'skipped' || r.status === 'duplicate'
      );
      let errorDetailsCSV: string | undefined = undefined;

      if (filesWithErrors.length > 0) {
        summaryMessage += `\n*Detalles de archivos con problemas*:`;
        // Build CSV with detailed error rows
        const csvRows = [
          [
            'Nombre de archivo',
            'Mensaje de error',
            'Estado',
            'VIN (intentado)',
            'Vehículo (intentado)',
            'Tipo de error',
          ],
        ];
        filesWithErrors.forEach((r) => {
          csvRows.push([
            r.originalFileName,
            r.error?.message || 'N/A',
            r.status,
            r.vin || 'N/A',
            r.vehicleCarNumber || 'N/A',
            r.error?.type || 'N/A',
          ]);
        });
        // Summarize error types in text summary instead of listing each file
        const errorTypeCounts: Record<string, number> = {};
        filesWithErrors.forEach((r) => {
          const type = r.error?.type || DocumentProcessingErrorType.PROCESSING_ERROR;
          errorTypeCounts[type] = (errorTypeCounts[type] || 0) + 1;
        });
        if (Object.keys(errorTypeCounts).length > 0) {
          summaryMessage += `\n*Por tipo de error*:`;
          for (const [type, count] of Object.entries(errorTypeCounts)) {
            summaryMessage += `\n  - ${type}: ${count}`;
          }
        }
        errorDetailsCSV = csvRows
          .map((row) => row.map((cell) => `"${(cell || '').replace(/"/g, '""')}"`).join(','))
          .join('\n');
      }

      // Truncate summary message to meet Slack's 3000 character limit
      if (summaryMessage.length > 3000) {
        summaryMessage = summaryMessage.substring(0, 3000 - 3) + '...';
      }

      const headerText =
        errorCount > 0 || skippedCount > 0 || duplicateCount > 0
          ? vehicleDocumentsSlackTexts.processCompleteWithFailure
          : vehicleDocumentsSlackTexts.processComplete;

      await slackChannelNotifier({
        message: createSlackSummaryNotification({
          headerText: headerText,
          userName: userNameForSlack,
          summaryMessage: summaryMessage,
          hasErrors: errorCount > 0 || skippedCount > 0 || duplicateCount > 0,
          bottomText:
            errorCount > 0 || skippedCount > 0 || duplicateCount > 0
              ? 'Revise los errores en el CSV adjunto (si lo hay) o los registros del sistema.'
              : 'Todos los documentos se procesaron correctamente.',
        }),
        file: errorDetailsCSV
          ? {
              content: errorDetailsCSV,
              filename: `vehicle_doc_upload_errors_${new Date().toISOString().split('T')[0]}.csv`,
              title: 'Vehicle Document Upload Errors',
              filetype: 'csv',
            }
          : undefined,
        BotToken: SLACK_NOTIFIER_BOT_TOKEN,
        ChannelId: payload.slackChannelId,
      });
    } catch (slackError) {
      logger.error('[VehicleDocService] CRITICAL: Failed to send summary Slack notification:', slackError);
    }
  }

  // Helper: construct OCR prompt text (without schema)
  private buildOcrPrompt(): string {
    return 'Extract the requested information and return ONLY the data values as a clean JSON object without any extra explanation.';
  }

  // Helper: construct JSON schema for structured output based on document category
  private buildOcrSchema(documentCategory: DocumentCategory): Schema {
    const commonSchema: Schema = {
      type: SchemaType.OBJECT,
      properties: {
        vin: {
          type: SchemaType.STRING,
          description:
            'Vehicle Identification Number: 17-character uppercase alphanumeric code (may be labeled VIN, NIV, SERIE or serial number). Example: LSGEN530XPD009568',
        },
      },
      required: ['vin'],
    };
    const platesSchema: Schema = {
      type: SchemaType.OBJECT,
      properties: {
        plates: {
          type: SchemaType.STRING,
          description:
            'License plate number: uppercase letters and numbers only, no special characters. Example: ABC1234',
        },
      },
      required: ['plates'],
    };
    switch (documentCategory) {
      case DocumentCategory.INSURANCE_POLICY:
        return {
          ...commonSchema,
          properties: {
            ...commonSchema.properties,
            isAuthentic: {
              type: SchemaType.BOOLEAN,
              description:
                'Indicates if the document actually is an insurance policy(see identifiers in the document like policy etc that should point to whether the document passes as an insurance policy document) from which the policy number is extracted',
            },
            policyNumber: {
              type: SchemaType.STRING,
              description:
                'Insurance policy number: as printed on the document, alphanumeric and may include dashes. Example: **********',
            },
            insurer: {
              type: SchemaType.STRING,
              description:
                'Name of the insurance company: full official title as shown on the policy. Example: Acme Seguros S.A. de C.V.',
            },
            validity: {
              type: SchemaType.STRING,
              description: 'Policy expiry date in YYYY-MM-DD format. Example: 2024-12-31',
            },
            broker: {
              type: SchemaType.STRING,
              description:
                'Insurance broker name: as it appears on policy document often deonted by "agenete" or similar word. Example: Qualitas',
            },
          },
          required: ['vin', 'isAuthentic', 'policyNumber', 'insurer', 'validity', 'broker'],
        };
      case DocumentCategory.TENENCIA:
        return {
          ...commonSchema,
          properties: {
            ...commonSchema.properties,
            isAuthentic: {
              type: SchemaType.BOOLEAN,
              description:
                'Indicates if the document actually is a tenencia document(see identifiers in the document like tenencia etc that should point to whether the document passes as a tenencia document) from which the data is extracted',
            },
            payment: {
              type: SchemaType.NUMBER,
              description: 'Payment amount: numeric value in MXN without currency symbols. Example: 1234.56',
            },
            validity: {
              type: SchemaType.STRING,
              description: 'Tax validity expiry date in YYYY-MM-DD format. Example: 2024-01-21',
            },
          },
          required: ['vin', 'isAuthentic', 'payment', 'validity'],
        };
      case DocumentCategory.CIRCULATION_CARD_FRONT:
        return {
          ...commonSchema,
          properties: {
            ...commonSchema.properties,
            isAuthentic: {
              type: SchemaType.BOOLEAN,
              description:
                'Indicates if the document actually is a circulation card(see identifiers in the document like tarjeta de circulación etc that should point to whether the document passes as a circulation card document) from which the data is extracted',
            },
            number: {
              type: SchemaType.STRING,
              description:
                'Circulation card number: alphanumeric code printed on front side. Example: 41657053172',
            },
            validity: {
              type: SchemaType.STRING,
              description:
                'Circulation card last validity date in YYYY-MM-DD format, find from the date of issue and years of validity. Example: 2026-06-30',
            },
          },
          required: ['vin', 'isAuthentic', 'number', 'validity'],
        };
      case DocumentCategory.CIRCULATION_CARD_BACK:
        return {
          type: SchemaType.OBJECT,
          properties: {
            serialNumber: {
              type: SchemaType.STRING,
              description:
                'Folio serial number: 7-character alphanumeric code on back of circulation card. Example: ABC1234',
            },
          },
          required: ['serialNumber'],
        };
      case DocumentCategory.PLATES_ALTA_PLACAS:
        return {
          ...commonSchema,
          properties: {
            ...commonSchema.properties,
            ...platesSchema.properties,
            isAuthentic: {
              type: SchemaType.BOOLEAN,
              description:
                'Indicates if the document actually is a plates alta document(see identifiers in the document like alta placas etc that should point to whether the document passes as a plates alta document) from which the data is extracted',
            },
          },
          required: ['vin', 'plates', 'isAuthentic'],
        };
      case DocumentCategory.PLATES_FRONT:
      case DocumentCategory.PLATES_BACK:
        return {
          ...platesSchema,
          properties: {
            ...platesSchema.properties,
            isAuthentic: {
              type: SchemaType.BOOLEAN,
              description:
                'Indicates if the document actually has vehicle plates from which the plates number is extracted',
            },
          },
          required: ['plates', 'isAuthentic'],
        };
      case DocumentCategory.FACTURE:
        return {
          ...commonSchema,
          properties: {
            ...commonSchema.properties,
            isAuthentic: {
              type: SchemaType.BOOLEAN,
              description:
                'Indicates if the document actually is an invoice(see identifiers in the document like factura etc that should point to whether the document passes as an invoice) from which the data is extracted',
            },
            billNumber: {
              type: SchemaType.STRING,
              description: 'Invoice number: alphanumeric identifier on invoice. Example: 4239342239',
            },
            billDate: {
              type: SchemaType.STRING,
              description: 'Invoice issuance date in YYYY-MM-DD format. Example: 2025-05-10',
            },
            billAmount: {
              type: SchemaType.NUMBER,
              description: 'Invoice total amount as numeric value without currency symbol. Example: 1500.75',
            },
          },
          required: ['vin', 'isAuthentic', 'billNumber', 'billDate', 'billAmount'],
        };
      default:
        return commonSchema;
    }
  }

  private getFolioPageUrl(serialNumber: string): string {
    // Construct the folio page URL based on the serial number
    return `https://www.semovi.cdmx.gob.mx/controlvehicular/tarjetacirculacion/validar.php?folio=${serialNumber}`;
  }

  private async fetchVinFromService(serialNumber: string): Promise<string | null> {
    const folioPageUrl = this.getFolioPageUrl(serialNumber);
    let vinFromHtml: string | null = null;
    let response; // To hold axios response

    const baseHeaders = {
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      Accept: '*/*',
      'Accept-Encoding': 'gzip, deflate, zstd',
      Connection: 'keep-alive',
    };

    // Common config options for both attempts
    const commonAxiosConfigOptions = {
      timeout: 10000, // 10 seconds timeout
      responseType: 'text' as 'text',
      validateStatus: (status: number) => status < 600, // Handle all statuses, check manually
      maxRedirects: 5,
    };

    // Attempt 1: Direct connection (no proxy)
    const directAxiosConfig: AxiosRequestConfig = {
      ...commonAxiosConfigOptions,
      headers: baseHeaders,
    };

    try {
      logger.info(`[VehicleDocService] Attempting direct fetch for VIN. URL: ${folioPageUrl}`);
      response = await axios.get(folioPageUrl, directAxiosConfig);

      if (response.status >= 200 && response.status < 300 && response.data) {
        vinFromHtml = parseVinFromHtml(response.data);
        if (vinFromHtml) {
          logger.info(
            `[VehicleDocService] Successfully fetched and parsed VIN (direct). VIN: ${vinFromHtml}`
          );
          return vinFromHtml; // Success, no need for proxy
        } else {
          logger.warn(
            `[VehicleDocService] Could not parse VIN from HTML (direct, 2xx response). URL: ${folioPageUrl}. Retrying with proxy.`
          );
        }
      } else {
        logger.warn(
          `[VehicleDocService] Direct fetch returned non-2xx status: ${response.status}. URL: ${folioPageUrl}. Retrying with proxy.`
        );
      }
    } catch (directError: any) {
      logger.warn(
        `[VehicleDocService] Direct fetch failed (e.g., timeout, network error). URL: ${folioPageUrl}. Error: ${
          directError.message
        }. Retrying with proxy.`
      );
      if (axios.isAxiosError(directError)) {
        const err = directError as AxiosError;
        if (err.response) {
          // This case is less likely for a timeout but could be other server errors not caught by validateStatus if it were stricter
          logger.warn(`[VehicleDocService] Direct Axios error details - Status: ${err.response.status}`);
        } else if (err.request) {
          // This is typical for timeout or network error
          logger.warn(`[VehicleDocService] Direct Axios error details - No response. Code: ${err.code}`);
        }
      }
    }

    // If vinFromHtml is still null, it means direct attempt failed (error, non-2xx, or parsing failure)
    // Proceed to Attempt 2: Via Proxy
    if (vinFromHtml === null) {
      logger.info(
        `[VehicleDocService] Proceeding to proxy fetch for URL: ${folioPageUrl} as direct attempt was unsuccessful.`
      );

      const proxyHost = '***************';
      const proxyPort = 443;
      const proxyConnectUrl = `https://${proxyHost}:${proxyPort}`;

      const agentOptions: HttpsProxyAgentOptions<string> = {
        keepAlive: false,
        rejectUnauthorized: false,
      };
      const httpsAgentForTunneling = new HttpsProxyAgent(proxyConnectUrl, agentOptions);

      const proxyAxiosConfig: AxiosRequestConfig = {
        ...commonAxiosConfigOptions, // Includes the 5000ms timeout
        headers: {
          ...baseHeaders,
          'Proxy-Connection': 'Keep-Alive',
        },
        httpsAgent: httpsAgentForTunneling,
      };

      try {
        logger.info(
          `[VehicleDocService] Attempting fetch with proxy. URL: ${folioPageUrl}, Proxy: ${proxyConnectUrl}`
        );
        response = await axios.get(folioPageUrl, proxyAxiosConfig);

        if (response.status >= 200 && response.status < 300 && response.data) {
          vinFromHtml = parseVinFromHtml(response.data);
          if (vinFromHtml) {
            logger.info(
              `[VehicleDocService] Successfully fetched and parsed VIN (proxy). VIN: ${vinFromHtml}`
            );
          } else {
            logger.warn(
              `[VehicleDocService] Could not parse VIN from HTML (proxy, 2xx response). URL: ${folioPageUrl}`
            );
          }
        } else {
          logger.warn(
            `[VehicleDocService] Proxy fetch returned non-2xx status: ${response.status}. URL: ${folioPageUrl}`
          );
        }
      } catch (proxyError: any) {
        logger.error(
          `[VehicleDocService] Proxy fetch failed. URL: ${folioPageUrl}, Proxy: ${proxyConnectUrl}. Error: ${proxyError.message}`
        );
        if (axios.isAxiosError(proxyError)) {
          const err = proxyError as AxiosError;
          if (err.response) {
            logger.error(
              `[VehicleDocService] Proxy Axios error details - Status: ${
                err.response.status
              }, Data (first 100 chars): ${String(err.response.data).substring(0, 100)}`
            );
          } else if (err.request) {
            logger.error(`[VehicleDocService] Proxy Axios error details - No response. Code: ${err.code}`);
          }
        }
      }
    }
    return vinFromHtml;
  }

  // Helper: handle OCR data enrichment for CIRCULATION_CARD_BACK
  private async handleCirculationCardBackOcrData(llmOcrData: any): Promise<any> {
    const serialNumber = llmOcrData.serialNumber;
    if (!serialNumber) {
      logger.warn('[VehicleDocService] Serial number not found in OCR result for CIRCULATION_CARD_BACK');
      throw new Error('Serial number not found in OCR result for CIRCULATION_CARD_BACK.');
    }
    const vinFromService = await this.fetchVinFromService(serialNumber);
    if (vinFromService) {
      llmOcrData.vin = vinFromService;
    } else {
      logger.warn(
        `[VehicleDocService] Proceeding without VIN from external service for serial ${serialNumber}`
      );
      llmOcrData.vinFetchFailed = true;
    }
    return llmOcrData;
  }

  // Helper: validate that OCR result includes all required fields and no placeholder types
  private validateOcrData(ocrResult: any, schema: ObjectSchema): void {
    const missingFields: string[] = [];
    const placeholderFields: string[] = [];
    if (Array.isArray(schema.required)) {
      for (const key of schema.required) {
        const val = ocrResult[key];
        const prop = schema.properties[key];
        const expectedType = prop?.type;
        if (val === undefined) {
          missingFields.push(key);
        } else if (expectedType === val) {
          placeholderFields.push(key);
        }
      }
    }
    if (missingFields.length || placeholderFields.length) {
      const allMissingFields = [...missingFields, ...placeholderFields];
      if (allMissingFields.length) {
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.MISSING_FIELDS,
          message: `Validación de OCR fallida, faltan los campos: [${allMissingFields.join(',')}]`,
          status: 'skipped',
        });
      }
    }

    // Check authenticity if required
    if (Array.isArray(schema.required) && schema.required.includes('isAuthentic')) {
      if (ocrResult.isAuthentic === false) {
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.NOT_AUTHENTIC,
          message: 'El documento no pasó la verificación de autenticidad.',
          status: 'skipped',
        });
      }
    }

    // Check validity date if required
    if (Array.isArray(schema.required) && schema.required.includes('validity') && ocrResult.validity) {
      const validityDate = new Date(ocrResult.validity);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (isNaN(validityDate.getTime())) {
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.INVALID_VALIDITY_DATE,
          message: `Formato de fecha de validez inválido: ${ocrResult.validity}`,
          status: 'skipped',
        });
      }
      if (validityDate <= today) {
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.VALIDITY_EXPIRED,
          message: `Fecha de validez expirada: ${ocrResult.validity}`,
          status: 'skipped',
        });
      }
    }
  }

  // Helper: single OCR call to extract VIN and data fields
  private async extractOCRData(
    docInfo: VehicleDocumentBatchPayload['documents'][0],
    fileBase64: string,
    country?: string
  ): Promise<any> {
    // For all document types, proceed with structured LLM OCR
    const prompt = this.buildOcrPrompt();
    const schema = this.buildOcrSchema(docInfo.documentCategory);
    const structuredSource: StructuredSource = {
      data: fileBase64,
      media_type: docInfo.contentType as AllowedMimeType,
      prompt,
      filename: docInfo.originalFileName,
      responseSchema: schema,
    };
    let llmOcrData = (await parseStructuredTextFromSource(structuredSource)) || {};
    // Delegate handling of circulation card back case to helper
    if (docInfo.documentCategory === DocumentCategory.CIRCULATION_CARD_BACK) {
      llmOcrData = await this.handleCirculationCardBackOcrData(llmOcrData);
    }
    const ocrResult = { ...llmOcrData };
    if (country !== CountriesEnum['United States']) {
      this.validateOcrData(ocrResult, schema as ObjectSchema);
    }

    return ocrResult;
  }

  // Helper to assign OCR data fields to the vehicle based on document category
  private assignOcrDataToVehicle(options: {
    vehicle: any;
    documentCategory: DocumentCategory;
    newDocumentId: Types.ObjectId;
    ocrData: any; // This will contain vinFromQrPage if applicable
    oldDocIdsToMove: Types.ObjectId[];
  }): boolean {
    const { vehicle, documentCategory, newDocumentId, ocrData, oldDocIdsToMove } = options;
    switch (documentCategory) {
      case DocumentCategory.INSURANCE_POLICY:
        vehicle.policiesArray.push({
          _id: new Types.ObjectId(),
          policyDocument: newDocumentId,
          policyNumber: parseInt(ocrData.policyNumber) || 0,
          insurer: ocrData.insurer || 'N/A',
          validity: ocrData.validity || 'N/A',
          broker: ocrData.broker || 'N/A',
        });
        return true;
      case DocumentCategory.TENENCIA:
        vehicle.tenancy.push({
          _id: new Types.ObjectId(),
          payment: parseFloat(ocrData.payment) || 0,
          validity: ocrData.validity || 'N/A',
          tenancyDocument: newDocumentId,
        });
        return true;
      case DocumentCategory.CIRCULATION_CARD_FRONT:
        if (!vehicle.circulationCard) vehicle.circulationCard = { number: '', validity: '', older: [] };
        if (vehicle.circulationCard.frontImg) oldDocIdsToMove.push(vehicle.circulationCard.frontImg!);
        vehicle.circulationCard.frontImg = newDocumentId;
        vehicle.circulationCard.number = ocrData.number || vehicle.circulationCard.number;
        vehicle.circulationCard.validity = ocrData.validity || vehicle.circulationCard.validity;
        return true;
      case DocumentCategory.CIRCULATION_CARD_BACK:
        if (!vehicle.circulationCard) vehicle.circulationCard = { number: '', validity: '', older: [] }; // Initialize if not present
        if (vehicle.circulationCard.backImg) oldDocIdsToMove.push(vehicle.circulationCard.backImg!);
        vehicle.circulationCard.backImg = newDocumentId;

        // VIN assignment: Prioritize VIN from QR page if available
        if (ocrData.vinFromQrPage) {
          vehicle.vin = ocrData.vinFromQrPage;
          logger.info(`[VehicleDocService] Assigning VIN from QR Page to vehicle: ${ocrData.vinFromQrPage}`);
        } else if (ocrData.vin && !vehicle.vin) {
          // This fallback for ocrData.vin should ideally not be hit for CIRCULATION_CARD_BACK anymore
          // as LLM OCR is skipped. Kept for safety, but implies an unexpected ocrData structure if hit.
          vehicle.vin = ocrData.vin;
          logger.warn(
            `[VehicleDocService] Assigning VIN from LLM to vehicle (CIRCULATION_CARD_BACK fallback - unexpected): ${ocrData.vin}`
          );
        }

        // Number and validity are NOT assigned from ocrData for CIRCULATION_CARD_BACK
        // as LLM OCR is skipped and these fields are not expected from QR page.
        return true;
      case DocumentCategory.PLATES_ALTA_PLACAS:
        if (!vehicle.carPlates) {
          vehicle.carPlates = {
            plates: '',
            frontImg: undefined,
            backImg: undefined,
            platesDocument: undefined,
          };
        }
        if (vehicle.carPlates.platesDocument) {
          oldDocIdsToMove.push(vehicle.carPlates.platesDocument!);
        }
        vehicle.carPlates.platesDocument = newDocumentId;
        vehicle.carPlates.plates = ocrData.plates || vehicle.carPlates.plates;
        return true;
      case DocumentCategory.PLATES_FRONT:
        if (!vehicle.carPlates) {
          vehicle.carPlates = {
            plates: '',
            frontImg: undefined,
            backImg: undefined,
            platesDocument: undefined,
          };
        }
        if (vehicle.carPlates.frontImg) oldDocIdsToMove.push(vehicle.carPlates.frontImg!);
        vehicle.carPlates.frontImg = newDocumentId;
        vehicle.carPlates.plates = ocrData.plates || vehicle.carPlates.plates;
        return true;
      case DocumentCategory.PLATES_BACK:
        if (!vehicle.carPlates) {
          vehicle.carPlates = {
            plates: '',
            frontImg: undefined,
            backImg: undefined,
            platesDocument: undefined,
          };
        }
        if (vehicle.carPlates.backImg) oldDocIdsToMove.push(vehicle.carPlates.backImg!);
        vehicle.carPlates.backImg = newDocumentId;
        vehicle.carPlates.plates = ocrData.plates || vehicle.carPlates.plates;
        return true;
      case DocumentCategory.FACTURE:
        if (vehicle.bill) oldDocIdsToMove.push(vehicle.bill!);
        vehicle.bill = newDocumentId;
        vehicle.billNumber = ocrData.billNumber || vehicle.billNumber;
        vehicle.billDate = ocrData.billDate || vehicle.billDate;
        vehicle.billAmount = parseFloat(ocrData.billAmount) || vehicle.billAmount;
        return true;
      default:
        throw new Error(`Invalid document category: ${documentCategory}`);
    }
  }
}

export const vehicleDocumentProcessingService = new VehicleDocumentProcessingService();
