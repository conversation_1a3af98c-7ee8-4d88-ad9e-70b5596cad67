import qrcode from 'qrcode';
import { Types } from 'mongoose';
import Document from '../models/documentSchema';
import { uploadFile } from '../aws/s3';
import { logger } from '@/clean/lib/logger';

const FRONTEND_ADMIN_URL = process.env.FRONTEND_ADMIN_URL;

/**
 * Generates a QR code for a vehicle, uploads it, and saves the Document reference.
 *
 * @param vehicleId - The MongoDB ObjectId of the vehicle.
 * @param carNumber - The car number (used for file path/naming).
 * @returns The ObjectId of the saved QR code Document, or null if generation/upload failed.
 */
export const generateAndUploadQrCode = async (
  vehicleId: Types.ObjectId,
  carNumber: string | undefined
): Promise<Types.ObjectId | null> => {
  if (!vehicleId || !carNumber) {
    logger.error('[generateAndUploadQrCode] Missing vehicleId or carNumber for QR code generation');
    return null;
  }

  try {
    // Generate a QR code that points to the redirect-info endpoint
    // The frontend can then handle the dynamic redirection based on the response
    const qrCodeUrl = `${FRONTEND_ADMIN_URL}/dashboard/flotilla/vehicle-redirect/${vehicleId.toString()}`;

    logger.info(
      `[generateAndUploadQrCode] Generating QR code for vehicleId ${vehicleId} with URL: ${qrCodeUrl}`
    );

    const qrCodeBuffer = await qrcode.toBuffer(qrCodeUrl, {
      errorCorrectionLevel: 'H', // High - provides best error correction
      type: 'png',
      width: 300,
      margin: 1,
    });

    // Define file paths and names
    const qrCodeDirectory = 'vehicle-qrcodes/ocn-admin/';
    const qrCodeFileName = `${carNumber}_qrcode_${vehicleId}.png`;
    const qrCodePath = `${qrCodeDirectory}${qrCodeFileName}`;

    // Create a Document record for the QR code
    const qrCodeDoc = new Document({
      originalName: qrCodeFileName,
      path: qrCodePath,
      vehicleId: vehicleId,
    });

    // Create an object mimicking Multer.File for the uploadFile function
    const qrCodeFileData: Express.Multer.File = {
      fieldname: 'qrCode',
      originalname: qrCodeFileName,
      encoding: '7bit',
      mimetype: 'image/png',
      buffer: qrCodeBuffer,
      size: qrCodeBuffer.length,
      destination: qrCodeDirectory,
      filename: qrCodeFileName,
      path: qrCodePath,
      stream: null as any, // Stream not needed for buffer upload
    };

    logger.info(`[generateAndUploadQrCode] Uploading QR code to: ${qrCodePath}`);
    await uploadFile(qrCodeFileData, qrCodeFileName, qrCodeDirectory);

    await qrCodeDoc.save();

    logger.info(
      `[generateAndUploadQrCode] QR code successfully generated and saved with document ID: ${qrCodeDoc._id}`
    );
    return qrCodeDoc._id;
  } catch (error) {
    logger.error(`[generateAndUploadQrCode] Error generating QR code for vehicle ${vehicleId}:`, error);
    return null;
  }
};
