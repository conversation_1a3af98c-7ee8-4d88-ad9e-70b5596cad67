import axios from 'axios';
import { WIRE4_API_URL, WIRE4_SUCRIPTION, WIRE4_OLD_SUCRIPTION } from '../constants';
import { getWire4Token, oldGetWire4Token } from './getWire4Token';

export async function getWire4SpeiIncoming(beginDate: string, endDate: string) {
  const token = await getWire4Token();
  const response = await axios(
    `${WIRE4_API_URL}/subscriptions/${WIRE4_SUCRIPTION}/transactions/incoming/spei?beginDate=${beginDate}&endDate=${endDate}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
}

export async function oldGetWire4SpeiIncoming(beginDate: string, endDate: string) {
  const token = await oldGetWire4Token();
  const response = await axios(
    `${WIRE4_API_URL}/subscriptions/${WIRE4_OLD_SUCRIPTION}/transactions/incoming/spei?beginDate=${beginDate}&endDate=${endDate}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
}
