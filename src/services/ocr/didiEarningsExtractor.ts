import { AllowedMimeType, parseTextFromSource, Source } from './llmClients/geminiClient';
import { logger } from '@/clean/lib/logger';

const prompt = `Extract text from the provided Didi earnings screenshot and output a structured JSON object using the following JSON schema:

{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "weeklyEarnings": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "startDate": {
            "type": "string",
            "format": "date"
          },
          "endDate": {
            "type": "string",
            "format": "date"
          },
          "amount": {
            "type": "number"
          }
        },
        "required": ["startDate", "endDate", "amount"],
        "additionalProperties": false
      }
    },
    "currentMonth": {
      "type": "string"
    },
    "currentYear": {
      "type": "integer"
    }
  },
  "required": ["weeklyEarnings", "currentMonth", "currentYear"],
  "additionalProperties": false
}

Additional requirements:
- Handle any language input and translate to english
- Return null for any fields not present in the image
- Format dates in ISO 8601 format (YYYY-MM-DD) when possible
- Use the todays date ${new Date()} to determine the year value of for the date range
- date range will be followed by an amount value vice versa is not possible and should be removed from the output
- date range example 2 dic - 9 dic this is 2nd december to 9th december and output will be "startDate" : "2024-12-02" and "endDate" : "2024-12-09"
- if date range starts in december and ends in january then the value of startDate and endDate should show respective year for example 30 dic - 6 ene should output "startDate" : "2024-12-30" and "endDate" : "2025-01-06"
- Handle year transition for months before the current month (if current month is January) then date ranges of older months will be from the previous year
- Do not include any thing else in the response other than that above JSON structure
- The response should be a valid JSON Object
- Do not use Markdown code block syntax in response`;

const getDidiEarningsData = async (image: Express.Multer.File) => {
  try {
    // Validate the image object
    if (!image || !image.buffer || !image.mimetype) {
      throw new Error('Invalid image file provided');
    }

    // Validate MIME type
    const allowedMimeTypes: AllowedMimeType[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedMimeTypes.includes(image.mimetype as AllowedMimeType)) {
      throw new Error(`Unsupported MIME type: ${image.mimetype}`);
    }

    // Prepare the source object
    const source: Source = {
      data: image.buffer.toString('base64'),
      media_type: image.mimetype as AllowedMimeType,
      prompt: prompt,
    };

    logger.info(`[getDidiEarningsData] - Processing image with MIME type: ${source.media_type}`);

    // Call the Anthropic API to parse the image
    const result = await parseTextFromSource(source);
    return result;
  } catch (error) {
    logger.error(`[getDidiEarningsData] - Error: ${JSON.stringify(error)}`);
    throw new Error(`Failed to extract earnings data: ${JSON.stringify(error)}`);
  }
};

export { getDidiEarningsData };
