import { AllowedMimeType, parseTextFromSource, Source } from './llmClients/geminiClient';
import { logger } from '@/clean/lib/logger';

const prompt = `Extract text from the provided Lyft earnings screenshot and output a structured JSON object using the following JSON schema:

{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "weeklyEarnings": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "startDate": {
            "type": "string",
            "format": "date"
          },
          "endDate": {
            "type": "string",
            "format": "date"
          },
          "amount": {
            "type": "number"
          }
        },
        "required": ["startDate", "endDate", "amount"],
        "additionalProperties": false
      }
    },
    "currentMonth": {
      "type": "string"
    },
    "currentYear": {
      "type": "integer"
    }
  },
  "required": ["weeklyEarnings", "currentMonth", "currentYear"],
  "additionalProperties": false
}

Additional requirements:
- Handle any language input and translate to english
- Return null for any fields not present in the image
- Format dates in ISO 8601 format (YYYY-MM-DD) when possible
- Use the today's date ${new Date()} to determine the year value of for the date range
- The primary date range for the earnings will typically be the most prominent date display or centrally located.
- Exclude any date ranges that appear to be part of navigational elements, such as those immediately preceded by '<' (e.g., "< Apr 21") or followed by '>' (e.g., "May 5 >").
- A valid date range will be followed by an amount value; a date range not associated with an amount should be ignored.
- Date range example like "2 dic - 9 dic" or "Apr 28-May 4" should be processed. For "2 dic - 9 dic", this is 2nd December to 9th December, and the output will be "startDate" : "2024-12-02" and "endDate" : "2024-12-09" (assuming the current year is 2024 and December hasn't passed; adjust year based on today's date and context).
- If a date range starts in December and ends in January (e.g., "30 dic - 6 ene" or "Dec 30-Jan 6"), then the value of startDate and endDate should show respective years. For example, "30 dic - 6 ene" should output "startDate" : "2024-12-30" and "endDate" : "2025-01-06" (assuming the transition happens from 2024 to 2025).
- Handle year transition for months before the current month. For example, if the current month is January ${new Date().getFullYear()}, then date ranges from December of the previous year should be assigned to ${new Date().getFullYear() - 1}. If the image displays a date like "Apr 28-May 4" and the current date is May 8, 2025, the output should be "startDate": "2025-04-28" and "endDate": "2025-05-04".
- Do not include any thing else in the response other than the above JSON structure.
- The response should be a valid JSON Object.
- Do not use Markdown code block syntax in response.`;

const getLyftEarningsData = async (image: Express.Multer.File) => {
  try {
    // Validate the image object
    if (!image || !image.buffer || !image.mimetype) {
      throw new Error('Invalid image file provided');
    }

    // Validate MIME type
    const allowedMimeTypes: AllowedMimeType[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedMimeTypes.includes(image.mimetype as AllowedMimeType)) {
      throw new Error(`Unsupported MIME type: ${image.mimetype}`);
    }

    // Prepare the source object
    const source: Source = {
      data: image.buffer.toString('base64'),
      media_type: image.mimetype as AllowedMimeType,
      prompt: prompt,
    };

    logger.info(`[getLyftEarningsData] - Processing image with MIME type: ${source.media_type}`);

    // Call the Anthropic API to parse the image
    const result = await parseTextFromSource(source);
    return result;
  } catch (error) {
    logger.error(`[getLyftEarningsData] - Error: ${JSON.stringify(error)}`);
    throw new Error(`Failed to extract earnings data: ${JSON.stringify(error)}`);
  }
};

export { getLyftEarningsData };
