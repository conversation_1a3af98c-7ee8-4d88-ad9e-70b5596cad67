import axios from 'axios';
import { associatePaymentsConsts, associateText } from '../constants';
import Associate from '../models/associateSchema';
import RegionsPayments from '../models/regionPaymentsSchema';
import { ValidRegion, tokenAssignGigstack } from '../services/tokenAssignGigstack';
import AssociatePayments from '../models/associatePayments';

export async function createFirstAssociateGigPayment(email: String, regionCode: String) {
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: email });
    if (!associatePayments) {
      throw new Error(associatePaymentsConsts.errors.payment404);
    }
    const associate = await Associate.findById(associatePayments.associateId);

    if (!associate) {
      throw new Error(associateText.errors.associateNotFound);
    }

    const region = await RegionsPayments.findOne({ region: regionCode });
    if (!region) {
      throw new Error(associatePaymentsConsts.errors.notValidRegion);
    }

    if (!associatePayments.model) {
      throw new Error(associatePaymentsConsts.errors.modelNotFound);
    }

    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);

    const dataGig = {
      client: {
        name: `${associate.firstName} ${associate.lastName}`,
        email: associate.email,
      },
      items: [
        {
          id: region.models[associatePayments.model].rentID,
          quantity: 1,
        },
        {
          id: region.models[associatePayments.model].assistanceID,
          quantity: 1,
        },
      ],
      automateInvoiceOnComplete: true,
      currency: 'MXN',
      useClientBankAccont: true,
      methodsTypesOptions: ['bank'],
    };
    const config = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const { data } = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/create',
      dataGig,
      config
    );
    return { message: associatePaymentsConsts.success.paymentCreated, data };
  } catch (error: any) {
    console.error(error);
    return error.response.data.message;
  }
}
