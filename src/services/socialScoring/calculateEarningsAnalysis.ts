/* eslint-disable @typescript-eslint/no-use-before-define */
import { Types } from 'mongoose';
import { determineEarningsAnalysisStatus } from './determineEarningsAnalysisStatus';
import {
  AdmissionRequestMongo,
  EarningsAnalysisMongoI,
  PalencaAccountRetrievalMongoI,
  AdmissionRequestMongoI,
  RequestPalencaMongoI,
  ScorecardDetailMongoI,
  RequestPersonalDataMongoI,
  ScorecardMongoI,
  ScorecardCategoryMongoI,
  RequestDocumentsAnalysisMongoI,
  RequestHomeVisitMongoI,
  RiskAnalysisMongoI,
  PalencaAccountMongoI,
  RequestDocumentsMongoI,
} from '../../models/admissionRequestSchema';
import { EarningMongo, IWeeklyEarning, IDailyEarning } from '../../models/earningSchema';
import {
  EarningsAnalysisStatus,
  GigPlatform,
  WeeklyEarning,
  CurrencyCode,
  AdmissionRequestRejectionReason,
  PalencaRetrievalStatus,
  MIN_WEEKS_TO_ANALYZE,
} from '../../constants';
import {
  ScorecardVariableName,
  AdmissionRequest,
  PalencaAccountRetrieval,
  RequestPalenca,
  Scorecard,
  ScorecardDetailCategory,
  EarningsAnalysis,
  DailyEarning,
  RiskCategory,
  RequestPersonalData,
  RequestPersonalDataStepStatus,
  RequestDocumentsAnalysis,
  RequestDocumentsAnalysisStatus,
  HomeVisit,
  ResidentOwnershipStatus,
  HomeVisitStatus,
  RiskAnalysis,
  ScorecardVersion,
  RiskAnalysisStatus,
  PalencaAccount,
  PalencaAccountStatus,
  RequestDocument,
  AdmissionRequestDocumentType,
  RequestDocumentStatus,
} from '../../constants/socialScoring';

import { changeProperties, createDeal, batchDealAndLead } from '../hubspot';
import { sendMessage, updateHilosContact } from '../sendHilosMessage';
import { HILOS_TEMPLATES } from '../../constants/onboarding';

export const requestPalencaAdapter = (requestPalenca: RequestPalencaMongoI): RequestPalenca => {
  const accounts = requestPalenca.accounts ? requestPalenca.accounts.map(palencaAccountAdapter) : [];

  const requestPalencaEntity = new RequestPalenca({
    widgetId: requestPalenca.widgetId,
    externalId: requestPalenca.externalId,
    accounts,
  });
  return requestPalencaEntity;
};

export const palencaAccountAdapter = (palencaAccount: PalencaAccountMongoI): PalencaAccount => {
  const palencaAccountEntity = new PalencaAccount({
    accountId: palencaAccount.accountId,
    platform: palencaAccount.platform as GigPlatform,
    earnings: palencaAccountRetrievalMongoAdapter(palencaAccount.earnings),
    metrics: palencaAccountRetrievalMongoAdapter(palencaAccount.metrics),
    status: palencaAccount.status as PalencaAccountStatus,
    createdAt: new Date(palencaAccount.createdAt),
  });
  return palencaAccountEntity;
};
export const palencaAccountRetrievalMongoAdapter = (
  palencaAccountRetrieval: PalencaAccountRetrievalMongoI
): PalencaAccountRetrieval => {
  const entity = new PalencaAccountRetrieval({
    status: palencaAccountRetrieval.status as PalencaRetrievalStatus,
  });

  return entity;
};
export const requestPersonalDataMongoAdapter = (
  requestPersonalData: RequestPersonalDataMongoI
): RequestPersonalData => {
  const entity = new RequestPersonalData({
    status: requestPersonalData.status as RequestPersonalDataStepStatus,
    firstName: requestPersonalData.firstName,
    lastName: requestPersonalData.lastName,
    email: requestPersonalData.email,
    phone: requestPersonalData.phone,
    birthdate: requestPersonalData.birthdate,
    taxId: requestPersonalData.taxId,
    nationalId: requestPersonalData.nationalId,
    postalCode: requestPersonalData.postalCode,
    city: requestPersonalData.city,
    state: requestPersonalData.state,
    neighborhood: requestPersonalData.neighborhood,
    street: requestPersonalData.street,
    streetNumber: requestPersonalData.streetNumber,
    department: requestPersonalData.department,
  });

  return entity;
};

export const requestDocumentsAnalysisMongoAdapter = (
  requestDocuments: RequestDocumentsAnalysisMongoI
): RequestDocumentsAnalysis => {
  const documents = requestDocuments.documents.map(requestDocumentAdapter);

  const entity = new RequestDocumentsAnalysis({
    status: requestDocuments.status as RequestDocumentsAnalysisStatus,
    documents,
  });
  return entity;
};
export const requestDocumentAdapter = (document: RequestDocumentsMongoI): RequestDocument => {
  const entity = new RequestDocument({
    mediaId: document.mediaId ? document.mediaId.toString() : null,
    status: document.status as RequestDocumentStatus,
    type: document.type as AdmissionRequestDocumentType,
  });

  return entity;
};

export const riskAnalysisMongoAdapter = (riskAnalysis: RiskAnalysisMongoI): RiskAnalysis => {
  const scorecard = riskAnalysis.scorecard ? scorecardMongoAdapter(riskAnalysis.scorecard) : null;

  const entity = new RiskAnalysis({
    status: riskAnalysis.status as RiskAnalysisStatus,
    scorecardVersion: riskAnalysis.scorecardVersion as ScorecardVersion,
    scorecard,
  });
  return entity;
};

export const admissionRequestMongoAdapter = (
  admissionRequestMongo: AdmissionRequestMongoI
): AdmissionRequest => {
  const palenca = requestPalencaAdapter(admissionRequestMongo.palenca);
  const personalData = requestPersonalDataMongoAdapter(admissionRequestMongo.personalData);
  const documentsAnalysis = requestDocumentsAnalysisMongoAdapter(admissionRequestMongo.documentsAnalysis);
  const earningsAnalysis = earningsAnalysisMongoAdapter(admissionRequestMongo.earningsAnalysis);
  const homeVisit = admissionRequestMongo.homeVisit
    ? homeVisitMongoAdapter(admissionRequestMongo.homeVisit)
    : undefined;
  const riskAnalysis = riskAnalysisMongoAdapter(admissionRequestMongo.riskAnalysis);

  const admissionRequest = new AdmissionRequest({
    id: admissionRequestMongo._id.toString(),
    status: admissionRequestMongo.status,
    rejectionReason: admissionRequestMongo.rejectionReason as AdmissionRequestRejectionReason,
    palenca,
    personalData,
    documentsAnalysis,
    homeVisit: homeVisit,
    earningsAnalysis: earningsAnalysis,
    riskAnalysis: riskAnalysis,
    createdAt: new Date(admissionRequestMongo.createdAt),
    updatedAt: new Date(admissionRequestMongo.updatedAt),
  });
  return admissionRequest;
};
import { Document } from '../../constants/socialScoring';
import { Country } from '../../clean/domain/enums';
import { MetricMongo, MetricMongoI } from '../../models/metricsSchema';
import { logger } from '../../clean/lib/logger';

export const homeVisitMongoAdapter = (
  homeVisit: RequestHomeVisitMongoI,
  media?: Document[] | null
): HomeVisit => {
  const availableMedia = media ? media : null;
  const entity = new HomeVisit({
    isAddressProvidedByApplicant: homeVisit.isAddressProvidedByApplicant,
    residentOwnershipStatus: homeVisit.residentOwnershipStatus as ResidentOwnershipStatus,
    hasGarage: homeVisit.hasGarage,
    comments: homeVisit.comments,
    images: homeVisit.images,
    status: homeVisit.status as HomeVisitStatus,
    responsible: homeVisit.responsible,
    visitDate: homeVisit.visitDate,
    media: availableMedia,
  });

  return entity;
};
export const scorecardMongoAdapter = (scorecard: ScorecardMongoI): Scorecard => {
  const entity = new Scorecard({
    totalScore: scorecard.totalScore,
    scaledScore: scorecard.scaledScore,
    minScore: scorecard.minScore,
    maxScore: scorecard.maxScore,
    minScaledScore: scorecard.minScaledScore,
    maxScaledScore: scorecard.maxScaledScore,
    details: scorecard.details.map(scorecardDetailMongoAdapter),
  });
  return entity;
};
export const scorecardDetailMongoAdapter = (scorecardDetail: ScorecardDetailMongoI): ScorecardDetail => {
  const entity = new ScorecardDetail({
    variable: scorecardDetail.variable as ScorecardVariableName,
    category: scorecardCategoryMongoAdapter(scorecardDetail.category),
  });

  return entity;
};

export const scorecardCategoryMongoAdapter = (
  scorecardCategory: ScorecardCategoryMongoI
): ScorecardDetailCategory => {
  const entity = new ScorecardDetailCategory({
    threshold: scorecardCategory.threshold as { min: number; max: number },
    riskCategory: scorecardCategory.riskCategory as RiskCategory,
    riskScore: scorecardCategory.riskScore,
    weight: scorecardCategory.weight,
    result: scorecardCategory.result,
  });

  return entity;
};

export class ScorecardDetail {
  variable: ScorecardVariableName;

  category: ScorecardDetailCategory;

  constructor(props: ScorecardDetail) {
    this.variable = props.variable;
    this.category = props.category;
  }
}

export const earningsAnalysisMongoAdapter = (earningsAnalysis: EarningsAnalysisMongoI): EarningsAnalysis => {
  const entity = new EarningsAnalysis({
    totalEarnings: earningsAnalysis.totalEarnings,
    earnings: earningsAnalysis.earnings.map(weeklyEarningMongoAdapter),
    platforms: earningsAnalysis.platforms,
    status: earningsAnalysis.status as EarningsAnalysisStatus,
  });
  return entity;
};

export const repoSaveEarningsAnalysis = async (requestId: string, earningsAnalysis: EarningsAnalysis) => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    throw new Error('Admission request not found');
  }

  currentAdmissionRequest.earningsAnalysis = {
    status: earningsAnalysis.status,
    totalEarnings: earningsAnalysis.totalEarnings,
    earnings: earningsAnalysis.earnings,
    platforms: earningsAnalysis.platforms,
  } as EarningsAnalysisMongoI;

  const saved = await currentAdmissionRequest.save();
  return admissionRequestMongoAdapter(saved);
};

export const dailyEarningMongoAdapter = (dailyEarning: IDailyEarning): DailyEarning => {
  const entity = new DailyEarning({
    amount: dailyEarning.amount,
    countTrips: dailyEarning.countTrips,
    earningDate: dailyEarning.earningDate,
    currency: dailyEarning.currency as CurrencyCode,
  });
  return entity;
};

export const weeklyEarningMongoAdapter = (weeklyEarning: IWeeklyEarning): WeeklyEarning => {
  const dailyEarnings = weeklyEarning.dailyEarnings.map(dailyEarningMongoAdapter);
  const entity = new WeeklyEarning({
    totalAmount: weeklyEarning.totalAmount,
    totalTrips: weeklyEarning.totalTrips,
    fromDate: weeklyEarning.fromDate,
    toDate: weeklyEarning.toDate,
    week: weeklyEarning.week,
    year: weeklyEarning.year,
    currency: weeklyEarning.currency as CurrencyCode,
    dailyEarnings,
  });
  return entity;
};

export const repoRetrieveWeeklyEarnings = async (
  requestId: string,
  platform?: GigPlatform
): Promise<WeeklyEarning[]> => {
  const objectId = new Types.ObjectId(requestId);

  const matchCondition: { [key: string]: any } = { requestId: objectId };

  if (platform) {
    matchCondition.platform = platform;
  }

  const query = EarningMongo.aggregate<IWeeklyEarning>([
    {
      $match: matchCondition,
    },
    {
      $addFields: {
        totalEarnings: { $add: ['$amount', '$cashAmount'] },
        week: { $isoWeek: '$earningDate' },
        year: { $isoWeekYear: '$earningDate' },
      },
    },
    {
      $group: {
        _id: { week: '$week', year: '$year' },
        dailyEarnings: {
          $push: {
            amount: '$totalEarnings',
            countTrips: '$countTrips',
            earningDate: '$earningDate',
            currency: '$currency',
          },
        },
        totalAmount: { $sum: '$totalEarnings' },
        totalTrips: { $sum: '$countTrips' },
        currency: { $first: '$currency' },
      },
    },
    {
      $addFields: {
        fromDate: { $dateFromParts: { isoWeekYear: '$_id.year', isoWeek: '$_id.week', isoDayOfWeek: 1 } },
        toDate: { $dateFromParts: { isoWeekYear: '$_id.year', isoWeek: '$_id.week', isoDayOfWeek: 7 } },
      },
    },
    {
      $sort: { '_id.year': -1, '_id.week': -1 },
    },
    {
      $project: {
        _id: 0,
        currency: 1,
        week: '$_id.week',
        year: '$_id.year',
        dailyEarnings: 1,
        totalAmount: 1,
        totalTrips: 1,
        fromDate: 1,
        toDate: 1,
      },
    },
  ]);

  const weeklyEarnings = await query.exec();
  return weeklyEarnings.map((weeklyEarning) => {
    return weeklyEarningMongoAdapter(weeklyEarning);
  });
};

export const retrieveMostRecent12WeeksEarnings = async (
  requestId: string,
  platform?: GigPlatform
): Promise<WeeklyEarning[]> => {
  const weeklyEarnings = await repoRetrieveWeeklyEarnings(requestId, platform);

  const sortedWeeks = weeklyEarnings.sort((a, b) => {
    // First compare by year
    if (a.year > b.year) return -1;
    if (a.year < b.year) return 1;

    // If the years are equal, compare by week
    return b.week - a.week;
  });

  const mostRecent12Weeks = sortedWeeks.slice(0, 12);
  return mostRecent12Weeks;
};

export const updateAdmissionRequestPalencaAccountStatus = async (
  requestId: string,
  preQualification: EarningsAnalysisStatus
) => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    throw new Error('Admission request not found');
  }
  let status = 'failure';
  switch (preQualification) {
    case EarningsAnalysisStatus.approved:
      status = 'success';
      break;
    case EarningsAnalysisStatus.rejected:
      status = 'failure';
      break;
    case EarningsAnalysisStatus.approved_with_conditions:
      status = 'success';
      break;
    default:
      status = 'failure';
  }
  currentAdmissionRequest.palenca.accounts.forEach((account) => {
    account.status = status;
    account.earnings.status = status;
    account.metrics.status = status;
  });
  await currentAdmissionRequest.save();
};

export const calculateEarningsAnalysis = async (requestId: string) => {
  //Primero se trae el id de la base de datos
  logger.info(`[calculateEarningsAnalysis] Starting earnings analysis calculation for request ${requestId}`);

  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!currentAdmissionRequest) throw new Error('Admission request not found');

  // If the earnings analysis is not pending we do nothing
  // if (currentAdmissionRequest.earningsAnalysis?.status !== EarningsAnalysisStatus.pending)
  //   return currentAdmissionRequest;

  if (currentAdmissionRequest.personalData.country === Country.us) {
    const updatedAdmissionRequest = await earningsAnalysisUS(requestId, currentAdmissionRequest);
    return updatedAdmissionRequest;
  }

  const platforms = currentAdmissionRequest.palenca.accounts.length;
  const mostRecent12Weeks = await retrieveMostRecent12WeeksEarnings(requestId);
  const totalEarnings = mostRecent12Weeks.reduce((acc, curr) => acc + curr.totalAmount, 0);
  const preQualification = determineEarningsAnalysisStatus(totalEarnings);

  const earningsAnalysis = new EarningsAnalysis({
    totalEarnings,
    status: preQualification,
    earnings: mostRecent12Weeks,
    platforms,
  });

  const updated = await repoSaveEarningsAnalysis(requestId, earningsAnalysis);
  await updateAdmissionRequestPalencaAccountStatus(requestId, preQualification);

  const request = await AdmissionRequestMongo.findById(requestId).lean<AdmissionRequest>();
  const hubspotId = request?.hubspot?.id;
  if (updated.earningsAnalysis?.status === EarningsAnalysisStatus.approved && hubspotId) {
    logger.info(
      `[calculateEarningsAnalysis] Sending message to phone ${request.personalData.phone}, with template ${HILOS_TEMPLATES.approved.templateId} and props ${currentAdmissionRequest._id}`
    );
    await sendMessage({
      props: currentAdmissionRequest._id,
      template: HILOS_TEMPLATES.approved.templateId,
      phone: request.personalData.phone,
    });

    try {
      await changeProperties({
        hubspotId,
        properties: { hs_lead_status: 'Pre-Calificado', rango_ingreso_promedio_semanal: '>$7,000' },
      });
    } catch (error) {
      logger.info(`[calculateEarningsAnalysis] Error while changing properties ${error}`);
    }

    let hubspotDeal: any;
    try {
      hubspotDeal = await createDeal({
        dealname: request.personalData.firstName + ' ' + request.personalData.lastName,
        ciudad_deal: request.personalData.city ?? '',
        dealstage: '75788126',
      });
    } catch (error) {
      logger.info(`[calculateEarningsAnalysis] Error while creating deal ${error}`);
    }

    await updateHilosContact({ hilosId: request.personalData.phone, requestId });

    if (hubspotDeal) {
      await batchDealAndLead({ dealId: hubspotDeal.id, hubspotId, requestId });
    }

    try {
      await AdmissionRequestMongo.findByIdAndUpdate(requestId, { hubspotDeal: hubspotDeal.dealId });
    } catch (error) {
      console.error(error);
      logger.error(`Error updating deal id in admission request ${requestId}`, error);
    }
  }

  if (updated.earningsAnalysis?.status === EarningsAnalysisStatus.approved_with_conditions && hubspotId) {
    logger.info(
      `[calculateEarningsAnalysis] Sending message to phone ${request.personalData.phone}, with template ${HILOS_TEMPLATES.approved.templateId} and props ${currentAdmissionRequest._id}`
    );
    await sendMessage({
      props: currentAdmissionRequest._id,
      template: HILOS_TEMPLATES.approved.templateId,
      phone: request.personalData.phone,
    });
    try {
      await changeProperties({
        hubspotId,
        properties: { hs_lead_status: 'Pre-Calificado', rango_ingreso_promedio_semanal: '$6,000 - $6,999' },
      });
    } catch (error) {
      logger.info(`[calculateEarningsAnalysis] Error while changing properties ${error}`);
    }

    let hubspotDeal: any;
    try {
      hubspotDeal = await createDeal({
        dealname: request.personalData.firstName + ' ' + request.personalData.lastName,
        ciudad_deal: request.personalData.city ?? '',
      });
    } catch (error) {
      logger.info(`[calculateEarningsAnalysis] Error while creating deal ${error}`);
    }

    if (hubspotDeal) {
      await batchDealAndLead({ dealId: hubspotDeal.id, hubspotId, requestId });
    }

    await updateHilosContact({ hilosId: request.personalData.phone, requestId });
  }

  if (updated.earningsAnalysis?.status === EarningsAnalysisStatus.rejected && hubspotId) {
    try {
      await changeProperties({
        hubspotId,
        properties: { hs_lead_status: 'UNQUALIFIED', rango_ingreso_promedio_semanal: '$5,000 - $5,999' },
      });
    } catch (error) {
      logger.info(`[calculateEarningsAnalysis] Error while changing properties ${error}`);
    }
  }

  return updated;
};

const earningsAnalysisUS = async (requestId: string, admissionRequest: AdmissionRequestMongoI) => {
  try {
    logger.info(`Starting US client earnings approval for request ${requestId}`);

    const vehicleSelected = admissionRequest.personalData.vehicleSelected;
    if (!vehicleSelected) {
      throw new Error('Admission request not found');
    }

    const platforms: string[] = [];
    admissionRequest.palenca.accounts.forEach((account) => {
      platforms.push(account.platform);
    });

    const mostRecent12Weeks = await retrieveMostRecent12WeeksEarnings(requestId);
    const totalEarnings = mostRecent12Weeks.reduce((acc, curr) => acc + curr.totalAmount, 0);
    const minEarningsApproved = getMinimumEarningsToApproveUS(vehicleSelected);

    const platformsMetrics = await MetricMongo.find({ requestId: requestId, platform: { $in: platforms } });
    const isDriverProfileApproved = determineProfileApprovalStatusUS(
      platformsMetrics,
      totalEarnings,
      minEarningsApproved
    );

    const earningsAnalysis = new EarningsAnalysis({
      totalEarnings: totalEarnings,
      status: isDriverProfileApproved.status,
      earnings: mostRecent12Weeks,
      platforms: admissionRequest.palenca.accounts.length,
    });

    const updated = await repoSaveEarningsAnalysis(requestId, earningsAnalysis);
    await updateAdmissionRequestPalencaAccountStatus(requestId, isDriverProfileApproved.status);
    logger.info(`[earningsAnalysisUS] US client earnings analysis updated for request ${requestId}`);

    return updated;
  } catch (err: any) {
    logger.error(
      `Error occured while calculating earnings analysis for US client for request ${requestId}: ${err?.message}`,
      err?.stack
    );
    throw new Error(`Error occured while determining US Driver profile approval for request ${requestId}`);
  }
};

const determineProfileApprovalStatusUS = (
  platformsMetrics: MetricMongoI[],
  totalEarnings: number,
  minEarningsApproved: number
) => {
  /**
   *
   * Business rules for US driver profile approval
   * No of rides > 800 (should be cumulative)
   * Average earnings per week > 2.5 weeks of the vehicle price
   *
   * // Old rules, deprecated, leaving this as reference, and removing the code.
   * Tenure > 1 year
   * Ratings 4.8 or greater
   */
  const MINIMUM_RIDES_TO_APPROVE_PROFILE = 800;
  let totalNoOfRides = 0;

  platformsMetrics.forEach((platform) => {
    totalNoOfRides += Number(platform.lifetimeTrips);
  });

  const totalEarningsRounded = Math.round(totalEarnings);
  const averageEarningsPerWeek = totalEarningsRounded / MIN_WEEKS_TO_ANALYZE;

  if (totalNoOfRides < MINIMUM_RIDES_TO_APPROVE_PROFILE) {
    return {
      status: EarningsAnalysisStatus.rejected,
      reason: 'No of rides is less than 800',
    };
  }

  if (averageEarningsPerWeek < minEarningsApproved) {
    return {
      status: EarningsAnalysisStatus.rejected,
      reason: 'Earnings are less than the minimum required',
    };
  }

  return {
    status: EarningsAnalysisStatus.approved,
    reason: 'Driver profile is approved',
  };
};

enum USVehicles {
  'Hyundai Ioniq5 Long Range' = 'hyundai-ioniq5',
  'Genesis GV60 Long Range' = 'genesis-gv60',
  '2025 Hyundai Kona Electric' = 'hyundai-kona',
  'GM Equinox' = 'gm-equinox',
}
const vehiclePricesUS = Object.freeze({
  [USVehicles['Hyundai Ioniq5 Long Range']]: 2162,
  [USVehicles['Genesis GV60 Long Range']]: 2379,
  [USVehicles['2025 Hyundai Kona Electric']]: 1729,
  [USVehicles['GM Equinox']]: 1859,
});
// US pricing: Provided by Ammar
// - Ioniq 5: 2162 USD per month
// - Kona: 1729 USD per month
// - GV 60: 2379 USD per month
// - GM Equinox: 1859 USD per month

const getMinimumEarningsToApproveUS = (vehicleSelected: string): number => {
  const selectedVehiclePrice = vehiclePricesUS[USVehicles[vehicleSelected as keyof typeof USVehicles]];
  if (!selectedVehiclePrice) {
    throw new Error(`Vehicle ${vehicleSelected} is not supported for earnings analysis`);
  }
  const WEEKS_IN_A_MONTH = 4; // Average weeks in a month
  const TWO_POINT_FIVE = 2.5; // 2.5 weeks in a month
  const minEarningsApproved = (selectedVehiclePrice / WEEKS_IN_A_MONTH) * TWO_POINT_FIVE;
  return minEarningsApproved;
};
