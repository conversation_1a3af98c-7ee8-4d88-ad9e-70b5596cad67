// import { GigPlatform, PalencaRetrievalStatus } from '../../constants';
// import { AdmissionRequestMongo } from '../../models/admissionRequestSchema';

// export const checkAndCalculateMetrics = async (requestId: string) => {
//   const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

//   // If the request is has approved or rejected status we do nothing
//   if (
//     currentAdmissionRequest.status === AdmissionRequestStatus.approved ||
//     currentAdmissionRequest.status === AdmissionRequestStatus.rejected
//   ) {
//     return currentAdmissionRequest;
//   }

//   // Make sure we retreived all the metrics for each account before calculating the metrics analysis
//   const palencaAccounts = currentAdmissionRequest.palenca.accounts;

//   const metricsRetrievalStatus = palencaAccounts.every(
//     (account) => account.metrics.status === PalencaRetrievalStatus.success
//   );

//   if (!metricsRetrievalStatus) {
//     return currentAdmissionRequest;
//   }

//   // Calculate and save the metrics as RiskAnalysisData
//   await calculateMetricsAnalysis(requestId);

//   return currentAdmissionRequest;
// };

// export const retrievePalencaMetrics = async ({
//   accountId,
//   platform,
//   requestId,
// }: {
//   accountId: string;
//   platform: GigPlatform;
//   requestId: string;
// }): Promise<null> => {
//   const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);
//   if (!currentAdmissionRequest) return null;

//   const palencaAccount = currentAdmissionRequest.palenca.accounts.find(
//     (account) => account.accountId === accountId && account.platform === platform
//   );

//   if (!palencaAccount) {
//     return null;
//   }

//   // If the account has already been retrieved we do nothing
//   if (palencaAccount.metrics.status === PalencaRetrievalStatus.success) return null;

//   try {
//     await checkAndCalculateMetrics(requestId);
//   } catch (error) {
//     console.error(error);
//   }

//   return null;
// };
