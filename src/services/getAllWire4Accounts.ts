import axios from 'axios';
import { WIRE4_API_URL, WIRE4_SUCRIPTION } from '../constants';
import { getWire4Token } from './getWire4Token';

export default async function getAllWire4Accounts(userData: String) {
  const token = await getWire4Token();
  const { data } = await axios(`${WIRE4_API_URL}/subscriptions/${WIRE4_SUCRIPTION}/depositants`, {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  const depositantClabe = data.depositants
    .filter((depositant: any) => depositant.depositant_clabe === userData)
    .map((depositant: any) => depositant.email);
  if (!depositantClabe.length) return null;
  return depositantClabe.length > 0 ? depositantClabe[0] : null;
}
