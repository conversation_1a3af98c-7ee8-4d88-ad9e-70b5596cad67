import { getUrlSingleFile } from '../aws/s3';

export async function assignImageUrlsToObject(
  object: { [x: string]: any } | null | undefined,
  properties: string[]
) {
  if (object) {
    const updatedObject = { ...object };
    const promises = properties.map(async (property: string) => {
      if (object[property]) {
        const url = await getUrlSingleFile(object[property]);
        return { property, url };
      }
      return null;
    });
    const resolvedPromises = await Promise.all(promises);

    resolvedPromises.forEach((resolvedProperty) => {
      if (resolvedProperty) {
        const { property, url } = resolvedProperty;
        updatedObject[property] = url;
      }
    });
    return updatedObject;
  }
  return null;
}

// Esta función mapea arrays y reasigna la ruta a urls, ejemplo:
/* 
  tenency: [
    {
      payment: 3004,
      validity: 24/08/2027,
      tenencyDocument: 'stock/{vehicleId}/{doc}', <-- aqui al consultar la base de datos retorna la ruta
    },
  ],

  y esto devuelve: 

  tenency: [
    {
      payment: 3004,
      validity: 24/08/2027,
      tenencyDocument: 'https://aws.imagen.com/...', <-- y retorna la url en la propiedad de cada objeto
    },
  ],
*/

// export async function mapAndAssingUrlsToObjectsOfArray<T, K extends keyof T>(
//   array: T[],
//   getUrlFunction: (path: string) => Promise<string>,
//   propertyName: string
// ): Promise<(T & Record<K, string>)[]> {
//   const promises = array.map(async (element) => {
//     if (element[propertyName as K]) {
//       const url = await getUrlFunction(element[propertyName as K] as string);
//       return {
//         ...element,
//         [propertyName]: url,
//       };
//     }
//     return null;
//   });

//   return Promise.all(promises) as Promise<(T & Record<K, string>)[]>;
// }

export async function mapAndAssignUrlsToObjectsOfArray<T, K extends keyof T>(
  array: T[],
  propertyNames: string[]
): Promise<(T & Record<K, string>)[]> {
  if (array) {
    const updatedArray = [...array];

    const promises = updatedArray.map(async (element) => {
      const updatedElement: { [key: string]: string } = { ...element } as any;

      for (const propertyName of propertyNames) {
        if (element[propertyName as keyof T]) {
          const url = await getUrlSingleFile(element[propertyName as keyof T] as string);
          updatedElement[propertyName as string] = url;
        }
      }

      return updatedElement;
    });

    return Promise.all(promises) as Promise<(T & Record<K, string>)[]>;
  }
  return [];
}
