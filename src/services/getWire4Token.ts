import {
  WIRE4_TOKEN_URL,
  WIRE4_CLIENT_ID,
  WIRE4_I80_CLIENT_ID,
  WIRE4_CLIENT_SECRET,
  WIRE4_I80_CLIENT_SECRET,
  WIRE4_API_USERNAME,
  WIRE4_OLD_API_USERNAME,
  WIRE4_I80_API_PASSWORD,
  WIRE4_API_PASSWORD,
  WIRE4_OLD_API_PASSWORD,
  WIRE4_I80_API_USERNAME,
  wire4Data,
} from '../constants';
export function getWire4Token(scope: string = wire4Data.token.scope, type: string = wire4Data.token.type) {
  const Authorization = `Basic ${Buffer.from(`${WIRE4_CLIENT_ID}:${WIRE4_CLIENT_SECRET}`).toString(
    'base64'
  )}`;

  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    Authorization,
  };
  const body = new URLSearchParams();
  if (type === 'AccesToken') {
    body.append('grant_type', 'password');
    body.append('username', WIRE4_API_USERNAME);
    body.append('password', WIRE4_API_PASSWORD);
  } else {
    body.append('grant_type', 'client_credentials');
  }
  body.append('scope', scope);
  return fetch(WIRE4_TOKEN_URL, {
    method: 'POST',
    headers,
    body,
  })
    .then((response) => response.json())
    .then((response) => response.access_token)
    .catch((error) => console.error(error));
}

export function oldGetWire4Token(scope: string = wire4Data.token.scope, type: string = wire4Data.token.type) {
  const Authorization = `Basic ${Buffer.from(`${WIRE4_CLIENT_ID}:${WIRE4_CLIENT_SECRET}`).toString(
    'base64'
  )}`;

  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    Authorization,
  };
  const body = new URLSearchParams();
  if (type === 'AccesToken') {
    body.append('grant_type', 'password');
    body.append('username', WIRE4_OLD_API_USERNAME);
    body.append('password', WIRE4_OLD_API_PASSWORD);
  } else {
    body.append('grant_type', 'client_credentials');
  }
  body.append('scope', scope);
  return fetch(WIRE4_TOKEN_URL, {
    method: 'POST',
    headers,
    body,
  })
    .then((response) => response.json())
    .then((response) => response.access_token)
    .catch((error) => console.error(error));
}

export function i80GetWire4Token(scope: string = wire4Data.token.scope, type: string = wire4Data.token.type) {
  const Authorization = `Basic ${Buffer.from(`${WIRE4_I80_CLIENT_ID}:${WIRE4_I80_CLIENT_SECRET}`).toString(
    'base64'
  )}`;

  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    Authorization,
  };
  const body = new URLSearchParams();
  if (type === 'AccesToken') {
    body.append('grant_type', 'password');
    body.append('username', WIRE4_I80_API_USERNAME);
    body.append('password', WIRE4_I80_API_PASSWORD);
  } else {
    body.append('grant_type', 'client_credentials');
  }
  body.append('scope', scope);
  return fetch(WIRE4_TOKEN_URL, {
    method: 'POST',
    headers,
    body,
  })
    .then((response) => response.json())
    .then((response) => response.access_token)
    .catch((error) => console.error(error));
}
