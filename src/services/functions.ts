export function generateId(length: number) {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

export function capitalizeWords(text: string) {
  // Dividir el texto en palabras separadas por espacios
  const words = text.split(' ');

  // Iterar sobre cada palabra y capitalizar la primera letra
  const capitalizedWords = words.map((word) => {
    // Obtener la primera letra en mayúscula y concatenarla con el resto de la palabra en minúscula
    const capitalized = word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    return capitalized;
  });

  // Unir las palabras capitalizadas en un solo texto
  const capitalizedText = capitalizedWords.join(' ');

  return capitalizedText;
}
