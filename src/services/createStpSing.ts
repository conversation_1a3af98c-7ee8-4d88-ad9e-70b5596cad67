import crypto from 'crypto';
import { STP_PASSPHRASE, STP_PRIVATE_KEY } from '../constants';
import StpSign from '../models/stpSign';

export async function getSign(originalString: string, user: string, type: string) {
  const sign = crypto.createSign('RSA-SHA256');
  sign.update(originalString);
  sign.end();
  const key = Buffer.from(STP_PRIVATE_KEY, 'base64').toString('ascii');
  const signatureB64 = sign.sign({ key, passphrase: STP_PASSPHRASE }, 'base64');
  try {
    await StpSign.create({ originalString, signatureB64, user, type });
  } catch (error) {
    console.error(error);
  }
  return signatureB64;
}
