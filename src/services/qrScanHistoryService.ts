import { Types } from 'mongoose';
import QRScanHistory from '../models/qrScanHistorySchema';
import { PhysicalVehicleStatus } from '../models/StockVehicleSchema';
import { logger } from '@/clean/lib/logger';
import { QRScanHistoryActionType } from '../models/qrScanHistorySchema';

/**
 * Creates a new QR scan history entry QRScanHistoryActionType
 */

export const createQRScanHistoryEntry = async ({
  vehicleId,
  userId,
  statusChangedFrom,
  statusChangedTo,
  deviceInfo,
  actionToken,
  actionType,
  notes,
  location,
}: {
  vehicleId: Types.ObjectId;
  userId: Types.ObjectId;
  statusChangedFrom: PhysicalVehicleStatus;
  statusChangedTo: PhysicalVehicleStatus;
  deviceInfo?: string;
  actionToken?: string;
  actionType: QRScanHistoryActionType;
  notes?: string;
  location?: string;
}) => {
  try {
    const qrScanHistory = new QRScanHistory({
      vehicleId,
      userId,
      scanTime: new Date(),
      statusChangedFrom,
      statusChangedTo,
      deviceInfo,
      actionToken,
      actionType,
      notes,
      location,
    });

    await qrScanHistory.save();
    logger.info(`[createQRScanHistoryEntry] Created new QR scan history entry for vehicle ${vehicleId}`);
    return qrScanHistory;
  } catch (error) {
    logger.error(
      `[createQRScanHistoryEntry] Error creating QR scan history: ${error instanceof Error ? error.message : error}`,
      error
    );
    throw error;
  }
};

/**
 * Gets QR scan history for a vehicle
 */
export const getQRScanHistoryForVehicle = async (vehicleId: Types.ObjectId) => {
  try {
    // Get QR scan history and populate user information
    const history = await QRScanHistory.find({ vehicleId })
      .sort({ scanTime: -1 }) // Latest first
      .populate('userId', 'name email')
      .lean();

    return history;
  } catch (error) {
    logger.error(
      `Error getting QR scan history for vehicle: ${error instanceof Error ? error.message : error}`,
      error
    );
    return [];
  }
};

/**
 * Gets recent QR scan activity across all vehicles
 */
export const getRecentQRScanActivity = async (limit = 50) => {
  try {
    const recentActivity = await QRScanHistory.find()
      .sort({ scanTime: -1 })
      .limit(limit)
      .populate('userId', 'firstName lastName email')
      .populate('vehicleId', 'carNumber brand model')
      .lean();

    logger.info(`[getRecentQRScanActivity] Retrieved ${recentActivity.length} recent QR scan activities`);
    return recentActivity;
  } catch (error) {
    logger.error(
      `[getRecentQRScanActivity] Error fetching recent QR scan activity: ${error instanceof Error ? error.message : error}`,
      error
    );
    throw error;
  }
};

/**
 * Gets statistics on QR scans
 */
export const getQRScanStats = async () => {
  try {
    const totalScans = await QRScanHistory.countDocuments();
    const statusChanges = await QRScanHistory.countDocuments({
      actionType: QRScanHistoryActionType.STATUS_CHANGE,
    });
    const scansByDay = await QRScanHistory.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$scanTime' },
            month: { $month: '$scanTime' },
            day: { $dayOfMonth: '$scanTime' },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.year': -1, '_id.month': -1, '_id.day': -1 } },
      { $limit: 30 },
    ]);

    return {
      totalScans,
      statusChanges,
      scansPerDay: scansByDay.map((item) => ({
        date: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
        count: item.count,
      })),
    };
  } catch (error) {
    logger.error(
      `[getQRScanStats] Error fetching QR scan statistics: ${error instanceof Error ? error.message : error}`,
      error
    );
    throw error;
  }
};
