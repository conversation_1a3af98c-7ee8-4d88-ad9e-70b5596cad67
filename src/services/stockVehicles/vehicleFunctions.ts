import { Types } from 'mongoose';
import {
  IStockVehicle,
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleState,
  VehicleSubCategory,
} from '../../models/StockVehicleSchema';
import { deleteFileFromS3 } from '@/aws/s3';
import Document from '@/models/documentSchema';
import { getCurrentDateTime } from '../timestamps';
import { steps } from '@/constants';
import { logger } from '@/clean/lib/logger';

export const deleteDocument = async (docId: Types.ObjectId) => {
  const document = await Document.findById(docId);
  if (document) {
    await deleteFileFromS3(document.path);
    await document.remove();
    logger.info(`Document with ID ${docId} deleted successfully.`);
  } else {
    logger.warn(`Document with ID ${docId} not found.`);
  }
};

// CHECK CIRCULATION BACK FOR CDMX VEHICLES AND NOT OTHERS
export const checkAndUpdateVehicleStatus = (stockVehicle: IStockVehicle, userId: string) => {
  let hasAllDocs =
    Boolean(stockVehicle.carPlates?.plates) &&
    Boolean(stockVehicle.circulationCard?.number) && // Check for number instead of validity
    stockVehicle.gpsInstalled &&
    Boolean(stockVehicle.tenancy.length > 0) &&
    Boolean(stockVehicle.policiesArray.length > 0);

  // if the region of vehicle is CDMX, check if the circulation card has a back and front.
  if (stockVehicle.vehicleState === VehicleState.cdmx) {
    hasAllDocs =
      hasAllDocs &&
      Boolean(stockVehicle.circulationCard?.backImg) &&
      Boolean(stockVehicle.circulationCard?.frontImg);
  }

  const isPolicyValid =
    stockVehicle.policiesArray?.length > 0
      ? new Date(stockVehicle.policiesArray[stockVehicle.policiesArray.length - 1]?.validity) > new Date()
      : false;

  const isTenancyValid =
    stockVehicle.tenancy?.length > 0
      ? new Date(stockVehicle.tenancy[stockVehicle.tenancy.length - 1]?.validity) > new Date()
      : false;

  const isCirculationValid =
    stockVehicle.circulationCard && stockVehicle.circulationCard?.validity
      ? new Date(stockVehicle.circulationCard.validity) > new Date()
      : true; // If there's no validity, consider it valid

  if (hasAllDocs && isPolicyValid && isTenancyValid && isCirculationValid) {
    stockVehicle.vehicleDocsComplete = true;
    logger.info(`Vehicle vin: ${stockVehicle.vin} documents are complete.`);
  } else {
    stockVehicle.vehicleDocsComplete = false;
    logger.info(`Vehicle vin: ${stockVehicle.vin} documents are incomplete.`);
  }

  if (
    stockVehicle.step &&
    stockVehicle.step.stepName === steps.stock.name &&
    stockVehicle.receptionDate &&
    stockVehicle.vehicleDocsComplete
  ) {
    stockVehicle.updateHistory.push({
      userId: new Types.ObjectId(userId),
      step: 'Vehiculo enviado a stock',
      description: '',
      time: getCurrentDateTime(),
    });
    stockVehicle.step.stepName = steps.vehicleReady.name;
    stockVehicle.step.stepNumber = steps.vehicleReady.number;
    stockVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    stockVehicle.category = VehicleCategory.stock;
    stockVehicle.subCategory = VehicleSubCategory.default;
    logger.info(`Vehicle vin: {stockVehicle.vin} status updated to stock.`);
  }
};

export const deleteMostRecentPolicy = async (stockVehicle: IStockVehicle) => {
  if (stockVehicle.policiesArray.length > 0) {
    const mostRecentPolicy = stockVehicle.policiesArray[stockVehicle.policiesArray.length - 1];

    if (mostRecentPolicy && new Date(mostRecentPolicy.validity) > new Date()) {
      stockVehicle.policiesArray.pop();
      if (mostRecentPolicy.policyDocument) {
        await deleteDocument(mostRecentPolicy.policyDocument);
        logger.info(`Most recent policy document for vehicle vin: ${stockVehicle.vin} deleted.`);
      }
    } else {
      throw new Error('No se puede eliminar una póliza expirada.');
    }
    if (stockVehicle.policiesArray?.length > 0) {
      const newTopPolicy = stockVehicle.policiesArray[stockVehicle.policiesArray.length - 1];
      if (new Date(newTopPolicy?.validity) < new Date()) {
        stockVehicle.vehicleDocsComplete = false;
      }
    } else {
      stockVehicle.vehicleDocsComplete = false;
    }
  }
};

export const deleteMostRecentTenancy = async (stockVehicle: IStockVehicle) => {
  if (stockVehicle.tenancy.length > 0) {
    const mostRecentTenancy = stockVehicle.tenancy[stockVehicle.tenancy.length - 1];

    if (mostRecentTenancy && new Date(mostRecentTenancy.validity) > new Date()) {
      stockVehicle.tenancy.pop();
      if (mostRecentTenancy.tenancyDocument) {
        await deleteDocument(mostRecentTenancy.tenancyDocument);
        logger.info(`Most recent tenancy document for vehicle vin: ${stockVehicle.vin} deleted.`);
      }
    } else {
      throw new Error('No se puede eliminar una tenencia expirada.');
    }
    if (stockVehicle.tenancy.length > 0) {
      const newTopTenancy = stockVehicle.tenancy[stockVehicle.tenancy.length - 1];
      if (new Date(newTopTenancy?.validity) < new Date()) {
        stockVehicle.vehicleDocsComplete = false;
      }
    } else {
      stockVehicle.vehicleDocsComplete = false;
    }
  }
};
