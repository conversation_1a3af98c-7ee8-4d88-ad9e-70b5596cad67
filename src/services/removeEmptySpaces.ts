const validChars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
/**
 * Returns the original name file without empty spaces between words
 */

export const removeEmptySpacesNameFile = (document: Express.Multer.File | undefined, length = 5) => {
  let removeSpacesNamefile = '';
  if (document) {
    // Genera un UUID de 6 caracteres aleatorios (excluyendo dígitos numéricos)
    const randomCharPosition = Math.floor(Math.random() * 52);
    const uuid = `${validChars[randomCharPosition]}${Array.from(
      { length },
      () => validChars[Math.floor(Math.random() * validChars.length)]
    ).join('')}`;

    // Obtener el nombre original del archivo sin espacios
    const fileExtension = document.originalname.split('.').slice(-1)[0];
    const originalNameWithoutSpaces = document.originalname.replace(/\s/g, '').split(`.${fileExtension}`)[0];

    // Concatenar el nombre original y el UUID
    removeSpacesNamefile = `${originalNameWithoutSpaces}${uuid}.${fileExtension}`;
  }
  return removeSpacesNamefile;
};

export const removeEmptySpacesNameFileV2 = (nameFile: string, length = 5) => {
  let removeSpacesNamefile = '';
  if (nameFile) {
    // Genera un UUID de 6 caracteres aleatorios (excluyendo dígitos numéricos)
    const randomCharPosition = Math.floor(Math.random() * 52);
    const uuid = `${validChars[randomCharPosition]}${Array.from(
      { length },
      () => validChars[Math.floor(Math.random() * validChars.length)]
    ).join('')}`;

    // Obtener el nombre original del archivo sin espacios
    const fileExtension = nameFile.split('.').slice(-1)[0];
    const originalNameWithoutSpaces = nameFile.replace(/\s/g, '').split(`.${fileExtension}`)[0];

    // Concatenar el nombre original y el UUID
    removeSpacesNamefile = `${originalNameWithoutSpaces}${uuid}.${fileExtension}`;
  }

  return removeSpacesNamefile;
};

export const removeEmptySpacesName = (name: string) => {
  let removeSpacesName = '';
  if (name) {
    removeSpacesName = name.split(' ').join('');
  }
  return removeSpacesName;
};
