export const multerDocs = [
  {
    name: 'ineFront',
    maxCount: 1,
  },
  {
    name: 'ineBack',
    maxCount: 1,
  },
  {
    name: 'curpDoc',
    maxCount: 1,
  },
  {
    name: 'taxStatus',
    maxCount: 1,
  },
  {
    name: 'addressVerification',
    maxCount: 1,
  },
  {
    name: 'bankStatementsOne',
    maxCount: 1,
  },
  {
    name: 'bankStatementsTwo',
    maxCount: 1,
  },
  {
    name: 'bankStatementsThree',
    maxCount: 1,
  },
  {
    name: 'bankStatementsFour',
    maxCount: 1,
  },
  {
    name: 'bankStatementsFive',
    maxCount: 1,
  },
  {
    name: 'bankStatementsSix',
    maxCount: 1,
  },
  {
    name: 'driverLicenseFront',
    maxCount: 1,
  },
  {
    name: 'driverLicenseBack',
    maxCount: 1,
  },
  {
    name: 'garage',
    maxCount: 1,
  },
  {
    name: 'picture',
    maxCount: 1,
  },
  // {
  //   name: 'avalData[ine]',
  //   maxCount: 1,
  // },
  {
    name: 'avalINE',
    maxCount: 1,
  },
  {
    name: 'proofOfCompletionOfAnyRequiredSafetyCourses',
    maxCount: 1,
  },
  {
    name: 'drivingRecord',
    maxCount: 1,
  },
  {
    name: 'rideShareDates',
    maxCount: 1,
  },
  {
    name: 'rideShareRideHistory',
    maxCount: 1,
  },
  {
    name: 'avgWeeklyIncomeOfLastTwelveWeeks',
    maxCount: 12,
  },
  {
    name: 'signature',
    maxCount: 1,
  },
];

export const ineDocs = [
  {
    name: 'ineFront',
    maxCount: 1,
  },
  {
    name: 'ineBack',
    maxCount: 1,
  },
];

export const driverLicense = [
  {
    name: 'driverLicenseFront',
    maxCount: 1,
  },
  {
    name: 'driverLicenseBack',
    maxCount: 1,
  },
];

export const bankStatementsDocs = [
  {
    name: 'bankStatementOne',
    maxCount: 1,
  },
  {
    name: 'bankStatementTwo',
    maxCount: 1,
  },
  {
    name: 'bankStatementThree',
    maxCount: 1,
  },
  {
    name: 'bankStatementFour',
    maxCount: 1,
  },
  {
    name: 'bankStatementFive',
    maxCount: 1,
  },
  {
    name: 'bankStatementSix',
    maxCount: 1,
  },
];

export const contractDocs = [
  {
    name: 'deliveredImages[]',
    maxCount: Infinity,
  },
  {
    name: 'contract',
    maxCount: 1,
  },
  {
    name: 'promissoryNote',
    maxCount: 1,
  },
  {
    name: 'deliveryReceipt',
    maxCount: 1,
  },
  {
    name: 'warranty',
    maxCount: 1,
  },
  {
    name: 'invoice',
    maxCount: 1,
  },
  {
    name: 'privacy',
    maxCount: 1,
  },
  {
    name: 'contactInfo',
    maxCount: 1,
  },
];
