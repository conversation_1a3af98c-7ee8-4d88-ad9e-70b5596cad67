import fs from 'fs';
import multer from 'multer';

const uploadDirectory = 'uploads';

// Verificar la existencia del directorio y crearlo si no existe
if (!fs.existsSync(uploadDirectory)) {
  fs.mkdirSync(uploadDirectory);
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Define la carpeta de destino donde se guardarán los archivos
    cb(null, uploadDirectory);
  },
  filename: (req, file, cb) => {
    // Define el nombre del archivo
    const removeSpacesNamefile = file.originalname.split(' ').join('');
    const randomTwoDigits = Math.floor(Math.random() * 90) + 10; // Genera un número aleatorio de dos dígitos (10-99)
    const finalFileName = `${Date.now()}-${randomTwoDigits}-${removeSpacesNamefile}`;
    // console.log('MULTER', finalFileName);
    cb(null, finalFileName);
  },
});

const fileFilter = (req: any, file: any, cb: any) => {
  // Filtrar los archivos que se aceptarán
  const allowedMimeTypes = [
    'image/jpeg',
    'image/png',
    'image/webp',
    'application/pdf',
    'video/mp4',
    'video/quicktime',
    'video/mkv',
    'video/webp',
    'video/ogg',
    'video/flv',
    'video/webm',
    'video/mpeg-4',
    'video/mpeg-2',
    'video/mkv',
    'video/avi',
    'video/wmv',
    'video/amv',
    'video/mov',
    'video/3gp',
    'text/csv',
    'application/xml',
    'text/xml',
  ];
  if (allowedMimeTypes.includes(file.mimetype)) {
    // Aceptar un archivo
    cb(null, true);
  } else {
    // Rechazar un archivo
    cb(null, false);
  }
};

const inMemoryStorage = multer.memoryStorage();

export const upload = multer({ storage, fileFilter });
export const uploadInMemory = multer({ storage: inMemoryStorage, fileFilter });
