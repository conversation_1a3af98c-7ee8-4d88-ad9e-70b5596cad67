import app from './app';
import { PORT } from './constants/index';
import initScheduleJobs from './cron';
import { connectDB } from './database/index';
import './global';
import * as dotenv from 'dotenv';
dotenv.config();

const root = process.cwd();

dotenv.config({ path: `${root}/.env.${process.env.NODE_ENV || 'development'}` });

app.listen(PORT, async () => {
  await connectDB();
  console.log(`Server running on port http://localhost:${PORT}`);
  initScheduleJobs();
});
