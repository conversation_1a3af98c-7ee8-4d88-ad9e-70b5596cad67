import { steps } from '../../../constants';
import StockVehicle from '../../../models/StockVehicleSchema';
import AssociatePayments from '../../../models/associatePayments';
import Associate from '../../../models/associateSchema';
import Document from '../../../models/documentSchema';
import MainContractSchema from '../../../models/mainContractSchema';
import StartPayFlow from '../../../models/start-pay-flow';
import { deleteWeetrustDocument } from '../../Associate/services/weetrust';

export const returnToDriverAssigned = async (stockId: string) => {
  const stockVehicle = await StockVehicle.findById(stockId);
  if (!stockVehicle) {
    throw new Error('Stock not found');
  }

  // this validation, if it's not delivered, it should not be returned to contract generated
  if (stockVehicle.step.stepNumber < steps.contractCreated.number) {
    throw new Error('Stock not in the correct step');
  }

  stockVehicle.step.stepName = steps.driverAssigned.name;
  stockVehicle.step.stepNumber = steps.driverAssigned.number;

  const contractNumber = stockVehicle.extensionCarNumber
    ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
    : stockVehicle.carNumber;

  const drivers = stockVehicle.drivers;
  const associate = await Associate.findById(drivers[drivers.length - 1]._id).select('+digitalSignature');

  if (!associate) {
    throw new Error('Associate not found');
  }

  const unSignedDocId = associate.unSignedContractDoc;

  if (unSignedDocId) {
    const unsignedDoc = await Document.findById(unSignedDocId);

    if (unsignedDoc) {
      Associate.updateOne({ _id: associate._id }, { $unset: { unSignedContractDoc: 1 } });
      await unsignedDoc.remove();
    }
  }

  stockVehicle.deliveredDate.pop();

  const mainContract = await MainContractSchema.findOne({
    $or: [
      {
        stockId: stockVehicle._id,
        associatedId: associate._id,
      },
      {
        contractNumber,
        stockId: stockVehicle._id,
      },
    ],
  });

  if (mainContract) {
    await mainContract.remove();
  } else {
    await MainContractSchema.deleteOne({
      contractNumber,
      stockId: stockVehicle._id,
    });
  }

  const associatePayments = await AssociatePayments.findOne({
    $or: [
      {
        associateId: associate._id,
        vehiclesId: stockVehicle._id,
      },
      {
        monexEmail: associate.email,
        vehiclesId: stockVehicle._id,
      },
      {
        monexEmail: associate.email,
      },
    ],
  });

  await StartPayFlow.findOneAndDelete({
    associateId: associate._id,
    stockId: stockVehicle._id,
  });

  if (associatePayments) {
    await associatePayments.remove();
  }

  if (associate.digitalSignature) {
    if (associate.digitalSignature.documentID) {
      try {
        await deleteWeetrustDocument(undefined, associate.digitalSignature.documentID);
      } catch (error: any) {
        console.error('Error deleting document from Weetrust', error.message);
      }
    }

    delete associate.digitalSignature.documentID;
    delete associate.digitalSignature.url;

    associate.digitalSignature.signed = false;
    associate.digitalSignature.isSent = false;
    await associate.save();
  }

  await stockVehicle.save();

  return { message: 'Vehiculo regresado a Conductor asignado' };
};
