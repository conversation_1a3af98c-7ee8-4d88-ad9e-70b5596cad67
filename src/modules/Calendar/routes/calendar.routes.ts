import { Router } from 'express';
import {
  createSchedule,
  getSchedule,
  getAppointment,
  getAppointmentsByUserId,
  statusChangeAppointment,
  getUsersWithSameAvailableSlot,
  homeVisitorChangeApointment,
} from '../controllers/calendar.controller';
import { errorHandler } from '@/clean/errors/express';

const calendarRouter = Router();

calendarRouter.get('/calendar/schedule/:userId', getSchedule);
calendarRouter.put('/calendar/schedule/:userId', createSchedule);
calendarRouter.get('/calendar/events/:userId', getAppointmentsByUserId);
calendarRouter.patch('/calendar/event/status', errorHandler(statusChangeAppointment));
calendarRouter.get('/calendar/events/application/:admissionRequestId', errorHandler(getAppointment));
calendarRouter.get(
  '/calendar/users-with-same-available-slot/:slotId',
  errorHandler(getUsersWithSameAvailableSlot)
);
calendarRouter.patch('/calendar/event/home-visitor-change', errorHandler(homeVisitorChangeApointment));

export default calendarRouter;
