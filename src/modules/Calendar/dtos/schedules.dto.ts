import z from 'zod';

export const createScheduleDto = z.object({
  name: z.string().trim(),
  weeklySchedule: z.record(
    z.object({
      start: z.string().trim(),
      end: z.string().trim(),
    })
  ),
  timezone: z.string().trim(),
  breakTimes: z.array(
    z.object({
      start: z.string().trim(),
      end: z.string().trim(),
    })
  ),
  bufferTime: z.number(),
  duration: z.number(),
  maxAdvanceBookingDays: z.number(),
  minBookingNoticeHours: z.number(),

  // below array will be like this structure, but can be empty
  // { startDate: '2025-02-26', endDate: '2025-02-26', startTime: '09:30am', endTime: '10:00am' }

  blockSlots: z
    .array(
      z.object({
        startDate: z.string().trim(),
        endDate: z.string().trim(),
        startTime: z.string().trim(),
        endTime: z.string().trim(),
      })
    )
    .optional(),
  addedSlots: z
    .array(
      z.object({
        startDate: z.string().trim(),
        endDate: z.string().trim(),
        startTime: z.string().trim(),
        endTime: z.string().trim(),
      })
    )
    .optional(),
});

export const updateScheduleDto = z.object({
  name: z.string().trim(),
  weeklySchedule: z.record(
    z.object({
      start: z.string().trim(),
      end: z.string().trim(),
    })
  ),
  appointmentDuration: z.number(),
  maxSimultaneousAppointments: z.number(),
  timezone: z.string().trim(),
  breakTimes: z.array(
    z.object({
      start: z.string().trim(),
      end: z.string().trim(),
    })
  ),
  bufferTime: z.number(),
  user: z.string().trim(),
});
