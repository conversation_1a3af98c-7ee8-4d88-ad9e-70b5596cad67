import { logger } from '../../../clean/lib/logger';
import { genericMessages } from '../../../constants';
import Associate from '../../../models/associateSchema';
// import MainContractSchema from '../../../models/mainContractSchema';
import StockVehicle from '../../../models/StockVehicleSchema';
import { getCurrentDateTime } from '../../../services/timestamps';
import { AsyncController } from '../../../types&interfaces/types';
import { EMAIL_OCN, fixAddendumSignature, sendAddendumSignatory } from '../services/fix-signature-adendum';
import { sendAdendumWhatsapp, sendAdendumWhatsappOCN } from '../services/send-whatsapp-messages';
import getWeetrustToken, { deleteWeetrustDocument, sendDocumentToWeetrust } from '../services/weetrust';
import fs from 'fs';

export const getSigntature: AsyncController = async (req, res) => {
  try {
    const { associateId } = req.body;

    const associate = await Associate.findById(associateId).select('+digitalSignature');

    if (!associate) return res.status(404).json({ message: 'Associate not found' });

    return res.status(200).send({
      message: 'Associate and signature found',
      associate,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Something went wrong' });
  }
};

export const postSignature: AsyncController = async (req, res) => {
  try {
    const { associateId, documentID, stockId, participants } = req.body;
    // console.log('participants', participants);

    if (!associateId || !documentID) {
      return res.status(400).json({ message: 'Missing fields' });
    }

    const associate = await Associate.findById(associateId).select('+digitalSignature');

    if (!associate) return res.status(404).json({ message: 'Associate not found' });

    if (associate.digitalSignature.documentID) {
      await deleteWeetrustDocument(undefined, associate.digitalSignature.documentID);
    }

    associate.digitalSignature.documentID = documentID;
    associate.digitalSignature.url = '';
    associate.digitalSignature.isSent = true;
    associate.digitalSignature.participants = participants;

    await associate.save();

    const stockVehicle = await StockVehicle.findById(stockId);

    if (stockVehicle) {
      stockVehicle.updateHistory.push({
        step: 'Contrato enviado a firma',
        time: getCurrentDateTime(),
        userId: req.userId.userId,
      });

      await stockVehicle.save();
    }

    return res.status(200).json({ message: 'Signature saved' });
  } catch (error) {
    return res.status(500).json({ message: 'Something went wrong' });
  }
};

export const sendAddendumToWeetrust: AsyncController = async (req, res) => {
  const { isEdit, hasFee } = req.body;

  const { associateId, vehicleId } = req.body;
  const associate = await Associate.findById(associateId).select(
    '+digitalSignature +adendumDigitalSignature email firstName lastName phone'
  );

  if (!associate) return res.status(404).json({ message: 'Associate not found' });

  const file = req.file;

  if (!file) return res.status(400).json({ message: 'Missing file' });
  let DocumentID;
  try {
    const sendingOrEditing = isEdit ? 'Editing' : 'Sending';

    logger.info(
      `[sendAddendumToWeetrust] ${sendingOrEditing} adendum to Weetrust for associate ${associateId}`
    );

    const token = await getWeetrustToken();

    const data = await sendDocumentToWeetrust({ file, token });
    const { documentID }: { documentID: string } = data.responseData;

    DocumentID = documentID;
    const fixedSignature = await fixAddendumSignature({
      associateEmail: associate.email,
      hasFee,
      documentID,
      token,
    });

    // console.log('fixedSignature', fixedSignature);
    if (!fixedSignature) {
      return res.status(500).send({ message: 'Error fixing signature' });
    }

    const signatoryResponse = await sendAddendumSignatory(documentID, token, {
      associate: { name: `${associate.firstName} ${associate.lastName}`, email: associate.email },
    });

    if (!signatoryResponse.success) {
      return res.status(500).send({ message: 'Error sending signature' });
    }

    const { responseData } = signatoryResponse.response?.data;

    const participants: { name: string; email: string; signed: boolean; urlSign: string }[] =
      responseData.signatory.map((s: any) => {
        return {
          name: s.name,
          email: s.emailID,
          signed: false,
          urlSign: s.signing.url,
        };
      });

    if (!associate.adendumDigitalSignature) {
      associate.adendumDigitalSignature = associate.adendumDigitalSignature || [];
    }

    if (isEdit) {
      const lastAdendum = associate.adendumDigitalSignature[associate.adendumDigitalSignature.length - 1];
      const lastDocumentID = lastAdendum?.documentID;
      if (lastDocumentID) {
        console.log('Deleting last document...');
        const deletedRes = await deleteWeetrustDocument(token, lastDocumentID);
        if (!deletedRes.success) {
          logger.error(
            `[sendAddendumToWeetrust]: Error deleting last document ${lastDocumentID} for associateId: ${associateId}`
          );
          return res
            .status(500)
            .send({ message: 'No se pudo editar el documento anterior porqué ya fue firmado' });
        }
        associate.adendumDigitalSignature[associate.adendumDigitalSignature.length - 1] = {
          documentID,
          url: '',
          signed: false,
          isSent: true,
          participants,
        };
      }
    } else {
      associate.adendumDigitalSignature.push({
        documentID,
        url: '',
        signed: false,
        isSent: true,
        participants,
      });
    }
    try {
      fs.unlink(file.path, (err) => {
        if (err) {
          console.log('error deleting file', err);
        }
      });
    } catch (error: any) {
      console.log('error deleting file', error.message);
    }

    const mess = isEdit ? 'Adendum edited' : 'Adendum sent';

    logger.info(`[sendAddendumToWeetrust]: ${mess} to Weetrust for associateId: ${associateId}`);

    await Promise.allSettled(
      participants.map(async (p) => {
        if (p.email === EMAIL_OCN) {
          // Send to Manuel

          await sendAdendumWhatsappOCN({
            url: p.urlSign,
            name: 'Manuel',
            phone: '5548007452',
            associateName: `${associate.firstName} ${associate.lastName}`,
          });

          return;
        }
        // Client email

        await sendAdendumWhatsapp({
          url: p.urlSign,
          name: p.name,
          phone: associate.phone.toString(),
        });
      })
    );

    const stockVehicle = await StockVehicle.findById(vehicleId);

    if (stockVehicle) {
      stockVehicle.updateHistory.push({
        step: 'ADENDUM ENVIADO A FIRMAR',
        time: getCurrentDateTime(),
        userId: req.userId.userId,
      });
      await stockVehicle.save();
    }

    console.log('associate', associate.adendumDigitalSignature); // to view on production logs
    console.log('participants', participants); // to view on production logs

    await associate.save();
    return res.status(200).send({ message: 'Adendum sent to Weetrust' });
  } catch (error: any) {
    const message =
      error?.response?.data?.message || error.message || genericMessages.errors.somethingWentWrong;
    if (file.path) {
      fs.unlink(file.path, (err) => {
        if (err) {
          console.log('error deleting file', err);
        }
      });
    }
    if (DocumentID) {
      await deleteWeetrustDocument(undefined, DocumentID);
    }

    const obj = {
      message,
      documentID: DocumentID,
      associateId,
      error,
    };

    logger.error(`[sendAddendumToWeetrust]: ${obj} `);

    return res.status(500).send({ message });
  }
};
