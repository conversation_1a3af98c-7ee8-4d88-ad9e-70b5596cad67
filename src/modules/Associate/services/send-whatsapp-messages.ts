import axios from 'axios';
import { HILOS_API, HILOS_API_KEY } from '../../../constants';

interface Params {
  url: string;
  name: string;
  phone: string;
}

export const sendAdendumWhatsapp = async (data: Params) => {
  try {
    let { url } = data;
    url = url.replace('https://api.weetrust.mx/', '');
    url = url.replace('https://app.weetrust.mx/', '');
    url = url.replace('https://sandbox.weetrust.com.mx/', '');

    const templateId = '7680ec67-8b40-4314-8df4-7319ff455f86'; //

    const { data: d } = await axios.post(
      `${HILOS_API}/channels/whatsapp/template/${templateId}/send`,
      {
        variables: [data.name, url],
        phone: data.phone,
      },
      {
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`,
        },
      }
    );
    console.log('Message sent to client successfully', d);
  } catch (error: any) {
    console.error('[sendAdendumWhatsapp]', error.response.data || error.message);
  }
};

interface ParamsForOCNMessage extends Params {
  associateName: string;
}

export const sendAdendumWhatsappOCN = async (data: ParamsForOCNMessage) => {
  try {
    let { url } = data;

    url = url.replace('https://api.weetrust.mx/', '');
    url = url.replace('https://app.weetrust.mx/', '');
    url = url.replace('https://sandbox.weetrust.com.mx/', '');

    const templateId = '99f63a7b-3728-40a6-918b-e53a724038f1';
    const { data: d } = await axios.post(
      `${HILOS_API}/channels/whatsapp/template/${templateId}/send`,
      {
        variables: [data.name, data.associateName, url],
        phone: data.phone,
      },
      {
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`,
        },
      }
    );
    console.log('Message sent to OCN successfully', d);
  } catch (error: any) {
    console.error('[sendAdendumWhatsappOCN]', error.response.data || error.message);
  }
};
