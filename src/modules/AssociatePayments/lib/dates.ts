import { DateTime } from 'luxon';

export function getThursday5PM(dt: DateTime, timezone: string): DateTime {
  return dt.set({ weekday: 4, hour: 17, minute: 0, second: 0, millisecond: 0 }).setZone(timezone);
}

/**
 * Returns true if the current date is before Thursday 5 PM
 * @param dt - The date to compare, defaults is the current date
 * @param timezone - The timezone to use, defaults to 'America/Mexico_City'
 */

export function isBeforeThursday5PM(dt: DateTime = DateTime.now(), timezone: string = 'America/Mexico_City') {
  const thursday5PM = getThursday5PM(dt, timezone);

  if (dt.weekday > 4 || (dt.weekday === 4 && dt > thursday5PM)) {
    return false;
  }

  return dt < thursday5PM;
}

export function previousThursday(date: Date, timezone = 'America/Mexico_City') {
  // Convertimos el objeto Date nativo a un objeto DateTime de Luxon en la zona horaria especificada

  let dateTime = DateTime.fromJSDate(date, { zone: timezone });

  while (dateTime.weekday !== 4) {
    dateTime = dateTime.minus({ days: 1 });
  }

  const adjustedDateTime = dateTime.set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).setZone(timezone);

  const adjustedDateStr = adjustedDateTime.toISO()?.split('-').slice(0, 3).join('-') + 'Z';

  return new Date(adjustedDateStr);
}
