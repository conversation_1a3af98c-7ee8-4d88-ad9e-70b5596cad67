/* eslint-disable @typescript-eslint/no-use-before-define */
import { Types } from 'mongoose';
import AssociatePayments from '../../../models/associatePayments';
import { AsyncController } from '../../../types&interfaces/types';
import { CustomError, getCustomErrorStatusAndMessage } from '../../../services/customErrors';
import StockVehicle from '../../../models/StockVehicleSchema';
import { availableChangeStatus } from '../../../controllers/ChangeStatus/patchStockStatus';
import MainContractSchema from '../../../models/mainContractSchema';
import Associate from '../../../models/associateSchema';
import { divideWeeks } from '../services/divide-weeks';
import { addWeeksAdendum } from '../services/add-weeks';
import { logger } from '../../../clean/lib/logger';

function ValidateID(id: string | Types.ObjectId) {
  if (!Types.ObjectId.isValid(id)) {
    throw new CustomError('ID no valido', 404);
  }
}

export const updateAssociatePaymentById: AsyncController = async (req, res) => {
  const { associatePaymentId } = req.params;

  try {
    const associatePayment = await AssociatePayments.findById(associatePaymentId);
    if (!associatePayment) return res.status(404).send({ message: 'Pagos de asociado no encontrados' });

    return res.status(200).send({ message: 'Lista de pagos del asociado Actualizado' });
  } catch (error: any) {
    const { status, message } = getCustomErrorStatusAndMessage(error);
    return res.status(status).send({ message });
  }
};

export const updateByRelationId: AsyncController = async (req, res) => {
  const { contractId } = req.body;

  let query: any = {};

  if (contractId) {
    query = { contractId };
  }

  try {
    ValidateID(contractId);
    const hasParams = Object.keys(query).length > 0;
    const associatePayment = hasParams ? await AssociatePayments.findOne(query) : null;
    if (!associatePayment) return res.status(404).send({ message: 'Pagos de asociado no encontrados' });

    const mainContract = await MainContractSchema.findById(associatePayment.contractId);

    const associate = await Associate.findById(associatePayment.associateId);
    if (!associate) return res.status(404).send({ message: 'Asociado no encontrado' });
    if (!mainContract) return res.status(404).send({ message: 'Contrato principal no encontrado' });

    const entries = Object.keys(req.body);
    for (let i = 0; i < entries.length; i++) {
      if (entries[i] !== 'paymentsArray') {
        (associatePayment as any)[entries[i]] = Object.values(req.body)[i];
        continue;
      }

      if (entries[i] === 'paymentsArray') {
        const method = req.body.method;
        if (method === 'add-weeks') {
          await addWeeksAdendum({ req, associatePayment, mainContract, associate });
        } else {
          /* This else is when method === 'divideWeeks */
          /* DIVIDE WEEKS PROCESS */

          await divideWeeks({
            req,
            associatePayment,
            associate,
            mainContract,
          });
        }
      }
    }

    // const fs = require('fs');
    // const path = require('path');

    // const root = process.cwd();

    // const filePath = path.join(root, 'associatePayment.json');
    // fs.writeFileSync(filePath, JSON.stringify(associatePayment, null, 2));

    const vehicle = await StockVehicle.findById(associatePayment.vehiclesId);

    if (vehicle) {
      if (availableChangeStatus.includes(vehicle.status)) {
        vehicle.canFinishProcess = true;
        vehicle.updateHistory.push({
          step: `ADENDUM GENERADO`,
          userId: req.userId.userId,
          description: '',
        });
        await vehicle.save();
      }
    }
    associatePayment.adendumGenerated = true;

    await associatePayment.save();
    return res.status(200).send({ message: 'Pago de asociado Actualizado' });
  } catch (error: any) {
    console.error(error);
    logger.error('[updateByRelationId] Error on update by relation id' + JSON.stringify(error));

    const { status, message: m } = getCustomErrorStatusAndMessage(error);
    const message = error?.response?.data?.message || m || 'Algo salió mal';

    return res.status(status).send({ message });
  }
};
