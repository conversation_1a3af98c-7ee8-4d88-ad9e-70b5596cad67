import { AsyncController } from '@/types&interfaces/types';
import { createOrgDto } from '../dtos/create-org.dto';
import { organizationsService } from '@/vendor-platform/modules/organizations/services/organizations.service';

export const createOrganization: AsyncController = async (req, res) => {
  const validatedData = createOrgDto.parse(req.body);

  const role = req.userReq.role;
  const newOrg = await organizationsService.createOrganization(validatedData, role);

  return res.status(201).send({
    message: 'Organization created',
    data: newOrg,
  });
};
