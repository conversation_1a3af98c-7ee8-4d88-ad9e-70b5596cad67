import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { acceptVendorInvitation, inviteUserToVendor } from './controllers/invite-user.controller';
import { createOrganization } from './controllers/organizations.controller';
import { verifyToken } from '@/middlewares/verifyToken';

const adminCreateVendorsRouter = Router();

// Routes for Admin Platform

adminCreateVendorsRouter.post('/vendor/organization', verifyToken, errorHandlerV2(createOrganization));

adminCreateVendorsRouter.post('/vendor/invite-user', verifyToken, errorHandlerV2(inviteUserToVendor));
adminCreateVendorsRouter.post('/vendor/accept-invitation', errorHandlerV2(acceptVendorInvitation));

export default adminCreateVendorsRouter;
