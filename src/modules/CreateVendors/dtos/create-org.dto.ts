import z from 'zod';

export const createOrgDto = z.object({
  name: z.string().trim().min(3),

  globalAvailability: z
    .array(
      z.object({
        day: z.string().min(3),
        slots: z.array(z.string().min(5)),
      })
    )
    .optional(),
  users: z.array(z.string().uuid()).optional(),
  // literals validation, only 'MX' and 'US' are allowed
  country: z
    .union([z.literal('mx'), z.literal('us')])
    .optional()
    .default('mx'),
});

export type CreateOrgDto = z.infer<typeof createOrgDto>;
