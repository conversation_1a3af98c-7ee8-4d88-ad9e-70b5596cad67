import { Router } from 'express';
import { cronJobTempPaymentsHandler } from '../controllers/cronjobHandler.controller';
import { jobHandlerLogin } from '../controllers/jobHandlerLogin.controller';

const cronJobTempPayments = Router();

cronJobTempPayments.post('/login', jobHandlerLogin);
// cronJobTempPayments.use(verifyCronJobToken);
cronJobTempPayments.post('/run', cronJobTempPaymentsHandler);

export default cronJobTempPayments;
