import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../../../services/timestamps';

// export interface ITempSuscriptionPayments extends Document {
//   // _id: string;
//   associateId: Types.ObjectId;
//   associatePaymentId: Types.ObjectId;
//   stockId: Types.ObjectId;
//   suscriptionId: Types.ObjectId;
//   // status: string;
//   // createdAt: string;
//   // updatedAt: string;
//   tempItems: {
//     name: string;
//     description: string;
//     quantity: number;
//     temp: boolean;
//     total: number;
//     taxes: [
//       {
//         rate: number;
//         factor: string;
//         withholding: boolean;
//         type: string;
//         inclusive: boolean;
//       }
//     ];
//     expirationDate: string;
//   }[];
// }

const tempSuscriptionPayment = new Schema({
  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
    required: true,
  },
  associatePaymentId: {
    type: Schema.Types.ObjectId,
    ref: 'AssociatePayments',
    required: true,
  },
  stockId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicles',
    required: true,
  },
  region: {
    type: String,
    required: true,
  },
  suscriptionId: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending'],
    required: true,
    default: 'active',
  },

  adendumType: {
    type: String,
    required: false,
    default: 'divide-weeks',
  },

  activationDate: {
    type: Date,
    default: null,
  },

  dateToAddTempProducts: {
    type: Date,
    default: null,
  },

  stopDate: {
    type: Date,
    default: null,
  },

  tempItems: {
    type: [
      {
        name: { type: String, required: true },
        description: { type: String, required: true },
        quantity: { type: Number, required: true },
        temp: { type: Boolean, default: true },
        total: { type: Number, required: true },
        taxes: [
          {
            rate: { type: Number, required: true },
            factor: { type: String, required: true },
            withholding: { type: Boolean, required: true },
            type: { type: String, required: true },
            inclusive: { type: Boolean, required: true },
          },
        ],
        expirationDate: { type: Date, required: true },
      },
    ],
    required: true,
  },
  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

const TempSuscriptionPayments = model('TempSuscriptionPayments', tempSuscriptionPayment);

export default TempSuscriptionPayments;
