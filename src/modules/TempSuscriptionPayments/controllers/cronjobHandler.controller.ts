/* eslint-disable @typescript-eslint/no-use-before-define */
import moment from 'moment';
// import { ValidRegion, gigstackRequests } from '../../../services/tokenAssignGigstack';
import { AsyncController } from '../../../types&interfaces/types';
import TempSuscriptionPayments from '../model/tempSuscriptionPayment.model';
// import { getCurrentDateTime } from '../../../services/timestamps';
import axios from 'axios';
import Associate from '../../../models/associateSchema';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../../constants/payments-api';
import { getCurrentDateTime } from '../../../services/timestamps';
import { logger } from '../../../clean/lib/logger';

type GenerateDateNotUTC = {
  date?: string | null;
  currentTime?: boolean;
};

// const testingDate = '2024-11-21';
/**
 * Function to generate a date without changing the time zone, by default it will generate a date with 06:00:00 time
 * @param {GenerateDateNotUTC}{ date = null, removeTime = true } by default
 * @returns {Date}
 */

export function generateDateNotUTC({ date = null, currentTime = false }: GenerateDateNotUTC = {}) {
  const today = date ? moment(date) : moment();
  const year = today.year();
  const month = today.month();
  const day = today.date();

  const hour = currentTime ? today.hour() : 6;
  const minute = currentTime ? today.minute() : 0;
  const second = currentTime ? today.second() : 0;
  return new Date(Date.UTC(year, month, day, hour, minute, second));
}

export const cronJobTempPaymentsHandler: AsyncController = async (_req, res) => {
  try {
    /* FIRST PART */
    await firstPart();

    /* SECOND PART */
    /* ACTIVATE PENDING TEMP SUPSCRIPTIONS */

    await secondPart();

    return res.status(200).json({ message: 'Proceso terminado' });
  } catch (error) {
    console.error('Error en cronJobTempPaymentsHandler', error);
    return res.status(500).json({ message: 'Error al procesar la solicitud' });
  }
};

/* FIRST PART */
/**
 * Function to remove temporal items from a subscription if the expiration date is less than or equal to today
 */
async function firstPart() {
  const tempPayments = await TempSuscriptionPayments.find({
    status: 'active',
    // stockId: '64e3e69e0cdfc2c684350bf6',
  });

  // Crear un nuevo objeto Date sin cambiar la zona horaria
  const today = generateDateNotUTC({
    // date: testingDate, // for testing
  });

  console.log('[TEMP CRONJOB]: temporal payments status active', tempPayments.length);

  for (const tempPayment of tempPayments) {
    // if (tempPayment.adendumType === 'add-weeks') {
    //   if (tempPayment.addWeeksExpirationDate && tempPayment.addWeeksExpirationDate <= today) {
    //     const associate = await Associate.findById(tempPayment.associateId);
    //     if (!associate) continue;

    //     await axios.patch(
    //       `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
    //       {
    //         status: true,
    //       },
    //       {
    //         headers: {
    //           Authorization: `Bearer ${PAYMENTS_API_KEY}`,
    //         },
    //       }
    //     );

    //     tempPayment.status = 'inactive';
    //     tempPayment.updatedAt = getCurrentDateTime();
    //     await tempPayment.save();
    //   }
    // }

    const copyTempItems = [...tempPayment.tempItems];
    for (const tempItem of tempPayment.tempItems) {
      // console.log('[TEMP CRONJOB]: today', today, tempItem.expirationDate);
      // console.log('FIRST PART FUNCTION, Expiration Date ProductItem: ', tempItem.expirationDate);
      console.log('FIRST PART FUNCTION, isExpired?', tempItem.expirationDate <= today);
      console.log('Expiration Date: ', tempItem.expirationDate);
      console.log('Today: ', today);
      if (tempItem.expirationDate <= today) {
        tempPayment.tempItems.shift();
      }
    } // FINISH FOR LOOP OF TEMPITEMS;
    console.log('Stock Id: ', tempPayment.stockId);
    /* request to gigstack to removeTempItems = true */
    const associate = await Associate.findById(tempPayment.associateId);
    if (tempPayment.tempItems.length === 0) {
      console.log('FIRST PART FUNCTION, [TEMP CRONJOB 2]: removeTempItems TO SUBSCRIPTION');

      if (associate) {
        if (associate && associate.clientId) {
          console.log('remove-temporal-products', tempPayment.tempItems);
          await axios.patch(
            `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/remove-temporal-products`,
            {},
            {
              headers: {
                Authorization: `Bearer ${PAYMENTS_API_KEY}`,
              },
            }
          );
          // update status to inactive because there are no more temporal items
          await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
            status: 'inactive',
            updatedAt: getCurrentDateTime(),
          });
          console.log(`[TEMP CRONJOB]: inactive ${associate?.clientId}`);
        }
      }
    } else if (tempPayment.tempItems.length < copyTempItems.length) {
      console.log(
        'THERE ARE ITEMS REMOVED FROM THE ARRAY',
        tempPayment.tempItems.length,
        copyTempItems.length
      );
      if (tempPayments.length === 0 || tempPayments.length > 0) return;

      // update tempItems if some items were removed from the array tempItems in the previous step
      if (associate && associate.clientId) {
        console.log('remove-temporal-products', tempPayment.tempItems);
        await axios.patch(
          `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/remove-temporal-products`,
          {},
          {
            headers: {
              Authorization: `Bearer ${PAYMENTS_API_KEY}`,
            },
          }
        );

        const formatTempItems = tempPayment.tempItems.map((item) => {
          return {
            name: item.name,
            description: item.description,
            quantity: item.quantity,
            price: item.total,
            isTemporal: true,
          };
        });

        // console.log('add-temporal-products', formatTempItems);
        console.log(
          'ADDING NEW CALCULATED TEMPORAL PRODUCTS, REMOVING EXPIRED ITEMS AND ADDING JUST THE ACTIVE ONES'
        );
        const responseRemove = await axios.patch(
          `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/add-temporal-products`,
          {
            temporalProducts: formatTempItems,
          },
          {
            headers: {
              Authorization: `Bearer ${PAYMENTS_API_KEY}`,
            },
          }
        );
        console.log('FIRST PART FUNCTION, responseRemove', responseRemove.data);
      }
    }
    console.log('-------------------------------------------------');
  }
}

/* SECOND PART */

/**
 * Function to activate temporal payments if the activation date is less than or equal to today
 */
async function secondPart() {
  const tempPaymentsPending = await TempSuscriptionPayments.find({
    status: 'pending',
    // stockId: '64e3e69e0cdfc2c684350bf6',
  });

  console.log(
    'SECOND PART FUNCTION, [TEMP CRONJOB]: temporal payments status pending',
    tempPaymentsPending.length
  );
  // return;
  const today = generateDateNotUTC({
    // date: testingDate, // for testing
  });

  console.log('[TEMP CRONJOB]: today', today);
  for (const tempPayment of tempPaymentsPending) {
    const activationDate = tempPayment.activationDate;

    if (tempPayment.adendumType && tempPayment.adendumType === 'add-weeks') {
      // validate if the activation date is today
      const stopDate = tempPayment.stopDate;

      if (stopDate && stopDate.getTime() === today.getTime()) {
        // console.log(
        //   /*  */
        //   'SECOND PART FUNCTION, stoping subscription for add-weeks',
        //   tempPayment.stockId
        // );

        const associate = await Associate.findById(tempPayment.associateId);

        if (!associate) continue;
        logger.info(
          `[TEMP CRONJOB]: stoping subscription for add-weeks, Client ID: ${associate.clientId}, StockVehicle ID: ${tempPayment.stockId}`
        );

        await axios.patch(
          `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
          {
            status: false,
          },
          {
            headers: {
              Authorization: `Bearer ${PAYMENTS_API_KEY}`,
            },
          }
        );
      }

      const activateDate = tempPayment.activationDate;

      if (activateDate && activateDate.getTime() === today.getTime()) {
        // console.log('SECOND PART FUNCTION, activation date is today', tempPayment.stockId);

        const associate = await Associate.findById(tempPayment.associateId);
        if (!associate) continue;

        logger.info(
          `[TEMP CRONJOB]: activating subscription for add-weeks, Client ID: ${associate.clientId}, StockVehicle ID: ${tempPayment.stockId}`
        );

        await axios.patch(
          `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
          {
            status: true,
          },
          {
            headers: {
              Authorization: `Bearer ${PAYMENTS_API_KEY}`,
            },
          }
        );

        await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
          status: 'inactive',
          updatedAt: getCurrentDateTime(),
        });
      }
    }

    const stopDate = tempPayment.stopDate;
    const dateToAddTempProducts = tempPayment.dateToAddTempProducts;

    const associate = await Associate.findById(tempPayment.associateId);

    if (!associate) continue;

    // console.log('today', today);
    // console.log('activationDate', activationDate);
    // console.log(
    //   'validation between activationDate and today',
    //   activationDate && activationDate?.getTime() === today.getTime(),
    //   activationDate?.getTime(),
    //   today.getTime()
    // );
    // console.log('-------------------------------------------------');
    // console.log('stopDate', stopDate);
    // console.log(
    //   'validation',
    //   stopDate && stopDate.getTime() === today.getTime(),
    //   stopDate?.getTime(),
    //   today.getTime()
    // );
    if (stopDate && stopDate.getTime() === today.getTime()) {
      // stop subscription
      console.log('stoping subscription', 'Stock Id: ', tempPayment.stockId);
      await axios.patch(
        `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
        {
          status: false,
        },
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );
      continue;
    }

    // const dateToAddTempProducts = tempPayment.dateToAddTempProducts;

    /*  console.log('SECOND PART FUNCTION, activationDate', activationDate, 'Today', today);
    console.log(
      'SECOND PART FUNCTION, isToday',
      activationDate && activationDate?.getTime() === today.getTime()
    ); */
    if (tempPayment.tempItems.length === 1) {
      const expirationDate = tempPayment.tempItems[0].expirationDate;
      if (expirationDate === today) {
        await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
          status: 'inactive',
          updatedAt: getCurrentDateTime(),
        });
        continue;
      }
    }
    const formatTempItems = tempPayment.tempItems.map((item) => {
      return {
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        price: item.total,
        isTemporal: true,
      };
    });
    if (activationDate && activationDate?.getTime() === today.getTime()) {
      console.log(
        'actvation date is before than today, so it will be activated now',
        'Stock Id: ',
        tempPayment.stockId
      );

      await axios.patch(
        `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
        {
          status: true,
        },
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );

      if (!dateToAddTempProducts && associate.clientId) {
        try {
          await axios.patch(
            `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/add-temporal-products`,
            {
              temporalProducts: formatTempItems,
            },
            {
              headers: {
                Authorization: `Bearer ${PAYMENTS_API_KEY}`,
              },
            }
          );
        } catch (error: any) {
          console.error('Error al agregar temporal products', error.response.data);
        }

        await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
          status: 'active',
          updatedAt: getCurrentDateTime(),
        });
      }
    }
    /*  console.log(
      'VALIDATION 2----',
      dateToAddTempProducts && dateToAddTempProducts.getTime() === today.getTime(),
      dateToAddTempProducts?.getTime(),
      today.getTime()
    ); */
    if (dateToAddTempProducts && dateToAddTempProducts.getTime() === today.getTime() && associate.clientId) {
      try {
        await axios.patch(
          `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/add-temporal-products`,
          {
            temporalProducts: formatTempItems,
          },
          {
            headers: {
              Authorization: `Bearer ${PAYMENTS_API_KEY}`,
            },
          }
        );
      } catch (error: any) {
        console.error('Error al agregar temporal products', error.response.data);
      }

      await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
        status: 'active',
        updatedAt: getCurrentDateTime(),
      });
    }
    console.log('-------------------------------------------------');
  }
}
