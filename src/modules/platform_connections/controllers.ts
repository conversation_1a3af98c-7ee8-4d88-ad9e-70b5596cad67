import { genericMessages } from '../../constants';
import { AsyncController } from '../../types&interfaces/types';
import { sendStepOneEmail } from './emailFunc';

export const sendPlatformEmal: AsyncController = async (req, res) => {
  const { phone, pin, password, platform } = req.body;
  try {
    await sendStepOneEmail({ phone, pin, password, platform });
    return res.status(200).send('Hello from platform connections');
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};
