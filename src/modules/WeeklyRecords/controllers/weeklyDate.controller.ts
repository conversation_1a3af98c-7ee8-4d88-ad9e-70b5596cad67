import { genericMessages } from '../../../constants';
import { AsyncController } from '../../../types&interfaces/types';
import WeeklyDatesModel from '../model/weeklyDates';

/**
 * @description Controller to get the dates of the weekly records
 */

export const getWeeklyDates: AsyncController = async (_, res) => {
  try {
    const weeklyDates = await WeeklyDatesModel.find().sort({ startDate: -1 });
    return res.status(200).json({ message: 'Registro de fechas obtenidas con exito', weeklyDates });
  } catch (error) {
    return res.status(500).json({ message: genericMessages.errors.somethingWentWrong, error });
  }
};
