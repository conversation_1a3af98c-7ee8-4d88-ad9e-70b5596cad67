import { Router } from 'express';
import { verifyCronJobToken } from '../../../middlewares/verifyToken';
import { generateWeeklyRecords } from '../controllers/weeklyCronRecords.controller';

const weeklyCronRecords = Router();

weeklyCronRecords.use((req, res, next) => {
  // Ejecuta el middleware verifyCronJobToken si la ruta es /job-handler
  if (req.path.includes('/weekly-records-cronjob')) {
    verifyCronJobToken(req, res, next);
  } else {
    next(); // Si no es la ruta específica, pasamos al siguiente middleware sin verificar
  }
});

weeklyCronRecords.post('/generate', generateWeeklyRecords);

export default weeklyCronRecords;
