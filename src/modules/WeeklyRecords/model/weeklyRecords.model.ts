import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../../../services/timestamps';
import { stock } from '../../../controllers/ChangeStatus/cases';

type Status = (typeof stock)['status'];

export interface WeeklyRecords {
  _id: Schema.Types.ObjectId;
  stockVehicles: {
    _id: Schema.Types.ObjectId;
    carNumber: string;
    status: string;
    deliveredDate: string;
  }[];
  statusStatistics: { [K in Status]: number };
  weekNumber: number;
  startDate: Date;
  endDate: Date;
  year: number;
  createdAt: string;
  updatedAt: string;
}

const weeklyRecordsSchema = new Schema<WeeklyRecords>({
  stockVehicles: [
    {
      _id: Schema.Types.ObjectId,
      carNumber: String,
      status: String,
      deliveredDate: String,
    },
  ],
  statusStatistics: {
    type: Schema.Types.Mixed,
    required: false,
  },
  weekNumber: {
    type: Number,
    required: true,
  },
  startDate: {
    type: Date,
    required: true,
  },
  endDate: {
    type: Date,
    required: true,
  },
  year: {
    type: Number,
    required: true,
  },
  createdAt: { type: String, getCurrentDateTime },
  updatedAt: { type: String, getCurrentDateTime },
});
// weekNumber: 1
weeklyRecordsSchema.index({ startDate: 1 }, { unique: true });
weeklyRecordsSchema.index({ weekNumber: 1 });

const WeeklyRecordsModel = model<WeeklyRecords>('WeeklyRecords', weeklyRecordsSchema);

export default WeeklyRecordsModel;
