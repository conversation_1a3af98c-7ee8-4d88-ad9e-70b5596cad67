import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../../../services/timestamps';

export interface WeeklyDates {
  _id: Schema.Types.ObjectId;
  weekNumber: number;
  startDate: Date;
  year: number;
  payment: number;
  createdAt: string;
}

const weeklyDatesSchema = new Schema<WeeklyDates>({
  weekNumber: {
    type: Number,
    required: true,
  },
  startDate: {
    type: Date,
    required: true,
  },
  // endDate: {
  //   type: Date,
  //   required: true,
  // },
  year: {
    type: Number,
    required: true,
  },
  createdAt: { type: String, getCurrentDateTime },
});

const WeeklyDatesModel = model<WeeklyDates>('WeeklyDates', weeklyDatesSchema);

export default WeeklyDatesModel;
