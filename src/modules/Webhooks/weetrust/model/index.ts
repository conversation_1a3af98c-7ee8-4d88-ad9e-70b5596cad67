import { Schema, model } from 'mongoose';

const weetrustWebhook = new Schema({
  body: {
    type: Object,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
  },
  success: {
    type: Boolean,
    default: false,
  },
  // This field will be used to know if the new records saved are from now on or from the past
  // because success is a new field added, and the old records will not have this field
  // so we can use this field to know if the record is from now on or from the past
  isNewRecord: {
    type: Boolean,
    default: true,
  },
});

weetrustWebhook.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const WeetrustWebhook = model('WeetrustWebhook', weetrustWebhook);

export default WeetrustWebhook;

export const weetrustWebhookInstance = new WeetrustWebhook();
