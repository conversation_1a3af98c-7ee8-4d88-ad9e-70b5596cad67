import { Router } from 'express';
import {
  receiveCompletedDocument,
  receiveDocumentSignEvent,
  retryProcessAdendumDocumentCompleted,
} from './controller';

const weetrustWebhook = Router();

weetrustWebhook.post('/completedDocument', receiveCompletedDocument);

weetrustWebhook.post('/signDocument', receiveDocumentSignEvent);

weetrustWebhook.post('/manual-retry/adendum/completedDocument', retryProcessAdendumDocumentCompleted);

export default weetrustWebhook;
