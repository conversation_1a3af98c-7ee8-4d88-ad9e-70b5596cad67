## Clean Architecture

This project is following the
[Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
software design philosophy.

![Clean Architecture Diagram](/src/clean/docs/assets/clean_architecture.jpg?raw=true 'Clean Architecture Diagram')

## Modules

### Entities

`domain/entities` Entities are native business objects. In this
project, examples are Customer, Vehicle, Earnings, etc.
An entity is typically saved to the database using a repository method
(see below), but they can also be retrieved from APIs using HTTP clients.

`domain/enums` We use enums to guarantee the uniqueness of constant values.

### Adapters

`domain/adapters` The adapters are functions that convert a database model or
third party api model to an entity and vice versa. The adapters are used by the
repositories to convert the model instances to entities and by the resources to
convert the json request payload to entities.

### Repositories

`data/repositories` This functions receive entities and change the database in a
specific way or connect with an api to retrieve information. This funtions must
return entities by using an adapter to convert the model instance
to an entity. The repositories are used by the use cases and in this way, the database or third party api is abstracted from
the business logic.

### Use cases

`domain/usecases` The use cases contains the actual business logic. For example,
what to do if we asign a vehicle to a customer? The use case checks that the vehicle is not asigned, that the vechile its in a status that can be asigned, that the customer has enough credit, etc. The use case can call the repositories to save the changes to the database.

### Exceptions

`domain/exceptions` Exceptions are typically raised by use cases. They can
be converted to JSON, and returned to the HTTP client and typically include a
link to the documentation.

### Resources

`presentation/resources` Are built using Express. They use
validations to parse HTTP payloads. They call the use cases. They also
check the authentication, encode the Exceptions into JSON an serialize the
responses.

### Validations

`presentation/validations` Endpoint payload validation are build using the zod
library, they are by the resources to parse and validate the data. A lot of
schemata corresponds to entities or enums.

### Responses

`presentation/serializers` Endpoint JSON response are build using the class-transformer
library to convert entities to JSON and to remove fields that should not be exposed or translate
fields to a more user friendly format like Dates.
