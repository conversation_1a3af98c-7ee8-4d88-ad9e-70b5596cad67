export enum ErrorCode {
  admission_request_not_found = 'admission_request_not_found',
  try_again = 'try_again',
  could_not_retrieve_palenca_earnings = 'could_not_retrieve_palenca_earnings',
  could_not_retrieve_palenca_metrics = 'could_not_retrieve_palenca_metrics',
  missing_media_file = 'missing_media_file',
  could_not_upload_media = 'could_not_upload_media',
  could_not_update_request_document = 'could_not_update_request_document',
  could_not_sign_url = 'could_not_sign_url',
  request_document_not_found = 'request_document_not_found',
  not_all_documents_approved = 'not_all_documents_approved',
  request_not_in_documents_analysis_status = 'request_not_in_documents_analysis_status',
  could_not_found_platform_metrics = 'could_not_found_platform_metrics',
  request_not_in_risk_analysis_status = 'request_not_in_risk_analysis_status',
  risk_analysis_data_not_found = 'risk_analysis_data_not_found',
  could_not_retrieve_scorecard_config = 'could_not_retrieve_scorecard_config',
  risk_analysis_not_completed = 'risk_analysis_not_completed',
  request_not_in_final_evaluation_status = 'request_not_in_final_evaluation_status',
  platform_request_not_found = 'platform_request_not_found',
  already_exist = 'already_exist',
  slot_not_found = 'slot_not_found',
  appointment_not_found = 'appointment_not_found',
  appointment_already_exists = 'appointment_already_exists',
  slot_is_not_available = 'slot_is_not_available',
  appointment_max_reschedule_limit_exceeded = 'appointment_max_reschedule_limit_exceeded',
  slot_booking_time_limit = 'slot_booking_time_limit',
  schedule_not_found = 'schedule_not_found',
  meeting_link_creation_failed = 'meeting_link_creation_failed',
  request_not_in_social_analysis_status = 'request_not_in_social_analysis_status',
  model_execution_failed = 'model_execution_failed',
  required_documents_not_approved = 'required_documents_not_approved',
}
