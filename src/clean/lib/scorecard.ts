import { ScorecardConfig, ScorecardVariable, ScorecardVariableCategory } from '../domain/entities';
import {
  VehicleCondition,
  RiskCategory,
  ScorecardVersion,
  ScorecardVariableName,
  VehicleType,
  ScorecardVariableCategoryType,
} from '../domain/enums';

/**
 * *** DEPRECATED ***
 */
export function scaleScoreToRange({
  score,
  minScore,
  maxScore,
  minScaledScore,
  maxScaledScore,
}: {
  score: number;
  minScore: number;
  maxScore: number;
  minScaledScore: number;
  maxScaledScore: number;
}): number {
  // Perform linear transformation with reversed scaling for risk analysis
  const scaledScore =
    ((maxScore - score) / (maxScore - minScore)) * (maxScaledScore - minScaledScore) + minScaledScore;

  // Round the scaled score to the nearest integer
  return Math.round(scaledScore);
}

const ageVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.age,
  type: ScorecardVariableCategoryType.numeric,
  categories: [
    // Younger age groups might be considered slightly higher risk
    new ScorecardVariableCategory({
      threshold: { min: 18, max: 25 },
      riskCategory: RiskCategory.medium,
      weight: 0.15,
      riskScore: 2,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 26, max: 35 },
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
    // Higher age groups might have slightly increased risk
    new ScorecardVariableCategory({
      threshold: { min: 36, max: 55 },
      riskCategory: RiskCategory.medium,
      weight: 0.15,
      riskScore: 2,
    }),
  ],
});

const gigPlatformsVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.gig_platforms,
  type: ScorecardVariableCategoryType.numeric,
  categories: [
    // Working on multiple platforms might indicate higher risk due to divided attention
    new ScorecardVariableCategory({
      threshold: { min: 0, max: 1 },
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 2, max: 3 },
      riskCategory: RiskCategory.high,
      weight: 0.2,
      riskScore: 3,
    }),
  ],
});

const lifeTimeCompletedTripsVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.life_time_completed_trips,
  type: ScorecardVariableCategoryType.numeric,
  categories: [
    // Less experience might indicate higher risk
    new ScorecardVariableCategory({
      threshold: { min: 0, max: 100 },
      riskCategory: RiskCategory.high,
      weight: 0.2,
      riskScore: 3,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 101, max: 1000 },
      riskCategory: RiskCategory.medium,
      weight: 0.15,
      riskScore: 2,
    }),
    // More experience indicates lower risk
    new ScorecardVariableCategory({
      threshold: { min: 1001, max: 1000000000 },
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
  ],
});

const daysSinceFirstTripVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.days_since_first_trip,
  type: ScorecardVariableCategoryType.numeric,
  categories: [
    // Newer drivers might be higher risk
    new ScorecardVariableCategory({
      threshold: { min: 0, max: 365 },
      riskCategory: RiskCategory.high,
      weight: 0.2,
      riskScore: 3,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 366, max: 730 },
      riskCategory: RiskCategory.medium,
      weight: 0.15,
      riskScore: 2,
    }),
    // Long-term drivers are considered lower risk
    new ScorecardVariableCategory({
      threshold: { min: 731, max: 1000000000 },
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
  ],
});

const percentageAcceptanceRateVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.percentage_acceptance_rate,
  type: ScorecardVariableCategoryType.numeric,
  categories: [
    new ScorecardVariableCategory({
      threshold: { min: 0, max: 0.4 },
      riskCategory: RiskCategory.high,
      weight: 0.15,
      riskScore: 2,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 0.41, max: 0.7 },
      riskCategory: RiskCategory.medium,
      weight: 0.1,
      riskScore: 1,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 0.71, max: 1 },
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
  ],
});

const percentageCancellationRateVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.percentage_cancellation_rate,
  type: ScorecardVariableCategoryType.numeric,
  categories: [
    new ScorecardVariableCategory({
      threshold: { min: 0, max: 0.3 },
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 0.31, max: 0.6 },
      riskCategory: RiskCategory.medium,
      weight: 0.15,
      riskScore: 2,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 0.61, max: 1 },
      riskCategory: RiskCategory.high,
      weight: 0.2,
      riskScore: 3,
    }),
  ],
});

const averageRatingVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.average_rating,
  type: ScorecardVariableCategoryType.numeric,
  categories: [
    new ScorecardVariableCategory({
      threshold: { min: 0, max: 3.5 },
      riskCategory: RiskCategory.high,
      weight: 0.15,
      riskScore: 2,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 3.6, max: 4.5 },
      riskCategory: RiskCategory.medium,
      weight: 0.1,
      riskScore: 1,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 4.6, max: 5 },
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
  ],
});

const earningsLast12WeeksVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.earnings_last_12_weeks,
  type: ScorecardVariableCategoryType.numeric,
  categories: [
    new ScorecardVariableCategory({
      threshold: { min: 0, max: 40000 },
      riskCategory: RiskCategory.high,
      weight: 0.15,
      riskScore: 2,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 40001, max: 80000 },
      riskCategory: RiskCategory.medium,
      weight: 0.1,
      riskScore: 1,
    }),
    new ScorecardVariableCategory({
      threshold: { min: 80001, max: 1000000000 },
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
  ],
});

const vehicleConditioneVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.vehicle_condition,
  type: ScorecardVariableCategoryType.categorical,
  categories: [
    new ScorecardVariableCategory({
      threshold: VehicleCondition.new,
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
    new ScorecardVariableCategory({
      threshold: VehicleCondition.used,
      riskCategory: RiskCategory.medium,
      weight: 0.15,
      riskScore: 2,
    }),
  ],
});

const vehicleTypeVariableV1 = new ScorecardVariable({
  name: ScorecardVariableName.vehicle_type,
  type: ScorecardVariableCategoryType.categorical,
  categories: [
    new ScorecardVariableCategory({
      threshold: VehicleType.car,
      riskCategory: RiskCategory.low,
      weight: 0.1,
      riskScore: 1,
    }),
    new ScorecardVariableCategory({
      threshold: VehicleType.motorcycle,
      riskCategory: RiskCategory.medium,
      weight: 0.15,
      riskScore: 2,
    }),
  ],
});

// const riskCityVariableV1 = new ScorecardVariable({
//   name: ScorecardVariableName.risk_city,
//   categories: [
//     new ScorecardVariableCategory({
//       threshold: RiskCityList.high_risk_list,
//       riskCategory: RiskCategory.high,
//       weight: 0.1,
//       riskScore: 1,
//     }),
//     new ScorecardVariableCategory({
//       threshold: RiskCityList.medium_risk_list,
//       riskCategory: RiskCategory.medium,
//       weight: 0.1,
//       riskScore: 1,
//     }),
//     new ScorecardVariableCategory({
//       threshold: RiskCityList.low_risk_list,
//       riskCategory: RiskCategory.low,
//       weight: 0.1,
//       riskScore: 1,
//     }),
//   ],
// });

const scorecardV1 = new ScorecardConfig({
  version: ScorecardVersion.v1,
  minScore: 1,
  maxScore: 3.9,
  minScaledScore: 300,
  maxScaledScore: 850,
  variables: [
    ageVariableV1,
    gigPlatformsVariableV1,
    lifeTimeCompletedTripsVariableV1,
    daysSinceFirstTripVariableV1,
    percentageAcceptanceRateVariableV1,
    percentageCancellationRateVariableV1,
    averageRatingVariableV1,
    earningsLast12WeeksVariableV1,
    vehicleConditioneVariableV1,
    vehicleTypeVariableV1,
    // riskCityVariableV1,
  ],
});

export const scorecardConfigDB = [scorecardV1];
