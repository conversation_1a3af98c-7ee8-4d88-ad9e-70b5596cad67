import axios, { AxiosInstance, AxiosResponse, AxiosError, AxiosRequestConfig } from 'axios';
import { logger } from './logger';
export class HttpClient {
  private axiosInstance: AxiosInstance;

  constructor(baseURL: string, headers: Record<string, string> = {}) {
    this.axiosInstance = axios.create({
      baseURL,
      headers,
    });
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.get<T>(url, config);
      return response.data;
    } catch (error) {
      throw this.handleAxiosError(error);
    }
  }

  async post<T>(url: string, data: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.post<T>(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleAxiosError(error);
    }
  }

  async put<T>(url: string, data: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.put<T>(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleAxiosError(error);
    }
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.delete<T>(url, config);
      return response.data;
    } catch (error) {
      throw this.handleAxiosError(error);
    }
  }

  async patch<T>(url: string, data: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.patch<T>(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleAxiosError(error);
    }
  }

  // TODO: Better error handling
  private handleAxiosError(error: unknown) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        // The request was made, and the server responded with a status code outside the range of 2xx
        logger.error(
          `[handleAxiosError] Request failed with status code: ${axiosError.response.status} and data: ${axiosError.response.data}`
        );
        return new Error('Request failed with status code: ' + axiosError.response.status);
      } else if (axiosError.request) {
        // The request was made but no response was received
        logger.error(
          `[handleAxiosError] No response received from the server for request: ${axiosError.request}`
        );
        return new Error('No response received from the server');
      } else {
        // Something happened in setting up the request that triggered an error
        logger.error(`[handleAxiosError] Error setting up the request: ${axiosError.message}`);
        return new Error('Error setting up the request: ' + axiosError.message);
      }
    } else {
      // Handle other types of errors if needed
      logger.error(`[handleAxiosError] Unkown error occurred with message: ${error}`);
      return new Error('An error occurred');
    }
  }
}
