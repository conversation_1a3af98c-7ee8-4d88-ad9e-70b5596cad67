import * as winston from 'winston';
import { isDev, isProd } from '../../constants';
// import DailyRotateFile = require('winston-daily-rotate-file');

/**
 * we don't need to write logs in a fily any more, we are already using
 * Loki to store logs.
 */
// const transport = new DailyRotateFile({
//   filename: 'logs/%DATE%.log',
//   datePattern: 'YYYY-MM-DD',
//   zippedArchive: true,
//   maxSize: '20m',
//   maxFiles: '4d',
// });

const consoleTransport = new winston.transports.Console({
  format: winston.format.combine(
    ...(!(isDev || isProd) ? [winston.format.colorize({ all: true })] : []),
    winston.format.printf((info) => {
      const { timestamp, level, message, ...args } = info;
      if (isDev || isProd) {
        const orderedLog = {
          timestamp: timestamp,
          level: level,
          message: message,
          ...args, // Spread other fields to ensure they are also included
        };
        return JSON.stringify(orderedLog);
      }
      return `${timestamp} ${level}: ${message} ${
        Object.keys(args).length ? JSON.stringify(args, null, 2) : ''
      }`;
    })
  ),
});

export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss',
    }),
    winston.format.json()
  ),
  transports: [consoleTransport],
});
