const multer = require('multer');
import { Router } from 'express';
import { cleanAuthMiddleware } from '../../middlewares/cleanAuth';

import {
  createAdmissionRequestResource,
  getPublicAdmissionRequestResource,
  getAdmissionRequestResource,
  getAdmissionRequestScreenshots,
  updateRequestPersonalDataResource,
  updateRequestDocumentsResource,
  mediaUploadResource,
  taskRetrieveEarningsResource,
  taskRetrieveMetricsResource,
  palencaWebhookResource,
  createHomeVisitResource,
  addPalencaAccount,
  startEarningsAnalysisResource,
  getPlatformEarningsResource,
  searchAdmissionRequestsResource,
  getRequestDocumentsAnalysisResource,
  approveRequestDocumentResource,
  rejectRequestDocumentResource,
  approveDocumentAnalysisResource,
  getHomeVisitResource,
  getPlatformMetricsResource,
  getEventsResource,
  approveRiskAnalysisResource,
  rejectRiskAnalysisResource,
  rejectDocumentsAnalysisResource,
  checkEarningsAnalysisOrQueueResource,
  upadtePlatformMetricResource,
  createAdmissionRequestResourceByCsv,
  updateRequestHomeVisitResource,
  addAvalDataResource,
  addAvalDataResourceWithDocs,
  approveSocialAnalysisResource,
  executeSocialAnalysisResource,
  retryAnalysisResource,
  rejectSocialAnalysisResource,
  resetHomeVisitScheduleLinkSendDate,
  findAdmissionRequestByAssociateId,
  fetchVehiceViolation,
  fetchVehiceAmountViolation,
} from './resources';
import { errorHandler } from '../errors/express';
import { upload as uploadDiskStorage } from '../../multer/multer';
import * as fields from '../../multer/fields/admissionRequest';
import {
  cancelAppointment,
  createAppointment,
  getAppointmentForDriverWebApp,
  getAvailableSlots,
} from '../../modules/Calendar/controllers/calendar.controller';

const storage = multer.memoryStorage();
const upload = multer({ storage });
const publicRouter = Router();
const secureRouter = Router();
/**
 * making this because publicRouter isn't work with query strings,
 * and right now i don't want to change public router because it's being
 * used in critical flows.
 */
const routerWithoutAuthentication = Router();
secureRouter.use(cleanAuthMiddleware);

const PUBLIC_ADMISSION_REQUESTS_PATH = '/public/admission/requests';
const ADMISSION_PATH = '/admission/requests';

/*
 * Secure routes
 */

publicRouter.post(`${ADMISSION_PATH}`, errorHandler(createAdmissionRequestResource));
secureRouter.post(
  `${ADMISSION_PATH}/csv`,
  upload.single('file'),
  errorHandler(createAdmissionRequestResourceByCsv)
);
secureRouter.post(`${ADMISSION_PATH}/search`, errorHandler(searchAdmissionRequestsResource));
secureRouter.post(`${ADMISSION_PATH}/find`, errorHandler(findAdmissionRequestByAssociateId));
secureRouter.get(`${ADMISSION_PATH}/:requestId`, errorHandler(getAdmissionRequestResource));
secureRouter.get(`${ADMISSION_PATH}/screenshots/:requestId`, errorHandler(getAdmissionRequestScreenshots));
secureRouter.patch(
  `${ADMISSION_PATH}/:requestId/personal-data`,
  errorHandler(updateRequestPersonalDataResource)
);
secureRouter.patch(`${ADMISSION_PATH}/:requestId/documents`, errorHandler(updateRequestDocumentsResource));
secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents/:documentType/approve`,
  errorHandler(approveRequestDocumentResource)
);
secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents/:documentType/reject`,
  errorHandler(rejectRequestDocumentResource)
);
secureRouter.get(
  `${ADMISSION_PATH}/:requestId/documents-analysis/:documentClassification`,
  errorHandler(getRequestDocumentsAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents-analysis/approve`,
  errorHandler(approveDocumentAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents-analysis/reject`,
  errorHandler(rejectDocumentsAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/social-analysis/execute`,
  errorHandler(executeSocialAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/social-analysis/approve`,
  errorHandler(approveSocialAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/social-analysis/reject`,
  errorHandler(rejectSocialAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/risk-analysis/approve`,
  errorHandler(approveRiskAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/risk-analysis/reject`,
  errorHandler(rejectRiskAnalysisResource)
);

secureRouter.post(`${ADMISSION_PATH}/:requestId/retry-analysis`, errorHandler(retryAnalysisResource));

secureRouter.post(`${ADMISSION_PATH}/:requestId/home-visit`, errorHandler(createHomeVisitResource));
secureRouter.get(`${ADMISSION_PATH}/:requestId/home-visit`, errorHandler(getHomeVisitResource));
secureRouter.patch(`${ADMISSION_PATH}/:requestId/home-visit`, errorHandler(updateRequestHomeVisitResource));
secureRouter.patch(
  `${ADMISSION_PATH}/:requestId/reset-home-visit-schedule-link-send-date`,
  errorHandler(resetHomeVisitScheduleLinkSendDate)
);

secureRouter.get(
  `${ADMISSION_PATH}/:requestId/earnings/:platform`,
  errorHandler(getPlatformEarningsResource)
);
secureRouter.get(`${ADMISSION_PATH}/:requestId/metrics/:platform`, errorHandler(getPlatformMetricsResource));
secureRouter.post(
  `${ADMISSION_PATH}/:requestId/start-earnings-analysis`,
  errorHandler(startEarningsAnalysisResource)
);

secureRouter.patch(
  `${ADMISSION_PATH}/:requestId/update-platform-metric`,
  errorHandler(upadtePlatformMetricResource)
);

secureRouter.get(`/fetchVehiceViolation/:plate`, errorHandler(fetchVehiceViolation));
secureRouter.get(`/fetchVehiceAmountViolation/:plate`, errorHandler(fetchVehiceAmountViolation));

// Events
publicRouter.get(`/events/:entityType/:entityId`, errorHandler(getEventsResource));

/*
 * Public routes
 */

// Driver app
publicRouter.get(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId`,
  errorHandler(getPublicAdmissionRequestResource)
);
publicRouter.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/palenca-accounts`,
  errorHandler(addPalencaAccount)
);
publicRouter.patch(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/personal-data`,
  errorHandler(updateRequestPersonalDataResource)
);
publicRouter.patch(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/documents`,
  errorHandler(updateRequestDocumentsResource)
);
publicRouter.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/start-earnings-analysis`,
  errorHandler(startEarningsAnalysisResource)
);
publicRouter.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/aval-data`,
  errorHandler(addAvalDataResource)
);
publicRouter.patch(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/aval-data-with-documents`,
  uploadDiskStorage.fields(fields.avalFiles),
  errorHandler(addAvalDataResourceWithDocs)
);
publicRouter.get(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/check-earnings-analysis`,
  errorHandler(checkEarningsAnalysisOrQueueResource)
);

// Media
publicRouter.post(
  '/media/upload',
  uploadDiskStorage.fields(fields.homeVisitDoc),
  errorHandler(mediaUploadResource)
);

// Queue tasks
publicRouter.post('/tasks/retrieve-earnings/', errorHandler(taskRetrieveEarningsResource));
publicRouter.post('/tasks/retrieve-metrics', errorHandler(taskRetrieveMetricsResource));

// Webhooks
publicRouter.post('/webhooks/palenca', errorHandler(palencaWebhookResource));

routerWithoutAuthentication.get(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/:date/calendar/slots/get-available-slots`,
  errorHandler(getAvailableSlots)
);

routerWithoutAuthentication.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/calendar/appointment/create`,
  errorHandler(createAppointment)
);

routerWithoutAuthentication.get(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/calendar/appointment`,
  errorHandler(getAppointmentForDriverWebApp)
);

routerWithoutAuthentication.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/calendar/appointment/cancel`,
  errorHandler(cancelAppointment)
);

// Merge routers
const router = Router();
router.use(routerWithoutAuthentication);
router.use(publicRouter);
router.use(secureRouter);

export const cleanRouter = router;
