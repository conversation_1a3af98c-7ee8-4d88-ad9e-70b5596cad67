import { CurrencyCode } from '../domain/enums';

interface Error {
  code: number;
  message: string;
}

interface Pagination {
  page: number;
  items_per_page: number;
  total_items: number;
  total_pages: number;
}

export interface PalencaResponseEnvelope<T> {
  success: boolean;
  error: Error | null;
  data: T | null;
  pagination?: Pagination;
}

export interface PalencaEarning {
  amount: number;
  currency: CurrencyCode;
  earning_date: string;
  cash_amount: number;
  count_trips: number;
}

export interface EarningsResponsePayload {
  earnings: PalencaEarning[];
  account_id: string;
}

export interface EarningsRequestPayload {
  start_date: string;
  end_date: string;
  options: {
    items_per_page: number;
    page: number;
  };
}

export interface PalencaMetrics {
  acceptance_rate: number;
  cancellation_rate: number;
  rating: number;
  lifetime_trips: number;
  time_since_first_trip: string;
  // thumps_up: string;
  // level_name: string;
  // debt_pending: string | null;
  // debt_paid: string | null;
  activation_status: string;
}
export interface ProfileResponsePayload {
  account_id: string;
  metrics_info: PalencaMetrics;
  // We currently don't retrieve this data
  // profile_info
  // vehicle_info
  // bank_info
}
