/* eslint-disable max-params */
import {
  admissionRequestMongoAdapter,
  earningMongoAdapter,
  metricMongoAdapter,
  documentMongoAdapter,
  weeklyEarningMongoAdapter,
  requestDocumentAdapter,
  requestDocumentWithMediaAdapter,
  requestDocumentsAnalysisWithMediaAdapter,
  homeVisitMongoAdapter,
  eventMongoAdapter,
  riskAnalysisDataMongoAdapter,
  PlatformMetricMongoAdapter,
} from '../domain/adapters';
import {
  AdmissionRequest,
  Document,
  Earning,
  EarningsAnalysis,
  Event,
  HomeVisit,
  Metric,
  ModelResult,
  PaginationSearchOptions,
  PalencaAccount,
  PlatformMetric,
  RequestAvalData,
  RequestDocument,
  RequestDocumentsAnalysis,
  RequestPersonalData,
  RiskAnalysisData,
  Scorecard,
  ScorecardConfig,
  WeeklyEarning,
} from '../domain/entities';
import {
  AdmissionRequestMongo,
  EarningsAnalysisMongoI,
  ModelScoresMongoI,
  PalencaAccountMongoI,
  RequestAvalDataMongoI,
  RequestDocumentsMongoI,
  RequestHomeVisitMongoI,
  RequestPersonalDataMongoI,
  RiskAnalysisMongoI,
} from '../../models/admissionRequestSchema';
import { EarningMongo, IWeeklyEarning } from '../../models/earningSchema';
import { MetricMongo, MetricMongoI } from '../../models/metricsSchema';
import DocumentMongo from '../../models/documentSchema';
import { EventMongo } from '../../models/eventSchema';
import {
  AdmissionRequestDocumentType,
  AdmissionRequestRejectionReason,
  AdmissionRequestStatus,
  EntityType,
  GigPlatform,
  MediaStatus,
  RequestDocumentStatus,
  RequestDocumentsAnalysisStatus,
  ScorecardVersion,
  ScorecardVariableName,
  AnalysisStatus,
  PalencaAccountStatus,
  RequestPersonalDataStepStatus,
  DocumentClassification,
  AdmissionRequestAdditionalDocumentType,
  MLModels,
} from '../domain/enums';
import {
  AdmissionRequestNotFoundException,
  AlreadyExistException,
  CouldNotFoundPlatformMetricsException,
  CouldNotRetrieveScorecardConfigException,
  PlatformMetricNotFoundException,
  RequestDocumentNotFoundException,
  RiskAnalysisDataNotFoundException,
} from '../errors/exceptions';
import { Types } from 'mongoose';
import { repoGetMediaSignedUrl } from './s3Repositories';
import { RiskAnalysisDataMongo } from '../../models/riskAnalysisDataSchema';
import { scorecardConfigDB } from '../lib/scorecard';
import { logger } from '../lib/logger';
import { homeVisitDefaultObject } from '../lib/utils';
import { HUBSPOT_TOKEN } from '@/constants';

export const repoInsertAdmissionRequest = async (
  admissionRequest: AdmissionRequest
): Promise<AdmissionRequest> => {
  const personalData = admissionRequest.personalData;
  const documentsAnalysis = admissionRequest.documentsAnalysis;
  const palenca = admissionRequest.palenca;
  const earningsAnalysis = admissionRequest.earningsAnalysis;
  const riskAnalysis = admissionRequest.riskAnalysis;
  const newAdmissionRequest = await AdmissionRequestMongo.create({
    status: admissionRequest.status,
    rejectionReason: admissionRequest.rejectionReason,
    personalData: personalData,
    documentsAnalysis,
    palenca: palenca,
    earningsAnalysis: earningsAnalysis,
    riskAnalysis: riskAnalysis,
    source: admissionRequest.source,
    clientIpAddress: admissionRequest.clientIpAddress,
    agentId: admissionRequest.agentId,
  });

  return admissionRequestMongoAdapter(newAdmissionRequest);
};

export const repoAddPalencaAccountToAdmissionRequest = async (
  requestId: string,
  palencaAccount: PalencaAccount
): Promise<AdmissionRequest | null> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!admissionRequest) {
    return null;
  }

  admissionRequest.palenca.accounts.push({
    accountId: palencaAccount.accountId,
    platform: palencaAccount.platform,
    earnings: palencaAccount.earnings,
    metrics: palencaAccount.metrics,
    status: palencaAccount.status,
  } as PalencaAccountMongoI);

  const saved = await admissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoUpdatePalencaAccountStatus = async (
  requestId: string,
  accountId: string,
  platform: GigPlatform,
  status: PalencaAccountStatus
): Promise<AdmissionRequest | null> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!admissionRequest) {
    return null;
  }

  const accountFounded = admissionRequest.palenca.accounts.find((account) => {
    return account.accountId === accountId && account.platform === platform;
  });

  if (!accountFounded) {
    return null;
  }

  accountFounded.status = status;

  const saved = await admissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoUpdatePalencaAccountEarningsRetrievalStatus = async (
  requestId: string,
  accountId: string,
  platform: string,
  status: string
): Promise<AdmissionRequest | null> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!admissionRequest) {
    return null;
  }

  const accountFounded = admissionRequest.palenca.accounts.find((account) => {
    return account.accountId === accountId && account.platform === platform;
  });

  if (!accountFounded) {
    return null;
  }

  accountFounded.earnings.status = status;

  const saved = await admissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoUpdatePalencaAccountMetricsRetrievalStatus = async (
  requestId: string,
  accountId: string,
  platform: string,
  status: string
): Promise<AdmissionRequest | null> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!admissionRequest) {
    return null;
  }

  const accountFounded = admissionRequest.palenca.accounts.find((account) => {
    return account.accountId === accountId && account.platform === platform;
  });

  if (!accountFounded) {
    return null;
  }

  accountFounded.metrics.status = status;

  const saved = await admissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoSaveEarnings = async (earnings: Earning[]): Promise<null> => {
  const { requestId, platform } = earnings[0];
  // Insert only earnings that are not already in the database to avoid duplicates
  // We prevent that by using the requestId, and platform and earningDate
  const currentEarnings = await EarningMongo.find({
    requestId,
    platform,
  });

  // If there are no current earnings, insert all
  if (currentEarnings.length === 0) {
    await EarningMongo.insertMany(earnings);
    return null;
  }

  const currentEarningsEntities = currentEarnings.map((currentEarning) => {
    return earningMongoAdapter(currentEarning);
  });

  // If there are current earnings, insert only the ones that are not already in the database to avoid duplicates
  const earningsToInsert = earnings.filter((earning) => {
    return !currentEarningsEntities.some((currentEarning) => {
      return (
        currentEarning.requestId === earning.requestId &&
        currentEarning.platform === earning.platform &&
        currentEarning.earningDate.toString() === earning.earningDate.toString()
      );
    });
  });

  await EarningMongo.insertMany(earningsToInsert);
  return null;
};

export const repoSaveMetric = async (metric: Metric): Promise<Metric | null> => {
  const { requestId, platform } = metric;
  // Insert only if metric is  not already in the database to avoid duplicates
  // We prevent that by using the requestId, and platform
  const currentMetrics = await MetricMongo.find({
    requestId,
    platform,
  });

  // If there are no current metrics insert it
  if (currentMetrics.length === 0) {
    const newMetric = new MetricMongo(metric);
    await newMetric.save();
    const savedMetricId = newMetric._id;
    const savedMetric = await MetricMongo.findById(savedMetricId);
    if (savedMetric) {
      return metricMongoAdapter(savedMetric);
    }
  }
  return null;
};

export const repoInsertDocument = async (document: Document): Promise<Document> => {
  const newDocument = await DocumentMongo.create({
    originalName: document.fileName,
    path: document.path,
    type: document.type,
    status: document.status,
    mimeType: document.mimeType,
  });

  const signedUrl = await repoGetMediaSignedUrl(newDocument.path);

  logger.info(`[repoInsertDocument] document saved with signedUrl ${signedUrl}`);

  return documentMongoAdapter(newDocument, signedUrl);
};

export const repoUpdateBatchDocumentStatus = async (
  documentIds: string[],
  status: MediaStatus
): Promise<Document[] | null> => {
  const documents = await DocumentMongo.find({
    _id: {
      $in: documentIds,
    },
  });

  if (documents.length === 0) {
    return null;
  }

  await DocumentMongo.updateMany(
    {
      _id: {
        $in: documentIds,
      },
    },
    {
      status,
    }
  );

  const updatedDocumentsEntities = await DocumentMongo.find({
    _id: {
      $in: documentIds,
    },
  });

  return updatedDocumentsEntities.map((document) => {
    return documentMongoAdapter(document);
  });
};

const isRequiredPersonalDataAvailable = (personalData: RequestPersonalDataMongoI): boolean => {
  return (
    personalData.firstName.length != 0 &&
    personalData.lastName != null &&
    personalData.lastName.length != 0 &&
    personalData.email.length != 0 &&
    personalData.phone.length != 0 &&
    personalData.birthdate != null &&
    personalData.birthdate.length != 0 &&
    personalData.taxId != null &&
    personalData.taxId.length != 0 &&
    personalData.nationalId != null &&
    personalData.nationalId.length != 0 &&
    personalData.postalCode != null &&
    personalData.postalCode.length != 0 &&
    personalData.city != null &&
    personalData.city.length != 0 &&
    personalData.state != null &&
    personalData.state.length != 0 &&
    personalData.neighborhood != null &&
    personalData.neighborhood.length != 0 &&
    personalData.street != null &&
    personalData.street.length != 0
  );
};

export const repoUpdateRequestPersonalData = async (
  requestId: string,
  requestPersonalData: RequestPersonalData
): Promise<AdmissionRequest> => {
  if (requestPersonalData.nationalId) {
    const existedPersonalData = await AdmissionRequestMongo.findOne({
      'personalData.nationalId': requestPersonalData.nationalId,
    });
    if (existedPersonalData) {
      logger.info(
        `[repoUpdateRequestPersonalData] - User found with CURP ${requestPersonalData.nationalId} and id ${existedPersonalData.id}, and requestId is ${requestId}`
      );
      if (existedPersonalData.id != requestId) {
        throw new AlreadyExistException();
      }
    }
  }

  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!currentAdmissionRequest) {
    logger.info(`User not found with id ${requestId}`);
    throw new AdmissionRequestNotFoundException();
  }

  // Explicitly update the to avoid updating fields that should not be updated and improve readability

  if (requestPersonalData.firstName) {
    currentAdmissionRequest.personalData.firstName = requestPersonalData.firstName;
  }

  if (requestPersonalData.lastName) {
    currentAdmissionRequest.personalData.lastName = requestPersonalData.lastName;
  }

  if (requestPersonalData.email) {
    currentAdmissionRequest.personalData.email = requestPersonalData.email;
  }

  if (requestPersonalData.phone) {
    currentAdmissionRequest.personalData.phone = requestPersonalData.phone;
  }

  if (requestPersonalData.birthdate) {
    currentAdmissionRequest.personalData.birthdate = requestPersonalData.birthdate;
  }

  if (requestPersonalData.taxId) {
    currentAdmissionRequest.personalData.taxId = requestPersonalData.taxId;
  }

  if (requestPersonalData.nationalId) {
    currentAdmissionRequest.personalData.nationalId = requestPersonalData.nationalId;
  }

  if (requestPersonalData.postalCode) {
    currentAdmissionRequest.personalData.postalCode = requestPersonalData.postalCode;
  }

  if (requestPersonalData.city) {
    currentAdmissionRequest.personalData.city = requestPersonalData.city;
  }

  if (requestPersonalData.state) {
    currentAdmissionRequest.personalData.state = requestPersonalData.state;
  }

  if (requestPersonalData.neighborhood) {
    currentAdmissionRequest.personalData.neighborhood = requestPersonalData.neighborhood;
  }

  if (requestPersonalData.street) {
    currentAdmissionRequest.personalData.street = requestPersonalData.street;
  }

  if (requestPersonalData.streetNumber) {
    currentAdmissionRequest.personalData.streetNumber = requestPersonalData.streetNumber;
  }

  if (requestPersonalData.department) {
    currentAdmissionRequest.personalData.department = requestPersonalData.department;
  }

  if (requestPersonalData.ssn) {
    currentAdmissionRequest.personalData.ssn = requestPersonalData.ssn;
  }

  if (requestPersonalData.rideShareTotalRides) {
    currentAdmissionRequest.personalData.rideShareTotalRides = requestPersonalData.rideShareTotalRides;
  }

  if (requestPersonalData.avgEarningPerWeek) {
    currentAdmissionRequest.personalData.avgEarningPerWeek = requestPersonalData.avgEarningPerWeek;
  }

  if (requestPersonalData.termsAndConditions) {
    currentAdmissionRequest.personalData.termsAndConditions = requestPersonalData.termsAndConditions;
  }

  if (requestPersonalData.dataPrivacyConsentForm) {
    currentAdmissionRequest.personalData.dataPrivacyConsentForm = requestPersonalData.dataPrivacyConsentForm;
  }

  if (requestPersonalData.nationality) {
    currentAdmissionRequest.personalData.nationality = requestPersonalData.nationality;
  }

  if (requestPersonalData.age) {
    currentAdmissionRequest.personalData.age = requestPersonalData.age;
  }

  if (requestPersonalData.occupation) {
    currentAdmissionRequest.personalData.occupation = requestPersonalData.occupation;
  }

  if (requestPersonalData.homePhone) {
    currentAdmissionRequest.personalData.homePhone = requestPersonalData.homePhone;
  }

  if (requestPersonalData.timeInResidency) {
    currentAdmissionRequest.personalData.timeInResidency = requestPersonalData.timeInResidency;
  }

  if (requestPersonalData.municipality) {
    currentAdmissionRequest.personalData.municipality = requestPersonalData.municipality;
  }

  if (requestPersonalData.maritalStatus) {
    currentAdmissionRequest.personalData.maritalStatus = requestPersonalData.maritalStatus;
  }

  if (requestPersonalData.dependents) {
    currentAdmissionRequest.personalData.dependents = requestPersonalData.dependents;
  }

  if (requestPersonalData.spouseOrPartnerIncome) {
    currentAdmissionRequest.personalData.spouseOrPartnerIncome = requestPersonalData.spouseOrPartnerIncome;
  }

  if (requestPersonalData.partnerSourceOfIncome) {
    currentAdmissionRequest.personalData.partnerSourceOfIncome = requestPersonalData.partnerSourceOfIncome;
  }

  if (requestPersonalData.partnerName) {
    currentAdmissionRequest.personalData.partnerName = requestPersonalData.partnerName;
  }

  if (requestPersonalData.partnerPhone) {
    currentAdmissionRequest.personalData.partnerPhone = requestPersonalData.partnerPhone;
  }

  if (requestPersonalData.noOfDependents) {
    currentAdmissionRequest.personalData.noOfDependents = requestPersonalData.noOfDependents;
  }

  if (requestPersonalData.dependendsInfo) {
    currentAdmissionRequest.personalData.dependendsInfo = requestPersonalData.dependendsInfo;
  }

  if (requestPersonalData.ownACar) {
    currentAdmissionRequest.personalData.ownACar = requestPersonalData.ownACar;
  }

  if (requestPersonalData.carLeasingtime) {
    currentAdmissionRequest.personalData.carLeasingtime = requestPersonalData.carLeasingtime;
  }

  if (requestPersonalData.carModel) {
    currentAdmissionRequest.personalData.carModel = requestPersonalData.carModel;
  }

  if (requestPersonalData.carMake) {
    currentAdmissionRequest.personalData.carMake = requestPersonalData.carMake;
  }

  if (requestPersonalData.ownDebt) {
    currentAdmissionRequest.personalData.ownDebt = requestPersonalData.ownDebt;
  }

  if (requestPersonalData.outStandingDebt) {
    currentAdmissionRequest.personalData.outStandingDebt = requestPersonalData.outStandingDebt;
  }

  if (requestPersonalData.doesDebtAffectPersonalFinance) {
    currentAdmissionRequest.personalData.doesDebtAffectPersonalFinance =
      requestPersonalData.doesDebtAffectPersonalFinance;
  }

  if (requestPersonalData.references) {
    currentAdmissionRequest.personalData.references = requestPersonalData.references;
  }

  if (requestPersonalData.learnAboutOcn) {
    currentAdmissionRequest.personalData.learnAboutOcn = requestPersonalData.learnAboutOcn;
  }

  if (requestPersonalData.doesItApplyToElectricCars) {
    currentAdmissionRequest.personalData.doesItApplyToElectricCars =
      requestPersonalData.doesItApplyToElectricCars;
  }

  if (requestPersonalData.ficoScore) {
    currentAdmissionRequest.personalData.ficoScore = requestPersonalData.ficoScore;
  }

  if (requestPersonalData.criminalBackgroundCheck) {
    currentAdmissionRequest.personalData.criminalBackgroundCheck =
      requestPersonalData.criminalBackgroundCheck;
  }

  if (requestPersonalData.motorVehicleRecordCheck) {
    currentAdmissionRequest.personalData.motorVehicleRecordCheck =
      requestPersonalData.motorVehicleRecordCheck;
  }

  if (requestPersonalData.vehicleSelected) {
    currentAdmissionRequest.personalData.vehicleSelected = requestPersonalData.vehicleSelected;
  }

  if (requestPersonalData.privacyPolicy) {
    currentAdmissionRequest.personalData.privacyPolicy = requestPersonalData.privacyPolicy;
  }

  if (requestPersonalData.ocnBackgroundAndCreditCheckForApplication) {
    currentAdmissionRequest.personalData.ocnBackgroundAndCreditCheckForApplication =
      requestPersonalData.ocnBackgroundAndCreditCheckForApplication;
  }

  if (isRequiredPersonalDataAvailable(currentAdmissionRequest.personalData)) {
    logger.info(`User with id ${requestId} personal data status set to completed`);
    currentAdmissionRequest.personalData.status = RequestPersonalDataStepStatus.completed;
  }

  const saved = await currentAdmissionRequest.save();
  logger.info(`User with id ${requestId} personal data saved successfully`);

  return admissionRequestMongoAdapter(saved);
};

export const repoUpdateRequestDocuments = async (
  requestId: string,
  requestDocuments: RequestDocument[]
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    logger.info(`[repoUpdateRequestDocuments] admission request not found with id ${requestId}`);
    throw new AdmissionRequestNotFoundException();
  }

  const documents = currentAdmissionRequest.documentsAnalysis.documents;

  requestDocuments.forEach((requestDocument) => {
    const document = documents.find((elem) => {
      return elem.type === requestDocument.type;
    });

    if (document) {
      document.status = requestDocument.status;
      document.mediaId = new Types.ObjectId(requestDocument.mediaId!);
      logger.info(
        `[repoUpdateRequestDocuments] document ${document.type} status updated to ${document.status} of requestId ${requestId}`
      );
    } else {
      const newDocument = {
        mediaId: new Types.ObjectId(requestDocument.mediaId!),
        status: requestDocument.status,
        type: requestDocument.type,
      };
      documents.push(newDocument as RequestDocumentsMongoI);
      logger.info(
        `[repoUpdateRequestDocuments] new document ${newDocument.type} added with status ${newDocument.status} for requestId ${requestId}`
      );
    }
  });

  const saved = await currentAdmissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoCreateHomeVisit = async (
  requestId: string,
  homeVisit: HomeVisit
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  currentAdmissionRequest.homeVisit = {
    status: homeVisit.status,
    isAddressProvidedByApplicant: homeVisit.isAddressProvidedByApplicant,
    residentOwnershipStatus: homeVisit.residentOwnershipStatus,
    hasGarage: homeVisit.hasGarage,
    comments: homeVisit.comments,
    images: homeVisit.images,
    visitDate: homeVisit.visitDate,
    responsible: homeVisit.responsible,
  } as RequestHomeVisitMongoI;

  const saved = await currentAdmissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoUpdateHomeVisit = async (
  requestId: string,
  homeVisit: HomeVisit
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  if (!currentAdmissionRequest.homeVisit) {
    currentAdmissionRequest.homeVisit = homeVisitDefaultObject as unknown as RequestHomeVisitMongoI;
    await currentAdmissionRequest.save();
  }

  if (homeVisit.visitTime) {
    currentAdmissionRequest.homeVisit.visitTime = homeVisit.visitTime;
  }

  if (homeVisit.visitDate) {
    currentAdmissionRequest.homeVisit.visitDate = homeVisit.visitDate;
  }

  if (homeVisit.responsible) {
    currentAdmissionRequest.homeVisit.responsible = homeVisit.responsible;
  }

  if (homeVisit.status) {
    currentAdmissionRequest.homeVisit.status = homeVisit.status;
  }

  if (homeVisit.isAddressProvidedByApplicant) {
    currentAdmissionRequest.homeVisit.isAddressProvidedByApplicant = homeVisit.isAddressProvidedByApplicant;
  }

  if (homeVisit.residentOwnershipStatus) {
    currentAdmissionRequest.homeVisit.residentOwnershipStatus = homeVisit.residentOwnershipStatus;
  }

  if (homeVisit.hasGarage) {
    currentAdmissionRequest.homeVisit.hasGarage = homeVisit.hasGarage;
  }

  if (homeVisit.comments) {
    currentAdmissionRequest.homeVisit.comments = homeVisit.comments;
  }

  if (homeVisit.images) {
    currentAdmissionRequest.homeVisit.images = homeVisit.images;
  }

  if (homeVisit.houseInformation) {
    currentAdmissionRequest.homeVisit.houseInformation = homeVisit.houseInformation;
  }

  if (homeVisit.proofOfPropertyOwnership) {
    currentAdmissionRequest.homeVisit.proofOfPropertyOwnership = homeVisit.proofOfPropertyOwnership;
  }

  if (homeVisit.visitorEmailAddress) {
    currentAdmissionRequest.homeVisit.visitorEmailAddress = homeVisit.visitorEmailAddress;
  }

  if (homeVisit.doesProofOfAddressMatchLocation) {
    currentAdmissionRequest.homeVisit.doesProofOfAddressMatchLocation =
      homeVisit.doesProofOfAddressMatchLocation;
  }

  if (homeVisit.characteristicsOfGarage) {
    currentAdmissionRequest.homeVisit.characteristicsOfGarage = homeVisit.characteristicsOfGarage;
  }

  if (homeVisit.behaviourOfCustomerDuringCall) {
    currentAdmissionRequest.homeVisit.behaviourOfCustomerDuringCall = homeVisit.behaviourOfCustomerDuringCall;
  }

  if (homeVisit.homeVisitStepsStatus) {
    currentAdmissionRequest.homeVisit.homeVisitStepsStatus = homeVisit.homeVisitStepsStatus;
  }

  if (homeVisit.reasonOfRejection) {
    currentAdmissionRequest.homeVisit.reasonOfRejection = homeVisit.reasonOfRejection;
  }

  if (homeVisit.suggestedStatus && homeVisit.status) {
    currentAdmissionRequest.homeVisit.suggestedStatus = homeVisit.suggestedStatus;
  }

  if (homeVisit.statusReason) {
    currentAdmissionRequest.homeVisit.statusReason = homeVisit.statusReason;
  }
  const saved = await currentAdmissionRequest.save();

  return admissionRequestMongoAdapter(saved!);
};

export const repoUpdateAdmissionRequestStatus = async (
  requestId: string,
  status: AdmissionRequestStatus,
  rejectedReason?: AdmissionRequestRejectionReason
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  currentAdmissionRequest.status = status;

  if (rejectedReason) {
    currentAdmissionRequest.rejectionReason = rejectedReason;
  }

  const saved = await currentAdmissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoRetrieveWeeklyEarnings = async (
  requestId: string,
  platform?: GigPlatform
): Promise<WeeklyEarning[]> => {
  const objectId = new Types.ObjectId(requestId);

  const matchCondition: { [key: string]: any } = { requestId: objectId };

  if (platform) {
    matchCondition.platform = platform;
  }

  const query = EarningMongo.aggregate<IWeeklyEarning>([
    {
      $match: matchCondition,
    },
    {
      $addFields: {
        totalEarnings: { $add: ['$amount', '$cashAmount'] },
        week: { $isoWeek: '$earningDate' },
        year: { $isoWeekYear: '$earningDate' },
      },
    },
    {
      $group: {
        _id: { week: '$week', year: '$year' },
        dailyEarnings: {
          $push: {
            amount: '$totalEarnings',
            countTrips: '$countTrips',
            earningDate: '$earningDate',
            currency: '$currency',
          },
        },
        totalAmount: { $sum: '$totalEarnings' },
        totalTrips: { $sum: '$countTrips' },
        currency: { $first: '$currency' },
      },
    },
    {
      $addFields: {
        fromDate: { $dateFromParts: { isoWeekYear: '$_id.year', isoWeek: '$_id.week', isoDayOfWeek: 1 } },
        toDate: { $dateFromParts: { isoWeekYear: '$_id.year', isoWeek: '$_id.week', isoDayOfWeek: 7 } },
      },
    },
    {
      $sort: { '_id.year': -1, '_id.week': -1 },
    },
    {
      $project: {
        _id: 0,
        currency: 1,
        week: '$_id.week',
        year: '$_id.year',
        dailyEarnings: 1,
        totalAmount: 1,
        totalTrips: 1,
        fromDate: 1,
        toDate: 1,
      },
    },
  ]);

  const weeklyEarnings = await query.exec();
  return weeklyEarnings.map((weeklyEarning) => {
    return weeklyEarningMongoAdapter(weeklyEarning);
  });
};

export const repoSaveEarningsAnalysis = async (
  requestId: string,
  earningsAnalysis: EarningsAnalysis
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  currentAdmissionRequest.earningsAnalysis = {
    status: earningsAnalysis.status,
    totalEarnings: earningsAnalysis.totalEarnings,
    earnings: earningsAnalysis.earnings,
    platforms: earningsAnalysis.platforms,
  } as EarningsAnalysisMongoI;

  const saved = await currentAdmissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoGetPaginatedAdmissionRequests = async (
  country: string,
  q: string,
  pagination: PaginationSearchOptions,
  paramStatus?: string
): Promise<[number, AdmissionRequest[]]> => {
  const { page, itemsPerPage } = pagination;

  const countryCondition = { 'personalData.country': { $regex: country, $options: 'i' } };

  let query: any;
  if (paramStatus) {
    query = {
      $and: [
        countryCondition,
        {
          $or: [
            { 'personalData.firstName': { $regex: q, $options: 'i' } },
            { 'personalData.lastName': { $regex: q, $options: 'i' } },
            { 'personalData.nationalId': { $regex: q, $options: 'i' } },
          ],
        },
        { status: { $regex: paramStatus, $options: 'i' } },
        {
          $or: [{ convertedToAssociate: false }, { convertedToAssociate: { $exists: false } }],
        },
      ],
    };
  } else {
    query = {
      $and: [
        countryCondition,
        {
          $or: [
            { 'personalData.firstName': { $regex: q || '', $options: 'i' } },
            { 'personalData.lastName': { $regex: q || '', $options: 'i' } },
            { 'personalData.email': { $regex: q || '', $options: 'i' } },
            { 'personalData.phone': { $regex: q || '', $options: 'i' } },
            { status: { $regex: paramStatus || q || '', $options: 'i' } },
          ],
        },
      ],
    };
  }

  const baseQuery = AdmissionRequestMongo.find(query);

  const total = await AdmissionRequestMongo.countDocuments(baseQuery.getFilter());
  const admissionRequests = await baseQuery
    .skip((page - 1) * itemsPerPage)
    .limit(itemsPerPage)
    .sort({ createdAt: -1 })
    .exec();

  const entities = admissionRequests.map((admissionRequest) => {
    return admissionRequestMongoAdapter(admissionRequest);
  });

  return [total, entities];
};

export const repoGetRequestDocumentAnalysisWithMedia = async (
  requestId: string,
  documentClassification: DocumentClassification
): Promise<RequestDocumentsAnalysis> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!admissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  // Type will be undefined for US region because we don't have additional documents
  const documents: Array<RequestDocumentsMongoI> =
    documentClassification === DocumentClassification.all ? admissionRequest.documentsAnalysis.documents : [];

  if (documentClassification !== DocumentClassification.all) {
    const admissionRequestDocumentTypeValues: Array<string> = Object.values(
      documentClassification === DocumentClassification.additional
        ? AdmissionRequestAdditionalDocumentType
        : AdmissionRequestDocumentType
    );
    admissionRequest.documentsAnalysis.documents.forEach((document) => {
      if (admissionRequestDocumentTypeValues.includes(document.type)) {
        documents.push(document);
      }
    });
  }

  const documentsWithMedia = await Promise.all(
    documents.map(async (document) => {
      if (!document.mediaId) {
        return requestDocumentAdapter(document);
      }
      const media = await DocumentMongo.findById(document.mediaId);

      if (!media) {
        return requestDocumentAdapter(document);
      }

      const signedUrl = await repoGetMediaSignedUrl(media.path);
      const documentWithMedia = documentMongoAdapter(media, signedUrl);
      return requestDocumentWithMediaAdapter(document, documentWithMedia);
    })
  );

  return requestDocumentsAnalysisWithMediaAdapter(
    admissionRequest.documentsAnalysis.status as RequestDocumentsAnalysisStatus,
    documentsWithMedia
  );
};

export const repoGetAdmissionRequestById = async (id: string): Promise<AdmissionRequest> => {
  const admissionRequest = await AdmissionRequestMongo.findById(id);

  if (!admissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  const admissionRequestResponse = admissionRequestMongoAdapter(admissionRequest);

  // Sending type all to get the all available media in the documents analysis
  admissionRequestResponse.documentsAnalysis = await repoGetRequestDocumentAnalysisWithMedia(
    id,
    DocumentClassification.all
  );
  let hilos = null;
  if (admissionRequest?.hubspot?.id) {
    try {
      const response = await fetch(
        `https://api.hubapi.com/crm/v3/objects/contacts/${admissionRequest.hubspot.id}?properties=hilos_whatsapp_link`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${HUBSPOT_TOKEN}`,
          },
        }
      );
      if (response.ok) {
        const data = await response.json();
        hilos = data?.properties?.hilos_whatsapp_link; // Asignar la respuesta a admissionRequest
      } else {
        console.error('Error fetching HubSpot data:', response.statusText);
      }
    } catch (error) {
      console.error('Error in HubSpot API request:', error);
    }
  }

  admissionRequestResponse.hilos = hilos;
  return admissionRequestResponse;
};

export const repoUpdateRequestDocumentStatus = async (
  requestId: string,
  documentType: AdmissionRequestDocumentType,
  status: RequestDocumentStatus
): Promise<null> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!admissionRequest) {
    logger.info(`[repoUpdateRequestDocumentStatus] admission request not found for id ${requestId}`);
    throw new AdmissionRequestNotFoundException();
  }

  const founded = admissionRequest.documentsAnalysis.documents.find((document) => {
    return document.type === documentType;
  });

  if (!founded) {
    logger.info(
      `[repoUpdateRequestDocumentStatus] document of type ${documentType} not found for id ${requestId}`
    );
    throw new RequestDocumentNotFoundException();
  }

  founded.status = status;
  await admissionRequest.save();

  logger.info(
    `[repoUpdateRequestDocumentStatus] document of type ${documentType} found and status updated to ${status} for id ${requestId}`
  );

  return null;
};

export const repoRejectRequestDocument = async (
  requestId: string,
  documentType: AdmissionRequestDocumentType
): Promise<null> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!admissionRequest) {
    logger.info(`[repoRejectRequestDocument] admission request not found for id ${requestId}`);
    throw new AdmissionRequestNotFoundException();
  }

  const founded = admissionRequest.documentsAnalysis.documents.find((document) => {
    return document.type === documentType;
  });

  if (!founded) {
    logger.info(`[repoRejectRequestDocument] document of type ${documentType} not found for id ${requestId}`);
    throw new RequestDocumentNotFoundException();
  }

  founded.status = RequestDocumentStatus.rejected;

  await admissionRequest.save();

  logger.info(
    `[repoRejectRequestDocument] document of type ${documentType} found and status updated to ${RequestDocumentStatus.rejected} for id ${requestId}`
  );

  return null;
};

export const repoRequestUpdateRequestDocumentsAnalysisStatus = async (
  requestId: string,
  status: RequestDocumentsAnalysisStatus
): Promise<null> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!admissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  admissionRequest.documentsAnalysis.status = status;

  await admissionRequest.save();

  return null;
};

export const repoGetHomeVisitWithMedia = async (requestId: string): Promise<HomeVisit> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!admissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  const homeVisit = admissionRequest.homeVisit;

  if (!homeVisit) {
    return homeVisit;
  }

  const images = homeVisit.images;

  const imagesWithMedia = await Promise.all(
    images.map(async (image) => {
      const media = await DocumentMongo.findById(image);

      if (!media) {
        return null;
      }

      const signedUrl = await repoGetMediaSignedUrl(media.path);
      const documentWithMedia = documentMongoAdapter(media, signedUrl);
      return documentWithMedia;
    })
  );

  const proofOfPropertyOwnership = homeVisit.proofOfPropertyOwnership;
  const proofOfPropertyOwnershipWithMedia = await Promise.all(
    proofOfPropertyOwnership.map(async (image) => {
      const media = await DocumentMongo.findById(image);

      if (!media) {
        return null;
      }

      const signedUrl = await repoGetMediaSignedUrl(media.path);
      const documentWithMedia = documentMongoAdapter(media, signedUrl);
      return documentWithMedia;
    })
  );

  const filteredImagesWithMedia = imagesWithMedia.filter((document) => document !== null) as Document[];
  const filteredproofOfPropertyOwnershipWithMedia = proofOfPropertyOwnershipWithMedia.filter(
    (document) => document !== null
  ) as Document[];

  return homeVisitMongoAdapter(homeVisit, [
    ...filteredImagesWithMedia,
    ...filteredproofOfPropertyOwnershipWithMedia,
  ]);
};

export const repoRetrievePalencaPlatformMetric = async (
  requestId: string,
  platform: GigPlatform
): Promise<Metric> => {
  const metric = await MetricMongo.findOne({ requestId, platform });

  if (!metric) {
    throw new CouldNotFoundPlatformMetricsException();
  }

  return metricMongoAdapter(metric);
};

export const repoRetrieveEvents = async (entityID: string, entityType: EntityType): Promise<Event[]> => {
  const events = await EventMongo.find({ entityId: entityID, entityType: entityType }).populate('user');

  return events.map((event) => {
    return eventMongoAdapter(event);
  });
};

export const repoSaveEvent = async (event: Event): Promise<Event> => {
  const newEvent = await EventMongo.create({
    user: event.userId,
    entityId: event.entityId,
    entityType: event.entityType,
    actionType: event.actionType,
    message: event.message,
  });

  return eventMongoAdapter(newEvent);
};

export const repoSaveEvents = async (events: Event[]): Promise<Event[]> => {
  // Map all the events to the mongo model before inserting them to
  // To map the userId to user
  const eventsMongo = events.map((event) => {
    return {
      user: event.userId,
      entityId: event.entityId,
      entityType: event.entityType,
      actionType: event.actionType,
      message: event.message,
    };
  });

  const newEvents = await EventMongo.insertMany(eventsMongo);

  return newEvents.map((event) => {
    return eventMongoAdapter(event);
  });
};

export const repoSaveOrReplaceRiskAnalysisData = async (
  requestId: string,
  variable: ScorecardVariableName,
  value: number | string
): Promise<RiskAnalysisData> => {
  const riskAnalysisData = await RiskAnalysisDataMongo.findOne({ requestId });

  //  If there is no riskAnalysisData, create it and add the variable
  if (!riskAnalysisData) {
    const newVariables = new Map<string, number | string>([[variable, value]]);
    const newData = await RiskAnalysisDataMongo.create({
      requestId,
      variables: newVariables,
    });

    return riskAnalysisDataMongoAdapter(newData);
  }

  // If there is riskAnalysisData, add the variable or replace it
  riskAnalysisData.variables.set(variable, value);
  await riskAnalysisData.save();

  return riskAnalysisDataMongoAdapter(riskAnalysisData);
};

export const repoSaveOrReplaceBatchRiskAnalysisData = async (
  requestId: string,
  variables: Map<ScorecardVariableName, number | string>
): Promise<RiskAnalysisData> => {
  const riskAnalysisData = await RiskAnalysisDataMongo.findOne({ requestId });

  //  If there is no riskAnalysisData, create it and add the variables
  if (!riskAnalysisData) {
    const newData = await RiskAnalysisDataMongo.create({
      requestId,
      variables,
    });

    return riskAnalysisDataMongoAdapter(newData);
  }

  // If there is riskAnalysisData, append the new variables or replace the existing ones
  variables.forEach((value, key) => {
    riskAnalysisData.variables.set(key, value);
  });

  await riskAnalysisData.save();

  return riskAnalysisDataMongoAdapter(riskAnalysisData);
};

/**
 * *** DEPRECATED ***
 */
export const repoGetRiskAnalysisData = async (requestId: string): Promise<RiskAnalysisData> => {
  const riskAnalysisData = await RiskAnalysisDataMongo.findOne({ requestId });

  if (!riskAnalysisData) {
    throw new RiskAnalysisDataNotFoundException();
  }

  return riskAnalysisDataMongoAdapter(riskAnalysisData);
};

/**
 * *** DEPRECATED ***
 */
export const repoGetScorecardConfig = async (version: ScorecardVersion): Promise<ScorecardConfig> => {
  const config = scorecardConfigDB.find((element) => {
    return element.version === version;
  });

  if (!config) {
    throw new CouldNotRetrieveScorecardConfigException();
  }

  return config;
};

export const repoRetriveMetricsForAllPlatforms = async (requestId: string): Promise<Metric[]> => {
  const metrics = await MetricMongo.find({ requestId });

  if (metrics.length === 0) {
    return [];
  }

  return metrics.map((metric) => {
    return metricMongoAdapter(metric);
  });
};

/**
 * *** DEPRECATED ***
 */
export const repoSaveRiskAnalysis = async (
  requestId: string,
  riskAnalysisStatus: AnalysisStatus,
  scorecard: Scorecard
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }

  currentAdmissionRequest.riskAnalysis = {
    status: riskAnalysisStatus,
    scorecardVersion: currentAdmissionRequest.riskAnalysis.scorecardVersion,
    scorecard: {
      totalScore: scorecard.totalScore,
      scaledScore: scorecard.scaledScore,
      details: scorecard.details,
      minScore: scorecard.minScore,
      maxScore: scorecard.maxScore,
      minScaledScore: scorecard.minScaledScore,
      maxScaledScore: scorecard.maxScaledScore,
    },
  } as RiskAnalysisMongoI;

  const saved = await currentAdmissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

// Save all model results in a single database operation
export const repoSaveAllModelResults = async (
  requestId: string,
  modelResults: { modelName: MLModels; result: ModelResult }[]
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);

  if (!currentAdmissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }
  if (!currentAdmissionRequest.modelScores) {
    currentAdmissionRequest.modelScores = {} as ModelScoresMongoI;
  }

  // Update all model scores at once
  for (const { modelName, result } of modelResults) {
    currentAdmissionRequest.modelScores.set(modelName, result);
  }

  // Save all changes in one operation
  const saved = await currentAdmissionRequest.save();

  return admissionRequestMongoAdapter(saved);
};

export const repoRetriveAllClientByDate = async (
  createdAtStartDate: Date,
  createdAtEndDate: Date
): Promise<AdmissionRequest[]> => {
  const admissionRequests = await AdmissionRequestMongo.find({
    createdAt: { $gte: createdAtStartDate, $lte: createdAtEndDate },
    isRequestLinkOpened: false,
  });
  if (!admissionRequests) {
    return [];
  }
  const entitie = admissionRequests.map((admissionRequest) => {
    return admissionRequestMongoAdapter(admissionRequest);
  });
  return entitie;
};

export const repoGetPlatformMetric = async (requestId: string): Promise<MetricMongoI> => {
  const platformMetric = await MetricMongo.findOne({ requestId });

  if (!platformMetric) {
    throw new PlatformMetricNotFoundException();
  }
  return platformMetric;
};

export const repoUpdatePlatformMetric = async (
  requestId: string,
  platformMetricPayload: PlatformMetric
): Promise<PlatformMetric> => {
  const platformMetric = await MetricMongo.findOne({ requestId });
  if (!platformMetric) {
    throw new PlatformMetricNotFoundException();
  }
  platformMetric.acceptanceRate = platformMetricPayload.acceptanceRate;
  platformMetric.cancellationRate = platformMetricPayload.cancellationRate;
  platformMetric.rating = platformMetricPayload.rating;
  platformMetric.lifetimeTrips = platformMetricPayload.lifetimeTrips;
  platformMetric.timeSinceFirstTrip = platformMetricPayload.timeSinceFirstTrip;

  const updatedPlatformMetric = await platformMetric.save();

  return PlatformMetricMongoAdapter(updatedPlatformMetric);
};

export const repoUpdateHomeVisitAppointmentSchedulingLinkSendDate = async (
  requestId: string,
  homeVisitAppointmentSchedulingLinkSendDate: Date
): Promise<void> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (admissionRequest) {
    admissionRequest.homeVisitScheduleLinkSendDate = homeVisitAppointmentSchedulingLinkSendDate;
    await admissionRequest.save();
  }
};

export const getHomeVisitScheduleLinkSendDate = async (requestId: string): Promise<Date> => {
  const admissionRequest = await AdmissionRequestMongo.findById(requestId).select(
    'homeVisitScheduleLinkSendDate'
  );
  if (!admissionRequest) {
    throw new AdmissionRequestNotFoundException();
  }
  return admissionRequest.homeVisitScheduleLinkSendDate;
};

export const repoAddRequestAvalData = async (requestId: string, requestAvalData: RequestAvalData) => {
  const currentAdmissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!currentAdmissionRequest) {
    logger.info(`User not found with id ${requestId}`);
    throw new AdmissionRequestNotFoundException();
  }
  currentAdmissionRequest.avalData = {
    name: requestAvalData.name,
    phone: requestAvalData.phone,
    email: requestAvalData.email,
    location: requestAvalData.location,
  } as RequestAvalDataMongoI;
  const saved = await currentAdmissionRequest.save();
  return admissionRequestMongoAdapter(saved);
};
