import { DRIVER_WEB_APP_URL, MAX_PALENCA_WAITING_TIME_MS, PALENCA_WIDGET_ID } from '../../constants';
import {
  AdmissionRequest,
  Document,
  PalencaWebhook,
  RequestDocument,
  RequestPersonalData,
  PalencaAccount,
  RequestPalenca,
  HomeVisit,
  EarningsAnalysis,
  WeeklyEarning,
  Pagination,
  PaginationSearchOptions,
  RequestDocumentsAnalysis,
  Metric,
  Event,
  RiskAnalysis,
  RiskAnalysisData,
  PalencaAccountRetrieval,
  PalencaJobRetrieveEarnings,
  PalencaJobRetrieveMetrics,
  PlatformMetric,
  RequestAvalData,
  ModelResult,
} from './entities';
import {
  AdmissionRequestRejectionReason,
  AdmissionRequestStatus,
  GigPlatform,
  MediaStatus,
  MediaType,
  PalencaAccountStatus,
  PalencaWebhookAction,
  EarningsAnalysisStatus,
  RequestDocumentStatus,
  RequestDocumentsAnalysisStatus,
  RequestPersonalDataStepStatus,
  AdmissionRequestDocumentType,
  HomeVisitStatus,
  EntityType,
  ActionType,
  AnalysisStatus,
  ScorecardVersion,
  ScorecardVariableName,
  VehicleType,
  VehicleCondition,
  PalencaRetrievalStatus,
  Country,
  AdmissionRequestDocumentTypeUS,
  AdmissionRequestAdditionalDocumentType,
  DriverSourceType,
  DocumentClassification,
  MLModels,
} from './enums';
import {
  repoInsertAdmissionRequest,
  repoGetAdmissionRequestById,
  repoSaveEarnings,
  repoSaveMetric,
  repoInsertDocument,
  repoUpdateBatchDocumentStatus,
  repoAddPalencaAccountToAdmissionRequest,
  repoUpdateRequestPersonalData,
  repoUpdateRequestDocuments,
  repoCreateHomeVisit,
  repoUpdateAdmissionRequestStatus,
  repoUpdatePalencaAccountEarningsRetrievalStatus,
  repoUpdatePalencaAccountMetricsRetrievalStatus,
  repoRetrieveWeeklyEarnings,
  repoSaveEarningsAnalysis,
  repoGetPaginatedAdmissionRequests,
  repoGetRequestDocumentAnalysisWithMedia,
  repoUpdateRequestDocumentStatus,
  repoRejectRequestDocument,
  repoRequestUpdateRequestDocumentsAnalysisStatus,
  repoGetHomeVisitWithMedia,
  repoRetrievePalencaPlatformMetric,
  repoRetrieveEvents,
  repoSaveEvent,
  repoSaveEvents,
  repoSaveOrReplaceRiskAnalysisData,
  repoSaveOrReplaceBatchRiskAnalysisData,
  repoRetriveMetricsForAllPlatforms,
  repoUpdatePalencaAccountStatus,
  repoRetriveAllClientByDate,
  repoUpdatePlatformMetric,
  repoUpdateHomeVisit,
  repoUpdateHomeVisitAppointmentSchedulingLinkSendDate,
  repoAddRequestAvalData,
  repoSaveAllModelResults,
} from '../data/mongoRepositories';
import {
  AdmissionRequestNotFoundException,
  AllRequiredDocumentsAreNotApproved,
  ModelExecutionFailedException,
  NotAllDocumentsApprovedException,
  RequestNotInDocumentsAnalysisStatusException,
  RequestNotInRiskAnalysisStatusException,
  RequestNotInSocialAnalysisStatusException,
} from '../errors/exceptions';
import { repoRetrieveEarnings, repoRetrieveMetric } from '../data/palencaRepositories';
import { repoUploadMedia } from '../data/s3Repositories';
import { repoAddRetrieveEarningsJob, repoAddRetrieveMetricsJob } from '../data/queueRepositories';
import { determineEarningsAnalysisStatus } from '../lib/earnings';
import { logger } from '../lib/logger';
import { generatePagination } from '../lib/pagination';
import { Types } from 'mongoose';
import {
  sendHomeVisitAppointmentApologyEmail,
  sendHomeVisitAppointmentCancelEmail,
  sendHomeVisitAppointmentFinishEmail,
  sendHomeVisitAppointmentNoShowMessageEmail,
  sendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo,
  sendHomeVisitAppointmentReminderEmailOneNightAgo,
  sendHomeVisitAppointmentScheduledEmail,
  sendHomeVisitAppointmentScheduleLinkEmail,
  sendHomeVisitFormApprovalEmail,
  sendReminderEmailToCustomerAfterRegistering,
} from '../../modules/platform_connections/emailFunc';
import { calculateEarningsAnalysis as updatedImplementationOfEarningsAnalysis } from '../../services/socialScoring/calculateEarningsAnalysis';
import { dealUpdate } from '../../services/hubspot';
import HubspotBatch from '../../models/hubspotBatch';
import { getMeetingIdFromMeetingLink, removeFile } from '../lib/utils';
import {
  sendHomeVisitAppointmentScheduleLink,
  sendHomeVisitAppointmentScheduledMessage,
  sendHomeVisitAppointmentFinishMessage,
  sendHomeVisitApprovalOrRejectionMessage,
  sendHomeVisitAppointmentReminderMessageOneNightAgoCron,
  sendHomeVisitAppointmentReminderMessageAboutFiveMinutes,
  sendHomeVisitAppointmentNoShowMessage,
  sendHomeVisitAppointmentApologyMessage,
  sendHomeVisitAppointmentCancelMessage,
} from '@/services/onboarding/sendHilos';
import { getFinancialAssessmentBody, getPreApprovalBody } from '@/controllers/socialscoring';
import { SOCIAL_SCORING_URL } from '@/constants/onboarding';
import { UserMongo } from '../../models/userSchema';
import { AdmissionRequestMongo } from '../../models/admissionRequestSchema';

export async function getNextAgent(): Promise<Types.ObjectId> {
  // Step 1: Fetch all active agents
  // const agents = await UserMongo.find({ role: 'agent', isVerified: true }).sort({
  //   _id: 1,
  // });
  const agentEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ];

  // Fetch agents by email list
  const agents = await UserMongo.find({ email: { $in: agentEmails } });
  if (agents.length === 0) {
    throw new Error('No verified agents available for assignment');
  }

  // Step 2: Find the last assigned Admission Request with an agent
  const lastRequest = await AdmissionRequestMongo.findOne(
    { agentId: { $in: agents.map((a) => a._id) } },
    { agentId: 1 }
  ).sort({ createdAt: -1 });

  // Step 3: Determine next agent index
  let nextIndex = 0;

  if (lastRequest && lastRequest.agentId) {
    const lastIndex = agents.findIndex((agent) => agent._id.equals(lastRequest.agentId));
    if (lastIndex !== -1) {
      nextIndex = (lastIndex + 1) % agents.length;
    }
  }

  return agents[nextIndex]._id;
}

export const createAdmissionRequest = async (
  requestPersonalData: RequestPersonalData,
  userId: string,
  clientIpAddress?: string,
  vehicleType?: VehicleType,
  source?: string
  // eslint-disable-next-line max-params
): Promise<AdmissionRequest> => {
  const admissionRequestId = new Types.ObjectId().toHexString();

  const requestPalenca = new RequestPalenca({
    widgetId: PALENCA_WIDGET_ID,
    externalId: admissionRequestId,
    accounts: [],
  });

  const documents =
    requestPersonalData?.country === Country.us
      ? AdmissionRequestDocumentTypeUS
      : { ...AdmissionRequestDocumentType, ...AdmissionRequestAdditionalDocumentType };

  const pendigDocuments = Object.values(documents).map((documentType) => {
    return new RequestDocument({
      type: documentType,
      status: RequestDocumentStatus.pending,
      mediaId: null,
    });
  });

  const documentsAnalysis = new RequestDocumentsAnalysis({
    status: RequestDocumentsAnalysisStatus.pending,
    documents: pendigDocuments,
  });

  const personalData = new RequestPersonalData({
    ...requestPersonalData,
    status: RequestPersonalDataStepStatus.pending,
    firstName: requestPersonalData.firstName,
    lastName: requestPersonalData.lastName,
    email: requestPersonalData.email,
    phone: requestPersonalData.phone,
  });

  const earningsAnalysis = new EarningsAnalysis({
    status: EarningsAnalysisStatus.pending,
  });

  const riskAnalysis = new RiskAnalysis({
    status: AnalysisStatus.pending,
    scorecardVersion: ScorecardVersion.v1, // Hardcoded for now, sue me :3
  });

  let decodedSource: string | null = null;
  if (source) {
    decodedSource = Object(DriverSourceType)[source];
  }

  const agentId = await getNextAgent();

  const newAdmissionRequest = new AdmissionRequest({
    status: AdmissionRequestStatus.created,
    palenca: requestPalenca,
    documentsAnalysis,
    personalData,
    earningsAnalysis,
    riskAnalysis,
    source: decodedSource,
    clientIpAddress,
    agentId,
  });

  const repoAdmissionRequest = await repoInsertAdmissionRequest(newAdmissionRequest);
  logger.info(
    `[createAdmissionRequest] Admission request inserted ${
      repoAdmissionRequest.id
    } with the following details Palenca: ${JSON.stringify(requestPalenca)}, Documents: ${JSON.stringify(
      documents
    )}, Personal data: ${JSON.stringify(
      personalData
    )}, the source is ${decodedSource}, and Ip address is ${clientIpAddress}`
  );

  const event = new Event({
    userId: userId,
    entityId: repoAdmissionRequest.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['admission_request.created'],
    message: `Solicitud de admisión creada`,
  });

  await repoSaveEvent(event);

  const defaultVehicleType = vehicleType || VehicleType.car;
  await repoSaveOrReplaceRiskAnalysisData(
    repoAdmissionRequest.id!,
    ScorecardVariableName.vehicle_type,
    defaultVehicleType
  );

  logger.info(
    `[createAdmissionRequest] Stored RiskAnalysisData for vehicle_type ${defaultVehicleType} for admission request ${repoAdmissionRequest.id}`
  );
  return repoAdmissionRequest;
};

export const getAdmissionRequestByIdOrThrow = async (id: string): Promise<AdmissionRequest> => {
  const admissionRequest = await repoGetAdmissionRequestById(id);

  if (!admissionRequest) {
    logger.info(`[getAdmissionRequestByIdOrThrow] Admission request not found for id ${id}`);
    throw new AdmissionRequestNotFoundException();
  }
  return admissionRequest;
};

export const addRetrieveEarningsJob = async (job: PalencaJobRetrieveEarnings): Promise<null> => {
  // Mark the accounts as queued to avoid duplicate jobs
  await repoAddRetrieveEarningsJob(job);
  await repoUpdatePalencaAccountEarningsRetrievalStatus(
    job.requestId,
    job.accountId,
    job.platform,
    PalencaRetrievalStatus.queued
  );

  return null;
};

export const addRetrieveMetricsJob = async (job: PalencaJobRetrieveMetrics): Promise<null> => {
  // Mark the accounts as queued to avoid duplicate jobs
  await repoAddRetrieveMetricsJob(job);
  await repoUpdatePalencaAccountMetricsRetrievalStatus(
    job.requestId,
    job.accountId,
    job.platform,
    PalencaRetrievalStatus.queued
  );

  return null;
};

export const evaluatePalencaWebhook = async (palencaWebhook: PalencaWebhook): Promise<null> => {
  const webhookActions = [
    PalencaWebhookAction['earnings.updated'],
    PalencaWebhookAction['profile.updated'],
    PalencaWebhookAction['login.success'],
  ];
  if (!webhookActions.includes(palencaWebhook.webhookAction)) {
    logger.info(`[evaluatePalencaWebhook] Webhook action not supported ${palencaWebhook.webhookAction}`);
    return null;
  }

  const admissionRequest = await repoGetAdmissionRequestById(palencaWebhook.externalId);

  // If the admission request does not exist we do nothing
  if (!admissionRequest) {
    logger.info(`[evaluatePalencaWebhook] Admission request not found for id ${palencaWebhook.externalId}`);
    return null;
  }

  // If the status is not created or earnings_analysis, do nothing
  if (
    admissionRequest.status !== AdmissionRequestStatus.created &&
    admissionRequest.status !== AdmissionRequestStatus.earnings_analysis &&
    admissionRequest.status !== AdmissionRequestStatus.documents_analysis
  ) {
    logger.info(
      `[evaluatePalencaWebhook] Admission request status not supported ${admissionRequest.status} for id ${palencaWebhook.externalId}`
    );
    return null;
  }

  const palenca = admissionRequest.palenca;

  const palencaAccount = palenca.accounts.find(
    (account) =>
      account.accountId === palencaWebhook.accountId && account.platform === palencaWebhook.platform
  );

  // This means the palenca has successfully logged in to their account and the data extraction process has started.
  if (!palencaAccount && palencaWebhook.webhookAction === PalencaWebhookAction['login.success']) {
    // If we have already an account with the same platform and accountId we do nothing

    const existingAccount = palenca.accounts.find(
      (account) =>
        account.accountId === palencaWebhook.accountId && account.platform === palencaWebhook.platform
    );

    if (existingAccount) {
      logger.info(
        `[evaluatePalencaWebhook] Palenca account already exists for account ${palencaWebhook.accountId} and platform ${palencaWebhook.platform}`
      );
      return null;
    }

    const earnings = new PalencaAccountRetrieval({
      status: PalencaRetrievalStatus.pending,
    });

    const metrics = new PalencaAccountRetrieval({
      status: PalencaRetrievalStatus.pending,
    });

    const newAccount = new PalencaAccount({
      accountId: palencaWebhook.accountId,
      platform: palencaWebhook.platform,
      earnings,
      metrics,
      status: PalencaAccountStatus.pending,
      createdAt: new Date(),
    });

    await repoAddPalencaAccountToAdmissionRequest(admissionRequest.id!, newAccount);
    logger.info(`[evaluatePalencaWebhook] Palenca account added to admission request ${admissionRequest.id}`);
  }

  // Earnings information has been created or updated and we can we can retrieve them
  if (admissionRequest && palencaWebhook.webhookAction === PalencaWebhookAction['earnings.updated']) {
    // If the account has already been retrieved we do nothing
    if (palencaAccount && palencaAccount.earnings.status === PalencaRetrievalStatus.success) {
      logger.info(
        `[evaluatePalencaWebhook] Earnings already retrieved for account ${palencaAccount.accountId}`
      );
      return null;
    }

    // If the account has pending status we queue a job to retrieve the earnings
    if (palencaAccount && palencaAccount.earnings.status === PalencaRetrievalStatus.pending) {
      const palencaJobRetrieveEarnings = new PalencaJobRetrieveEarnings({
        accountId: palencaWebhook.accountId,
        platform: palencaWebhook.platform,
        requestId: admissionRequest.id!,
      });

      logger.info(
        `[evaluatePalencaWebhook] Adding job to retrieve earnings for account ${palencaAccount.accountId} and platform ${palencaAccount.platform}`
      );
      await addRetrieveEarningsJob(palencaJobRetrieveEarnings);
    }
  }

  if (palencaWebhook.webhookAction === PalencaWebhookAction['profile.updated']) {
    // If the account has already been retrieved we do nothing
    if (palencaAccount && palencaAccount.metrics.status === PalencaRetrievalStatus.success) {
      logger.info(
        `[evaluatePalencaWebhook] Metrics already retrieved for account ${palencaAccount.accountId}`
      );
      return null;
    }

    // If the account has pending status we queue a job to retrieve the metrics
    if (palencaAccount && palencaAccount.metrics.status === PalencaRetrievalStatus.pending) {
      const palencaJobRetrieveMetrics = new PalencaJobRetrieveMetrics({
        accountId: palencaWebhook.accountId,
        platform: palencaWebhook.platform,
        requestId: admissionRequest.id!,
      });

      logger.info(
        `[evaluatePalencaWebhook] Adding job to retrieve metrics for account ${palencaAccount.accountId} and platform ${palencaAccount.platform}`
      );
      await addRetrieveEarningsJob(palencaJobRetrieveMetrics);
    }
  }

  return null;
};

export const retrieveMostRecent12WeeksEarnings = async (
  requestId: string,
  platform?: GigPlatform
): Promise<WeeklyEarning[]> => {
  const weeklyEarnings = await repoRetrieveWeeklyEarnings(requestId, platform);
  logger.info(
    `[retrieveMostRecent12WeeksEarnings] Fetched ${weeklyEarnings.length} earnings for admission request ${requestId} and platform ${platform}`
  );
  const sortedWeeks = weeklyEarnings.sort((a, b) => {
    // First compare by year
    if (a.year > b.year) return -1;
    if (a.year < b.year) return 1;

    // If the years are equal, compare by week
    return b.week - a.week;
  });

  const mostRecent12Weeks = sortedWeeks.slice(0, 12);
  return mostRecent12Weeks;
};

export const calculateEarningsAnalysis = async (requestId: string): Promise<AdmissionRequest> => {
  //Primero se trae el id de la base de datos
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the earnings analysis is not pending we do nothing
  // if (currentAdmissionRequest.earningsAnalysis?.status !== EarningsAnalysisStatus.pending) {
  //   //valida si esta en estado pendiente
  //   logger.info(
  //     `[calculateEarningsAnalysis] Earnings analysis status not pending for admission request ${requestId}`
  //   );
  //   return currentAdmissionRequest;
  // }

  const platforms = currentAdmissionRequest.palenca.accounts.length;
  //revisa cuantas plataformas tiene el usuario

  // Retrieve earnings of all platforms
  const mostRecent12Weeks = await retrieveMostRecent12WeeksEarnings(requestId);

  const totalEarnings = mostRecent12Weeks.reduce((acc, curr) => acc + curr.totalAmount, 0);

  const preQualification = determineEarningsAnalysisStatus(totalEarnings);
  //Precalificacion por ganancias, 90000 aprobado, 70000 aprobado con condiciones, menor de 70000 rechazado
  logger.info(
    `[calculateEarningsAnalysis] Pre qualification status ${preQualification} for admission request ${requestId}`
  );
  const earningsAnalysis = new EarningsAnalysis({
    totalEarnings,
    status: preQualification,
    earnings: mostRecent12Weeks,
    platforms,
  });

  const updated = await repoSaveEarningsAnalysis(requestId, earningsAnalysis);

  return updated;
};

export const checkAndCalculateEarningsAnalysis = async (requestId: string): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the request is not in earnings analysis status we do nothing
  if (currentAdmissionRequest.status !== AdmissionRequestStatus.earnings_analysis) {
    logger.info(
      `[checkAndCalculateEarningsAnalysis] Admission request status not earnings_analysis for id ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // If earnings analysis is not pending we do nothing
  if (currentAdmissionRequest.earningsAnalysis?.status !== EarningsAnalysisStatus.pending) {
    logger.info(
      `[checkAndCalculateEarningsAnalysis] Earnings analysis status not pending for admission request ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // Make sure we retreived all the earnings for each account before calculating the earnings analysis
  const palencaAccounts = currentAdmissionRequest.palenca.accounts;

  const earningsRetrievalStatus = palencaAccounts.every(
    (account) => account.earnings.status === PalencaRetrievalStatus.success
  );

  if (!earningsRetrievalStatus) {
    logger.info(
      `[checkAndCalculateEarningsAnalysis] Not all earnings have been retrieved for admission request ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // Calcualte and save the earnings analysis
  logger.info(
    `[checkAndCalculateEarningsAnalysis] Calculating earnings analysis for admission request ${requestId}`
  );
  const updated = await calculateEarningsAnalysis(requestId);

  // If the earnings analysis is approved or approved with conditions we update the request status as documents_analysis
  // If the earnings analysis is rejected we update the request status as rejected and set reasons earnings_analysis
  if (updated.earningsAnalysis) {
    if (
      updated.earningsAnalysis.status === EarningsAnalysisStatus.approved ||
      updated.earningsAnalysis.status === EarningsAnalysisStatus.approved_with_conditions
    ) {
      logger.info(
        `[checkAndCalculateEarningsAnalysis] Updating admission request status as documents_analysis for admission request ${requestId}`
      );
      await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.documents_analysis);

      // Store the RiskAnalysisData for earnings_last_12_weeks
      const earningsLast12Weeks = updated.earningsAnalysis.totalEarnings!;
      await repoSaveOrReplaceRiskAnalysisData(
        requestId,
        ScorecardVariableName.earnings_last_12_weeks,
        earningsLast12Weeks
      );
      logger.info(
        `[checkAndCalculateEarningsAnalysis] Stored RiskAnalysisData for earnings_last_12_weeks ${earningsLast12Weeks} for admission request ${requestId}`
      );

      // Store the RiskAnalysisData for gig_platforms
      const numberOfGigPlatforms = updated.earningsAnalysis.platforms!;
      await repoSaveOrReplaceRiskAnalysisData(
        requestId,
        ScorecardVariableName.gig_platforms,
        numberOfGigPlatforms
      );

      logger.info(
        `[checkAndCalculateEarningsAnalysis] Stored RiskAnalysisData for gig_platforms ${numberOfGigPlatforms} for admission request ${requestId}`
      );

      // Store the RiskAnalysisData from vehicle_condition
      const vehicleCondition =
        updated.earningsAnalysis.status === EarningsAnalysisStatus.approved_with_conditions
          ? VehicleCondition.used
          : VehicleCondition.new;

      await repoSaveOrReplaceRiskAnalysisData(
        requestId,
        ScorecardVariableName.vehicle_condition,
        vehicleCondition
      );
    }

    if (updated.earningsAnalysis.status === EarningsAnalysisStatus.rejected) {
      logger.info(
        `[checkAndCalculateEarningsAnalysis] Updating admission request status as rejected for admission request ${requestId}`
      );
      await repoUpdateAdmissionRequestStatus(
        requestId,
        AdmissionRequestStatus.rejected,
        AdmissionRequestRejectionReason.earnings_analysis
      );
    }
  }

  return updated;
};

export const calculateMetricsAnalysis = async (requestId: string): Promise<null> => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);

  // If the request is approved or rejected we do nothing
  if (
    current.status === AdmissionRequestStatus.approved ||
    current.status === AdmissionRequestStatus.rejected
  ) {
    logger.info(
      `[calculateMetricsAnalysis] Admission request status approved or rejected for id ${requestId}`
    );
    return null;
  }

  const palencaAccounts = current.palenca.accounts;

  const metricsRetrievalStatus = palencaAccounts.every(
    (account) => account.metrics.status === PalencaRetrievalStatus.success
  );

  if (!metricsRetrievalStatus) {
    logger.info(
      `[calculateMetricsAnalysis] Not all metrics have been retrieved for admission request ${requestId}`
    );
    return null;
  }

  const metrics = await repoRetriveMetricsForAllPlatforms(requestId);

  // We calculate the total lifetime trips by adding all the lifetime trips
  const lifeTimeCompletedTrips = metrics.reduce((acc, curr) => acc + curr.lifetimeTrips, 0);

  // We calculate the average acceptance rate by adding all the acceptance rates and dividing by the number of platforms
  const averageAcceptanceRate = metrics.reduce((acc, curr) => acc + curr.acceptanceRate, 0) / metrics.length;

  // We calculate the average cancellation rate by adding all the cancellation rates and dividing by the number of platforms
  const averageCancellationRate =
    metrics.reduce((acc, curr) => acc + curr.cancellationRate, 0) / metrics.length;

  // We calculate the average rating by adding all the ratings and dividing by the number of platforms
  const averageRating = metrics.reduce((acc, curr) => acc + curr.rating, 0) / metrics.length;

  // We calculate the time since first trip by getting the greatest time of all the platforms
  const greatestTimeSinceFirstTrip = metrics.reduce((max, currentElem) => {
    return currentElem.timeSinceFirstTrip > max ? currentElem.timeSinceFirstTrip : max;
  }, 0);

  const variables: Map<ScorecardVariableName, number | string> = new Map();
  variables.set(ScorecardVariableName.life_time_completed_trips, lifeTimeCompletedTrips);
  variables.set(ScorecardVariableName.percentage_acceptance_rate, averageAcceptanceRate);
  variables.set(ScorecardVariableName.percentage_cancellation_rate, averageCancellationRate);
  variables.set(ScorecardVariableName.average_rating, averageRating);
  variables.set(ScorecardVariableName.days_since_first_trip, greatestTimeSinceFirstTrip);

  await repoSaveOrReplaceBatchRiskAnalysisData(requestId, variables);

  logger.info(
    `[calculateMetricsAnalysis] Stored RiskAnalysisData Metrics for admission request ${requestId}`
  );

  return null;
};

export const checkAndCalculateMetrics = async (requestId: string): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the request is has approved or rejected status we do nothing
  if (
    currentAdmissionRequest.status === AdmissionRequestStatus.approved ||
    currentAdmissionRequest.status === AdmissionRequestStatus.rejected
  ) {
    logger.info(
      `[checkAndCalculateMetrics] Admission request status approved or rejected for id ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // Make sure we retreived all the metrics for each account before calculating the metrics analysis
  const palencaAccounts = currentAdmissionRequest.palenca.accounts;

  const metricsRetrievalStatus = palencaAccounts.every(
    (account) => account.metrics.status === PalencaRetrievalStatus.success
  );

  if (!metricsRetrievalStatus) {
    logger.info(
      `[checkAndCalculateMetrics] Not all metrics have been retrieved for admission request ${requestId}`
    );
    return currentAdmissionRequest;
  }

  // Calculate and save the metrics as RiskAnalysisData
  logger.info(`[checkAndCalculateMetrics] Calculating metrics analysis for admission request ${requestId}`);
  await calculateMetricsAnalysis(requestId);

  return currentAdmissionRequest;
};

export const startEarningsAnalysis = async (requestId: string): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  logger.info(
    `[startEarningsAnalysis] Updating admission request status as earnings_analysis for admission request ${requestId}`
  );

  // If admission request is not created we do nothing

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.created) {
    logger.info(
      `[startEarningsAnalysis] Admission request status not created for admission request ${requestId}`
    );
    return currentAdmissionRequest;
  }

  const updated = await repoUpdateAdmissionRequestStatus(
    currentAdmissionRequest.id!,
    AdmissionRequestStatus.earnings_analysis
  );

  // Since its an async process we must check if we can calculate the earnings analysis
  await checkAndCalculateEarningsAnalysis(currentAdmissionRequest.id!);

  return updated;
};

export const checkAndMarkPalencaAccountAsSuccess = async (
  requestId: string,
  accountId: string,
  platform: GigPlatform
): Promise<null> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const palencaAccount = currentAdmissionRequest.palenca.accounts.find(
    (account) => account.accountId === accountId && account.platform === platform
  );

  if (!palencaAccount) {
    return null;
  }

  if (palencaAccount.status === PalencaAccountStatus.success) {
    return null;
  }

  // If both earnings and metrics have been retrieved we mark the account as success
  if (
    palencaAccount.earnings.status === PalencaRetrievalStatus.success &&
    palencaAccount.metrics.status === PalencaRetrievalStatus.success
  ) {
    logger.info(
      `[checkAndMarkPalencaAccountAsSuccess] Updating palenca account status as success for account ${accountId}`
    );
    await repoUpdatePalencaAccountStatus(
      requestId,
      accountId,
      palencaAccount.platform,
      PalencaAccountStatus.success
    );
  }

  // If any of the earnings or metrics have error status we mark the account as error
  if (
    palencaAccount.earnings.status === PalencaRetrievalStatus.error ||
    palencaAccount.metrics.status === PalencaRetrievalStatus.error
  ) {
    logger.info(
      `[checkAndMarkPalencaAccountAsSuccess] Updating palenca account status as error for account ${accountId}`
    );
    await repoUpdatePalencaAccountStatus(
      requestId,
      accountId,
      palencaAccount.platform,
      PalencaAccountStatus.error
    );
  }

  return null;
};

export const retrievePalencaEarnings = async ({
  accountId,
  platform,
  requestId,
}: {
  accountId: string;
  platform: GigPlatform;
  requestId: string;
}): Promise<null> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const palencaAccount = currentAdmissionRequest.palenca.accounts.find(
    (account) => account.accountId === accountId && account.platform === platform
  );

  if (!palencaAccount) {
    return null;
  }

  // If the account has already been retrieved we do nothing
  if (palencaAccount.earnings.status === PalencaRetrievalStatus.success) {
    logger.info(`[retrievePalencaEarnings] Earnings already retrieved for account ${accountId}`);
    return null;
  }

  try {
    const earnings = await repoRetrieveEarnings(accountId, platform, requestId);
    await repoSaveEarnings(earnings);
    // Mark the account earnings as retrieved
    await repoUpdatePalencaAccountEarningsRetrievalStatus(
      requestId,
      accountId,
      platform,
      PalencaRetrievalStatus.success
    );
    await checkAndMarkPalencaAccountAsSuccess(requestId, accountId, platform);
    // Check and calculate the earnings analysis
    await checkAndCalculateEarningsAnalysis(requestId);
  } catch (error) {
    logger.error(
      `[retrievePalencaEarnings] Error retrieving earnings for account ${accountId} and platform ${platform}`
    );
    // Mark the account earnings as error
    await repoUpdatePalencaAccountEarningsRetrievalStatus(
      requestId,
      accountId,
      platform,
      PalencaRetrievalStatus.error
    );
  }

  return null;
};

export const retrievePalencaMetrics = async ({
  accountId,
  platform,
  requestId,
}: {
  accountId: string;
  platform: GigPlatform;
  requestId: string;
}): Promise<null> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const palencaAccount = currentAdmissionRequest.palenca.accounts.find(
    (account) => account.accountId === accountId && account.platform === platform
  );

  if (!palencaAccount) {
    return null;
  }

  // If the account has already been retrieved we do nothing
  if (palencaAccount.metrics.status === PalencaRetrievalStatus.success) {
    logger.info(`[retrievePalencaMetrics] Metrics already retrieved for account ${accountId}`);
    return null;
  }

  try {
    const metric = await repoRetrieveMetric(accountId, platform, requestId);
    if (metric) {
      await repoSaveMetric(metric);
      await repoUpdatePalencaAccountMetricsRetrievalStatus(
        requestId,
        accountId,
        platform,
        PalencaRetrievalStatus.success
      );
      await checkAndMarkPalencaAccountAsSuccess(requestId, accountId, platform);
      await checkAndCalculateMetrics(requestId);
    }
  } catch (error) {
    logger.error(
      `[retrievePalencaMetrics] Error retrieving metrics for account ${accountId} and platform ${platform}`
    );
    await repoUpdatePalencaAccountMetricsRetrievalStatus(
      requestId,
      accountId,
      platform,
      PalencaRetrievalStatus.error
    );
  }

  return null;
};

export const updateRequestPersonalData = async (
  requestId: string,
  requestPersonalData: RequestPersonalData
): Promise<AdmissionRequest> => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);

  const saved = await repoUpdateRequestPersonalData(current.id!, requestPersonalData);

  // If the birthdate has been updated we store the RiskAnalysisData as age
  if (requestPersonalData.birthdate) {
    const birthdate = new Date(requestPersonalData.birthdate);
    const age = new Date().getFullYear() - birthdate.getFullYear();
    await repoSaveOrReplaceRiskAnalysisData(current.id!, ScorecardVariableName.age, age);
    logger.info(
      `[updateRequestPersonalData] Stored RiskAnalysisData for age ${age} for admission request ${current.id!}`
    );
  }

  return saved;
};

export const uploadMedia = async ({
  file,
  mediaType,
}: {
  file: Express.Multer.File;
  mediaType: MediaType;
}): Promise<Document> => {
  const [path, mimeType] = await repoUploadMedia({ file, mediaType });
  const document = new Document({
    fileName: file.originalname,
    path,
    mimeType,
    type: mediaType,
    status: MediaStatus.pending,
  });

  logger.info(`[uploadMedia] media document created ${JSON.stringify(document)}`);

  const saved = await repoInsertDocument(document);
  await removeFile(file.path);

  return saved;
};

export const updateRequestDocuments = async (
  requestId: string,
  updateDocuments: RequestDocument[]
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const currentRequestDocuments = currentAdmissionRequest.documentsAnalysis.documents;

  const updates: RequestDocument[] = [];
  const deletions: string[] = [];

  // Check if there is already a mediaId in each document field of the currentRequestDocuments
  // If there is set it the new save to update array and the delete media id in deletions

  updateDocuments.forEach((updateDocument) => {
    const currentDocument = currentRequestDocuments.find((document) => document.type === updateDocument.type);
    logger.info(
      `[updateRequestDocuments] document ${updateDocument.type} with status ${updateDocument.status}`
    );
    if (currentDocument) {
      if (currentDocument.mediaId) {
        deletions.push(currentDocument.mediaId);
      }
    }
    updates.push(updateDocument);
  });

  if (updates.length > 0) {
    const updateMediaIds = updates.map((update) => update.mediaId!);
    await repoUpdateBatchDocumentStatus(updateMediaIds, MediaStatus.active);
  }

  if (deletions.length > 0) {
    await repoUpdateBatchDocumentStatus(deletions, MediaStatus.deleted);
  }

  if (updates.length > 0 || deletions.length > 0) {
    const updated = await repoUpdateRequestDocuments(requestId, updates);
    return updated;
  }

  return currentAdmissionRequest;
};

export const createHomeVisit = async (
  requestId: string,
  homeVisit: HomeVisit,
  userId: string
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  await repoUpdateBatchDocumentStatus(homeVisit.images as Array<string>, MediaStatus.active);

  const newHomeVisit = new HomeVisit({
    status: homeVisit.status,
    isAddressProvidedByApplicant: homeVisit.isAddressProvidedByApplicant,
    residentOwnershipStatus: homeVisit.residentOwnershipStatus,
    hasGarage: homeVisit.hasGarage,
    comments: homeVisit.comments,
    images: homeVisit.images,
    responsible: homeVisit.responsible,
    visitDate: homeVisit.visitDate,
  });
  logger.info(`[createHomeVisit] Creating home visit for admission request ${requestId}`);

  const saved = await repoCreateHomeVisit(requestId, newHomeVisit);

  const eventHomeVisit = new Event({
    userId: userId,
    entityId: currentAdmissionRequest.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['home_visit.created'],
    message: `Visita domiciliaria creada`,
  });

  if (homeVisit.status === HomeVisitStatus.approved) {
    logger.info(
      `[createHomeVisit] Updating admission request status as final_evaluation for admission request ${requestId}`
    );
    // The whole admission request is approved
    await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.approved);

    const eventHomeVisitResult = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['home_visit.approved'],
      message: `Visita domiciliaria aprobada`,
    });

    const eventAdmissionRequestApproved = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['admission_request.approved'],
      message: `Solicitud de admisión aprobada`,
    });

    const hubspotBtach = await HubspotBatch.findOne({ requestId });
    if (hubspotBtach && hubspotBtach.dealId) {
      await dealUpdate({
        dealId: hubspotBtach.dealId,
        properties: {
          dealstage: 'closedwon',
        },
      });
    }

    await repoSaveEvents([eventHomeVisit, eventHomeVisitResult, eventAdmissionRequestApproved]);
  }

  if (homeVisit.status === HomeVisitStatus.rejected) {
    logger.info(
      `[createHomeVisit] Updating admission request status as rejected for admission request ${requestId}`
    );
    await repoUpdateAdmissionRequestStatus(
      requestId,
      AdmissionRequestStatus.rejected,
      AdmissionRequestRejectionReason.home_visit
    );

    const hubspotBatch = await HubspotBatch.findOne({
      requestId,
    });

    if (hubspotBatch && hubspotBatch.dealId) {
      await dealUpdate({
        dealId: hubspotBatch.dealId,
        properties: {
          dealstage: 'closedlost',
        },
      });
    }

    const eventHomeVisitResult = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['home_visit.rejected'],
      message: `Visita domiciliaria rechazada`,
    });

    const eventAdmissionRequestRejected = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['admission_request.rejected'],
      message: `Solicitud de admisión rechazada`,
    });

    await repoSaveEvents([eventHomeVisit, eventHomeVisitResult, eventAdmissionRequestRejected]);
  }

  return saved;
};

export const notifyCustomerAboutHomeVisitFormApproval = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { phone, email },
    } = currentAdmissionRequest;

    await sendHomeVisitApprovalOrRejectionMessage({
      requestId: requestId,
      phone: phone!,
      type: 'homeVisitApproval',
    });

    await sendHomeVisitFormApprovalEmail({
      email: email!,
      requestId: requestId,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${requestId}`,
    });
  } catch (error: any) {
    logger.error(
      `[notifyCustomerAboutHomeVisitFormApproval] - Home visit form approval message failed to send to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const updateHomeVisit = async (
  requestId: string,
  homeVisit: HomeVisit,
  userId: string
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  try {
    await repoUpdateBatchDocumentStatus(homeVisit.images as Array<string>, MediaStatus.active);
    await repoUpdateBatchDocumentStatus(
      homeVisit.proofOfPropertyOwnership as Array<string>,
      MediaStatus.active
    );
  } catch (error) {
    logger.error(`[updateHomeVisit] Error updating home visit for admission request ${requestId}`);
  }

  logger.info(`[updateHomeVisit] Updating home visit for admission request ${requestId}`);

  const saved = await repoUpdateHomeVisit(requestId, homeVisit);

  if (homeVisit.status === HomeVisitStatus.pending) {
    logger.info(
      `[createHomeVisit] Updating admission request status as pending for admission request ${requestId}`
    );
    await repoUpdateAdmissionRequestStatus(
      requestId,
      AdmissionRequestStatus.home_visit,
      AdmissionRequestRejectionReason.home_visit
    );

    const eventHomeVisitResult = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['home_visit.pending'],
      message: `Visita domiciliaria pendiente`,
    });

    const eventAdmissionRequestPending = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['home_visit.pending'],
      message: `Solicitud de admisión rechazada`,
    });

    await repoSaveEvents([eventHomeVisitResult, eventAdmissionRequestPending]);
  }

  if (homeVisit.status === HomeVisitStatus.approved) {
    logger.info(
      `[createHomeVisit] Updating admission request status as final_evaluation for admission request ${requestId}`
    );

    await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.social_analysis);

    await notifyCustomerAboutHomeVisitFormApproval(requestId);

    const eventHomeVisitResult = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['home_visit.approved'],
      message: `Visita domiciliaria aprobada`,
    });

    await repoSaveEvent(eventHomeVisitResult);
  }

  if (homeVisit.status === HomeVisitStatus.rejected) {
    logger.info(
      `[createHomeVisit] Updating admission request status as rejected for admission request ${requestId}`
    );
    await repoUpdateAdmissionRequestStatus(
      requestId,
      AdmissionRequestStatus.rejected,
      AdmissionRequestRejectionReason.home_visit
    );

    const hubspotBatch = await HubspotBatch.findOne({
      requestId,
    });

    if (hubspotBatch && hubspotBatch.dealId) {
      await dealUpdate({
        dealId: hubspotBatch.dealId,
        properties: {
          dealstage: 'closedlost',
        },
      });
    }

    await sendHomeVisitApprovalOrRejectionMessage({
      requestId: requestId,
      phone: currentAdmissionRequest.personalData.phone!,
      type: 'homeVisitRejection',
    });

    const eventHomeVisitResult = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['home_visit.rejected'],
      message: `Visita domiciliaria rechazada`,
    });

    const eventAdmissionRequestRejected = new Event({
      userId: userId,
      entityId: currentAdmissionRequest.id!,
      entityType: EntityType.admission_request,
      actionType: ActionType['admission_request.rejected'],
      message: `Solicitud de admisión rechazada`,
    });

    await repoSaveEvents([eventHomeVisitResult, eventAdmissionRequestRejected]);
  }

  return saved;
};

export const addPalencaAccountToAdmissionRequest = async (
  requestId: string,
  platform: GigPlatform,
  accountId: string
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the account already exists we do nothing
  const accountExists = currentAdmissionRequest.palenca.accounts.find(
    (account) => account.accountId === accountId && account.platform === platform
  );

  if (accountExists) {
    return currentAdmissionRequest;
  }

  const earnings = new PalencaAccountRetrieval({
    status: PalencaRetrievalStatus.pending,
  });

  const metrics = new PalencaAccountRetrieval({
    status: PalencaRetrievalStatus.pending,
  });

  const palencaAccount = new PalencaAccount({
    accountId,
    platform,
    earnings,
    metrics,
    status: PalencaAccountStatus.pending,
    createdAt: new Date(),
  });

  logger.info(
    `[addPalencaAccountToAdmissionRequest] Adding palenca account to admission request ${requestId} for account ${accountId} and platform ${platform}`
  );
  const updated = await repoAddPalencaAccountToAdmissionRequest(requestId, palencaAccount);

  if (!updated) {
    throw new AdmissionRequestNotFoundException();
  }

  return updated;
};

export const searchPaginatedAdmissionRequests = async (
  country: string,
  q: string,
  options: PaginationSearchOptions,
  status?: string
  // eslint-disable-next-line max-params
): Promise<[Pagination, AdmissionRequest[]]> => {
  const [totalItems, entities] = await repoGetPaginatedAdmissionRequests(country, q, options, status);

  const pagination = generatePagination(options.page!, totalItems, options.itemsPerPage!);

  return [pagination, entities];
};

export const getRequestDocumentsAnalysis = async (
  requestId: string,
  documentClassification: DocumentClassification
): Promise<RequestDocumentsAnalysis> => {
  return repoGetRequestDocumentAnalysisWithMedia(requestId, documentClassification);
};

export const sendHomeVisitAppointmentSendedulingLink = async ({
  requestId,
  phone,
  name,
}: {
  requestId: string;
  phone: string;
  name: string;
}) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);
    const {
      personalData: { email },
    } = currentAdmissionRequest;
    const response = await sendHomeVisitAppointmentScheduleLink({
      phone,
      name,
      type: 'appointmentScheduler',
      requestId: requestId,
    });
    logger.info(
      `[sendHomeVisitAppointmentSendedulingLink] - Appointment scheduler link sent with id ${response.id} to clientId ${requestId}`
    );

    await sendHomeVisitAppointmentScheduleLinkEmail({
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit`,
      requestId: requestId,
    });

    await repoUpdateHomeVisitAppointmentSchedulingLinkSendDate(requestId, new Date());
  } catch (error) {
    logger.info(
      `[sendHomeVisitAppointmentSendedulingLink] - Failed to send appointment scheduler page link to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const approveRequestDocument = async (
  requestId: string,
  documentType: AdmissionRequestDocumentType
): Promise<null> => {
  await repoUpdateRequestDocumentStatus(requestId, documentType, RequestDocumentStatus.approved);
  return null;
};

export const rejectRequestDocument = async (
  requestId: string,
  documentType: AdmissionRequestDocumentType
): Promise<null> => {
  await repoRejectRequestDocument(requestId, documentType);
  return null;
};

// Calculate Rideshare Performance score only (no DB save)
const calculateRideshareScoreOnly = async (admissionRequest: AdmissionRequest): Promise<ModelResult> => {
  try {
    const body = await getPreApprovalBody(admissionRequest.id!);
    const response = await fetch(`${SOCIAL_SCORING_URL}/risk-scoring`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(body),
    });
    const data = await response.json();
    if (response.status !== 200) {
      throw new Error(data.message);
    }
    const { score, feature_scores: featureScores, model_result: modelResult, weights } = data;
    return {
      modelName: MLModels.RIDESHARE_PERFORMANCE,
      status: AnalysisStatus.completed,
      modelScore: score * 100,
      modelFeatureScores: featureScores,
      modelResult: modelResult,
      modelWeights: weights,
    };
  } catch (error: any) {
    logger.error(
      `[calculateRideshareScoreOnly] Error calculating rideshare performance for admission request ${admissionRequest.id}`,
      error
    );
    throw new Error(error.message);
  }
};

// Calculate Financial Assessment score only (no DB save)
const calculateFinancialScoreOnly = async (admissionRequest: AdmissionRequest): Promise<ModelResult> => {
  try {
    const body = await getFinancialAssessmentBody(admissionRequest);
    const response = await fetch(`${SOCIAL_SCORING_URL}/v3/financial-assessment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(body),
    });
    const data = await response.json();
    if (response.status !== 200) {
      throw new Error(data.message);
    }
    const { score, feature_scores: featureScores, model_result: modelResult, weights } = data;
    return {
      modelName: MLModels.FINANCIAL_ASSESSMENT,
      status: AnalysisStatus.completed,
      modelScore: score,
      modelFeatureScores: featureScores,
      modelResult: modelResult,
      modelWeights: weights,
    };
  } catch (error: any) {
    logger.error(
      `[calculateFinancialScoreOnly] Error performing financial assessment for admission request ${admissionRequest.id}`,
      error
    );
    throw new Error(error.message);
  }
};

// Calculate Personal Information Analysis score only (no DB save)
const calculatePersonalInformationScoreOnly = async (
  admissionRequest: AdmissionRequest
): Promise<ModelResult> => {
  try {
    const response = await fetch(`${SOCIAL_SCORING_URL}/v3/personal-info`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(admissionRequest),
    });
    const data = await response.json();
    if (response.status !== 200) {
      throw new Error(data.message);
    }
    const { score, feature_scores: featureScores, model_result: modelResult, weights } = data;
    return {
      modelName: MLModels.PERSONAL_INFORMATION,
      status: AnalysisStatus.completed,
      modelScore: score,
      modelFeatureScores: featureScores,
      modelResult: modelResult,
      modelWeights: weights,
    };
  } catch (error: any) {
    logger.error(
      `[calculatePersonalInformationScoreOnly] Error performing personal info analysis for admission request ${admissionRequest.id}`,
      error
    );
    throw new Error(error.message);
  }
};

// Calculate Home Information Analysis score only (no DB save)
const calculateHomeInformationScoreOnly = async (
  admissionRequest: AdmissionRequest
): Promise<ModelResult> => {
  try {
    const response = await fetch(`${SOCIAL_SCORING_URL}/v3/home-visit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(admissionRequest),
    });

    const data = await response.json();

    if (!response.ok || !data?.score || !data?.feature_scores || !data?.model_result || !data?.weights) {
      throw new Error(data.message || 'Invalid response data structure');
    }

    const { score, feature_scores: featureScores, model_result: modelResult, weights } = data;

    return {
      modelName: MLModels.HOMEVISIT_INFORMATION,
      status: AnalysisStatus.completed,
      modelScore: score,
      modelFeatureScores: featureScores,
      modelResult: modelResult,
      modelWeights: weights,
    };
  } catch (error: any) {
    logger.error(
      `[calculateHomeInformationScoreOnly] Error performing home info analysis for admission request ${admissionRequest.id}`,
      error
    );

    throw new Error(error.message);
  }
};

// Calculate model score without saving to database
const calculateModelScoreWithoutSaving = async (
  modelName: MLModels,
  admissionRequest: AdmissionRequest
): Promise<{ modelName: MLModels; result: ModelResult }> => {
  try {
    // Extract just the API call and result processing from the calculation functions
    let result: ModelResult;

    switch (modelName) {
      case MLModels.RIDESHARE_PERFORMANCE:
        result = await calculateRideshareScoreOnly(admissionRequest);
        break;
      case MLModels.FINANCIAL_ASSESSMENT:
        result = await calculateFinancialScoreOnly(admissionRequest);
        break;
      case MLModels.HOMEVISIT_INFORMATION:
        result = await calculateHomeInformationScoreOnly(admissionRequest);
        break;
      case MLModels.PERSONAL_INFORMATION:
        result = await calculatePersonalInformationScoreOnly(admissionRequest);
        break;
      default:
        throw new Error(`Unsupported model: ${modelName}`);
    }

    return { modelName, result };
  } catch (error: any) {
    logger.error(
      `[calculateModelScoreWithoutSaving] Error calculating ${modelName} for request ${admissionRequest.id}`,
      error
    );

    // Return an error result
    return {
      modelName,
      result: {
        modelName,
        status: AnalysisStatus.error,
        modelScore: 0,
        modelFeatureScores: {},
        modelResult: { message: error.message },
        modelWeights: {},
      },
    };
  }
};

// THIS ONE FOR FINANCIAL ASSESSMENT AND RISK ANALYSIS TRIGGERING AND SAVE IN DB
export const calculateRiskAnalysis = async (requestId: string): Promise<AdmissionRequest> => {
  let current = await getAdmissionRequestByIdOrThrow(requestId);

  // If admission request is not on risk analysis status we do nothing
  if (current.status !== AdmissionRequestStatus.risk_analysis) {
    logger.info(
      `[calculateRiskAnalysis] Admission request status not risk_analysis for admission request ${requestId}`
    );
    throw new RequestNotInRiskAnalysisStatusException();
  }

  // Determine which models need to be calculated
  const modelsToProcess: MLModels[] = [];

  // Check rideshare score
  const hasCompletedRideshareScore =
    current.modelScores?.[MLModels.RIDESHARE_PERFORMANCE]?.status &&
    current.modelScores[MLModels.RIDESHARE_PERFORMANCE].status === AnalysisStatus.completed;

  if (!hasCompletedRideshareScore) {
    modelsToProcess.push(MLModels.RIDESHARE_PERFORMANCE);
  } else {
    logger.info(
      `[calculateRiskAnalysis] Rideshare performance already calculated for admission request ${requestId}`
    );
  }

  // Check financial score
  const hasCompletedFinancialScore =
    current.modelScores?.[MLModels.FINANCIAL_ASSESSMENT]?.status &&
    current.modelScores[MLModels.FINANCIAL_ASSESSMENT].status === AnalysisStatus.completed;

  if (!hasCompletedFinancialScore) {
    modelsToProcess.push(MLModels.FINANCIAL_ASSESSMENT);
  } else {
    logger.info(
      `[calculateRiskAnalysis] Financial assessment already calculated for admission request ${requestId}`
    );
  }

  if (modelsToProcess.length > 0) {
    logger.info(
      `[calculateRiskAnalysis] Processing ${modelsToProcess.length} models in parallel for request ${requestId}`
    );

    // Execute all required model calculations in parallel
    const modelPromises = modelsToProcess.map((modelName) =>
      // We execute the fetch and processing but NOT the database save
      calculateModelScoreWithoutSaving(modelName, current)
    );

    // Wait for all model calculations to complete
    const modelResults = await Promise.all(modelPromises);

    // Save all model results in a single database update
    current = await repoSaveAllModelResults(requestId, modelResults);
  }

  logger.info(`[calculateRiskAnalysis] Risk analysis completed for admission request ${requestId}`);

  return current;
};

export const approveRequestDocumentAnalysis = async (
  requestId: string,
  userId: string
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.documents_analysis) {
    logger.info(
      `[approveRequestDocumentAnalysis] Admission request status not documents_analysis for id ${requestId}`
    );
    throw new RequestNotInDocumentsAnalysisStatusException();
  }

  const documentsAnalysis = currentAdmissionRequest.documentsAnalysis;
  let isRequiredDocumentsApproved: boolean;

  if (currentAdmissionRequest.personalData.country === Country.us) {
    isRequiredDocumentsApproved = documentsAnalysis.documents.every((document: RequestDocument) => {
      if (document.status === RequestDocumentStatus.approved) {
        return true;
      }
      logger.info(
        `[approveRequestDocumentAnalysis] ${document.type} is not approved for admission request ${requestId}`
      );
      return false;
    });
  } else {
    const requiredDocuments: string[] = Object.values(AdmissionRequestDocumentType);
    isRequiredDocumentsApproved = requiredDocuments.every((documentType: string) => {
      const requiredDocument = documentsAnalysis.documents.find(
        (document: RequestDocument) => document.type === documentType
      );
      if (requiredDocument && requiredDocument.status === RequestDocumentStatus.approved) {
        return true;
      }
      logger.info(
        `[approveRequestDocumentAnalysis] ${requiredDocument?.type} is not approved for admission request ${requestId}`
      );
      return false;
    });
  }

  if (!isRequiredDocumentsApproved) {
    throw new NotAllDocumentsApprovedException();
  }

  await repoRequestUpdateRequestDocumentsAnalysisStatus(requestId, RequestDocumentsAnalysisStatus.approved);

  const updated = await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.risk_analysis);

  const event = new Event({
    userId: userId,
    entityId: updated.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['documents_analysis.approved'],
    message: `Análisis de documentos aprobado`,
  });
  const isDeal = await HubspotBatch.findOne({ requestId }).lean();
  if (isDeal?.dealId) {
    console.log('entro');
    await dealUpdate({
      dealId: isDeal?.dealId,
      properties: { dealstage: 'qualifiedtobuy' },
    });
  }

  await repoSaveEvent(event);

  // We start the risk analysis process
  const updatedAdmissionRequest = await calculateRiskAnalysis(requestId);

  // Extract error messages from model scores
  const rideshareModel = updatedAdmissionRequest.modelScores?.[MLModels.RIDESHARE_PERFORMANCE];
  const financialModel = updatedAdmissionRequest.modelScores?.[MLModels.FINANCIAL_ASSESSMENT];

  let errorMessages: string[] = [];

  if (
    rideshareModel?.status === AnalysisStatus.error &&
    (rideshareModel.modelResult as { message: string })?.message
  ) {
    const message = (rideshareModel.modelResult as { message: string }).message;
    errorMessages.push(`Rideshare analysis error: ${message}`);
  }

  if (
    financialModel?.status === AnalysisStatus.error &&
    (financialModel.modelResult as { message: string })?.message
  ) {
    errorMessages.push(
      `Financial analysis error: ${(financialModel.modelResult as { message: string })?.message}`
    );
  }

  if (currentAdmissionRequest.personalData.country === Country.mx) {
    await sendHomeVisitAppointmentSendedulingLink({
      requestId: currentAdmissionRequest.id!,
      phone: currentAdmissionRequest.personalData.phone!,
      name: currentAdmissionRequest.personalData.firstName!,
    });
  }

  if (errorMessages.length > 0) {
    throw new ModelExecutionFailedException(errorMessages.join(', '));
  }

  return updated;
};

export const executeSocialAnalysis = async (requestId: string, userId: string): Promise<AdmissionRequest> => {
  let currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.social_analysis) {
    logger.info(`[executeSocialAnalysis] Admission request status not social_analysis for id ${requestId}`);
    throw new RequestNotInSocialAnalysisStatusException();
  }

  // Determine which models need processing
  const modelsToProcess = Object.values(MLModels).filter((modelName) => {
    const currentScore = currentAdmissionRequest.modelScores?.[modelName];
    return !currentScore || currentScore.status !== AnalysisStatus.completed;
  });

  if (modelsToProcess.length > 0) {
    logger.info(
      `[executeSocialAnalysis] Processing ${modelsToProcess.length} models for request ${requestId}`
    );

    // Execute all required model calculations in parallel
    const modelPromises = modelsToProcess.map((modelName) =>
      // We execute the fetch and processing but NOT the database save
      calculateModelScoreWithoutSaving(modelName, currentAdmissionRequest)
    );

    // Wait for all model calculations to complete
    const modelResults = await Promise.all(modelPromises);

    // Save all model results in a single database update
    currentAdmissionRequest = await repoSaveAllModelResults(requestId, modelResults);
  }

  // Check all model scores for any errors
  let errorMessages: string[] = [];

  for (const modelName of Object.values(MLModels)) {
    const modelScore = currentAdmissionRequest.modelScores?.[modelName];
    if (
      modelScore?.status === AnalysisStatus.error &&
      (modelScore.modelResult as { message: string })?.message
    ) {
      const message = (modelScore.modelResult as { message: string }).message;
      errorMessages.push(`${modelName} analysis error: ${message}`);
    }
  }

  if (errorMessages.length > 0) {
    throw new ModelExecutionFailedException(errorMessages.join(', '));
  }

  const event = new Event({
    userId: userId,
    entityId: currentAdmissionRequest.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['social_analysis.executed'],
    message: `Análisis social ejecutado`,
  });

  await repoSaveEvent(event);

  return currentAdmissionRequest;
};

export const approveRequestSocialAnalysis = async (
  requestId: string,
  userId: string
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.social_analysis) {
    logger.info(
      `[approveRequestSocialAnalysis] Admission request status not social_analysis for id ${requestId}`
    );
    throw new RequestNotInSocialAnalysisStatusException();
  }

  currentAdmissionRequest.documentsAnalysis.documents.forEach((document) => {
    if (document.status !== RequestDocumentStatus.approved) {
      logger.info(
        `[approveRequestSocialAnalysis] All the required documents are not approved yet for id ${requestId}`
      );
      throw new AllRequiredDocumentsAreNotApproved();
    }
  });

  await repoUpdateAdmissionRequestStatus(requestId, AdmissionRequestStatus.approved);

  const event = new Event({
    userId: userId,
    entityId: currentAdmissionRequest.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['social_analysis.approved'],
    message: `Análisis social aprobado`,
  });

  const eventAdmissionRequestApproved = new Event({
    userId: userId,
    entityId: currentAdmissionRequest.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['admission_request.approved'],
    message: `Solicitud de admisión aprobada`,
  });

  await repoSaveEvents([event, eventAdmissionRequestApproved]);

  return currentAdmissionRequest;
};

export const rejectRequestSocialAnalysis = async (
  requestId: string,
  userId: string
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.social_analysis) {
    logger.info(
      `[rejectRequestSocialAnalysis] Admission request status not social_analysis for id ${requestId}`
    );
    throw new RequestNotInSocialAnalysisStatusException();
  }

  await repoUpdateAdmissionRequestStatus(
    requestId,
    AdmissionRequestStatus.rejected,
    AdmissionRequestRejectionReason.social_analysis
  );

  const event = new Event({
    userId: userId,
    entityId: currentAdmissionRequest.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['social_analysis.rejected'],
    message: `Análisis social rechazado`,
  });

  const eventAdmissionRequestRejected = new Event({
    userId: userId,
    entityId: currentAdmissionRequest.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['admission_request.rejected'],
    message: `Solicitud de admisión rechazada`,
  });

  await repoSaveEvents([event, eventAdmissionRequestRejected]);

  return currentAdmissionRequest;
};

export const getHomeVisit = async (requestId: string): Promise<HomeVisit> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  return repoGetHomeVisitWithMedia(currentAdmissionRequest.id!);
};

export const retrievePalencaPlatformMetric = async ({
  requestId,
  platform,
}: {
  requestId: string;
  platform: GigPlatform;
}): Promise<Metric | null> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const palencaAccount = currentAdmissionRequest.palenca.accounts.find(
    (account) => account.platform === platform
  );

  if (!palencaAccount) {
    return null;
  }

  const metric = await repoRetrievePalencaPlatformMetric(requestId, platform);

  return metric;
};

export const retrieveEvents = async (entityId: string, entityType: EntityType): Promise<Event[]> => {
  const events = await repoRetrieveEvents(entityId, entityType);

  return events;
};

export const saveEvent = async (event: Event): Promise<Event> => {
  const saved = await repoSaveEvent(event);

  return saved;
};

export const saveOrReplaceRiskAnalysisData = async (
  requestId: string,
  variable: ScorecardVariableName,
  value: number | string
): Promise<RiskAnalysisData | null> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the admission request is approved or rejected we do nothing
  if (
    currentAdmissionRequest.status === AdmissionRequestStatus.approved ||
    currentAdmissionRequest.status === AdmissionRequestStatus.rejected
  ) {
    logger.info(
      `[saveOrReplaceRiskAnalysisData] Admission request status approved or rejected for admission request ${requestId}`
    );
    return null;
  }

  const updated = await repoSaveOrReplaceRiskAnalysisData(requestId, variable, value);

  return updated;
};

export const approveRiskAnalysis = async (requestId: string, userId: string): Promise<AdmissionRequest> => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);

  // If admission request is not on risk analysis status we do nothing
  if (current.status !== AdmissionRequestStatus.risk_analysis) {
    logger.info(
      `[approveRiskAnalysis] Admission request status not risk_analysis for admission request ${requestId}`
    );
    throw new RequestNotInRiskAnalysisStatusException();
  }

  // If the risk analysis is not completed we do nothing
  // if (current.riskAnalysis?.status !== RiskAnalysisStatus.completed) {
  //   logger.info(
  //     `[approveRiskAnalysis] Risk analysis status not completed for admission request ${requestId}`
  //   );
  //   throw new RiskAnalysisNotCompletedException();
  // }

  // We can start the home visit process
  // Check if home visit is already approved, move directly to social analysis
  const homeVisitApproved = current.homeVisit?.status === HomeVisitStatus.approved;
  const isCountryUS = current.personalData.country === Country.us;

  const updated = await repoUpdateAdmissionRequestStatus(
    requestId,
    isCountryUS
      ? AdmissionRequestStatus.social_analysis
      : homeVisitApproved
        ? AdmissionRequestStatus.social_analysis
        : AdmissionRequestStatus.home_visit
  );

  const event = new Event({
    userId: userId,
    entityId: updated.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['risk_analysis.approved'],
    message: `Análisis de riesgo aprobado`,
  });

  await repoSaveEvent(event);

  return updated;
};

export const rejectRiskAnalysis = async (requestId: string, userId: string): Promise<AdmissionRequest> => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);

  // If admission request is not on risk analysis status we do nothing
  if (current.status !== AdmissionRequestStatus.risk_analysis) {
    logger.info(
      `[rejectRiskAnalysis] Admission request status not risk_analysis for admission request ${requestId}`
    );
    throw new RequestNotInRiskAnalysisStatusException();
  }

  const updated = await repoUpdateAdmissionRequestStatus(
    requestId,
    AdmissionRequestStatus.rejected,
    AdmissionRequestRejectionReason.risk_analysis
  );

  const event = new Event({
    userId: userId,
    entityId: updated.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['risk_analysis.rejected'],
    message: `Análisis de riesgo rechazado`,
  });

  await repoSaveEvent(event);

  return updated;
};

export const rejectRequestDocumentAnalysis = async (
  requestId: string,
  userId: string
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  if (currentAdmissionRequest.status !== AdmissionRequestStatus.documents_analysis) {
    logger.info(
      `[rejectRequestDocumentAnalysis] Admission request status not documents_analysis for id ${requestId}`
    );
    throw new RequestNotInDocumentsAnalysisStatusException();
  }

  await repoRequestUpdateRequestDocumentsAnalysisStatus(requestId, RequestDocumentsAnalysisStatus.rejected);

  const updated = await repoUpdateAdmissionRequestStatus(
    requestId,
    AdmissionRequestStatus.rejected,
    AdmissionRequestRejectionReason.documents_analysis
  );

  const event = new Event({
    userId: userId,
    entityId: updated.id!,
    entityType: EntityType.admission_request,
    actionType: ActionType['documents_analysis.rejected'],
    message: `Análisis de documentos rechazado`,
  });

  await repoSaveEvent(event);

  return updated;
};
export const retryAnalysis = async (requestId: string, modelName: MLModels): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // Check if model exists in admission request and get its status
  const modelStatus = currentAdmissionRequest.modelScores?.[modelName]?.status;

  if (modelStatus === AnalysisStatus.pending) {
    logger.info(`[retryAnalysis] Model ${modelName} is still pending for admission request ${requestId}`);
    return currentAdmissionRequest;
  }

  if (modelStatus === AnalysisStatus.completed) {
    logger.info(`[retryAnalysis] Model ${modelName} is already completed for admission request ${requestId}`);
    return currentAdmissionRequest;
  }

  // Only retry if status is error or null(meaning the model not created, indicating an older record)
  if (modelStatus === AnalysisStatus.error || !modelStatus) {
    logger.info(`[retryAnalysis] Retrying model ${modelName} calculation for admission request ${requestId}`);

    // Use the same parallelized approach as other functions
    try {
      // Execute the model calculation without saving to DB
      const modelResult = await calculateModelScoreWithoutSaving(modelName, currentAdmissionRequest);

      // Save the result in a single database operation
      const updated = await repoSaveAllModelResults(requestId, [modelResult]);

      return updated;
    } catch (error: any) {
      logger.error(
        `[retryAnalysis] Error retrying model ${modelName} for request ${requestId}: ${error.message}`,
        error
      );
      throw error;
    }
  }

  logger.info(`[retryAnalysis] Invalid model status ${modelStatus} for admission request ${requestId}`);
  return currentAdmissionRequest;
};

export const checkEarningsAnalysisOrQueue = async (requestId: string): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  // If the request is not in earnigs analysis status we do nothing
  if (currentAdmissionRequest.status !== AdmissionRequestStatus.earnings_analysis) {
    return currentAdmissionRequest;
  }

  // If the earnings analysis is not pending we do nothing
  if (currentAdmissionRequest.earningsAnalysis?.status !== EarningsAnalysisStatus.pending) {
    logger.info(
      `[checkEarningsAnalysis] Earnings analysis status not pending for admission request ${requestId}`
    );
    return currentAdmissionRequest;
  }

  const palencaAccounts = currentAdmissionRequest.palenca.accounts;

  // Check how many accounts are still in PalencaRetrievalStatus.pending
  const pendingEarningsAccounts = palencaAccounts.filter(
    (account) => account.earnings.status === PalencaRetrievalStatus.pending
  );

  // Check how many accounts are still in PalencaRetrievalStatus.pending
  const pendingMetricsAccounts = palencaAccounts.filter(
    (account) => account.metrics.status === PalencaRetrievalStatus.pending
  );

  // If there are pending accounts, we queue the earnings retrieval for each one
  if (pendingEarningsAccounts.length > 0) {
    logger.info(
      `[checkEarningsAnalysis] There are ${pendingEarningsAccounts.length} accounts pending for admission request ${requestId}`
    );

    pendingEarningsAccounts.forEach((account) => {
      // If the account.createdAt is less than MAX_PALENCA_WAITING_TIME_MS we do nothing
      // We can still wait for the webhook to be called
      const now = new Date().getTime();
      const createdAt = account.createdAt.getTime();

      if (now - createdAt < MAX_PALENCA_WAITING_TIME_MS) {
        return;
      }

      // If the account.status is not pending we do nothing to avoid duplicate jobs
      if (account.status !== PalencaAccountStatus.pending) {
        return;
      }

      const palencaJobRetrieveEarnings: PalencaJobRetrieveEarnings = {
        accountId: account.accountId,
        platform: account.platform,
        requestId: requestId,
      };

      logger.info(
        `[checkEarningsAnalysis] Adding job to retrieve earnings for account ${account.accountId} and platform ${account.platform}`
      );
      addRetrieveEarningsJob(palencaJobRetrieveEarnings);
    });
  }

  // If there are pending accounts, we queue the metrics retrieval for each one
  if (pendingMetricsAccounts.length > 0) {
    logger.info(
      `[checkEarningsAnalysis] There are ${pendingMetricsAccounts.length} accounts pending for admission request ${requestId}`
    );

    pendingMetricsAccounts.forEach((account) => {
      // If the account.createdAt is less than MAX_PALENCA_WAITING_TIME_MS we do nothing

      const now = new Date().getTime();
      const createdAt = account.createdAt.getTime();

      if (now - createdAt < MAX_PALENCA_WAITING_TIME_MS) {
        return;
      }

      // If the account.status is not pending we do nothing to avoid duplicate jobs
      if (account.status !== PalencaAccountStatus.pending) {
        return;
      }

      const palencaJobRetrieveMetrics: PalencaJobRetrieveMetrics = {
        accountId: account.accountId,
        platform: account.platform,
        requestId: requestId,
      };

      logger.info(
        `[checkEarningsAnalysis] Adding job to retrieve metrics for account ${account.accountId} and platform ${account.platform}`
      );
      addRetrieveMetricsJob(palencaJobRetrieveMetrics);
    });
  }

  return currentAdmissionRequest;
};

export const notificationEmailForCustomersWhoHasNotStartedOnboardingWithInTwoDays = async () => {
  try {
    logger.info(
      'Sending auto notification reminder email to customers who has not started onboarding within two days'
    );

    const lastTwoDaysDate = new Date();
    lastTwoDaysDate.setUTCDate(lastTwoDaysDate.getUTCDate() - 2);

    const lastTwoDaysDateStartDate = new Date(lastTwoDaysDate);
    lastTwoDaysDateStartDate.setUTCHours(0, 0, 0, 0);

    const lastTwoDaysDateEndDate = new Date(lastTwoDaysDate);
    lastTwoDaysDateEndDate.setUTCHours(23, 59, 59, 999);

    const registeredCustomersOflastTwoDays = await repoRetriveAllClientByDate(
      lastTwoDaysDateStartDate,
      lastTwoDaysDateEndDate
    );

    logger.info(
      `Total no of Registered customers of last two days: ${registeredCustomersOflastTwoDays.length}`
    );

    await Promise.allSettled(
      registeredCustomersOflastTwoDays.map(async (customer) => {
        const { email, firstName, lastName, country } = customer.personalData;
        try {
          await sendReminderEmailToCustomerAfterRegistering({
            customerEmail: email as string,
            customerName: `${firstName} ${lastName ? lastName : ''}`,
            customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${customer.id}`,
            country: country as Country,
          });
          logger.info(`Successfully sent auto notification onboarding reminder email to ${email}`);
        } catch (err) {
          logger.error(
            `Error occured while sending auto notification onboarding reminder email to ${email}   `
          );
        }
      })
    );
  } catch (err) {
    logger.error('Error occured while sending auto notification reminder email to customers', err);
  }
};

export const updatePlatformMetric = async (requestId: string, platformMetricPayload: PlatformMetric) => {
  try {
    logger.info(`Updating platform metric for admission request ${requestId}`);
    const platformMetric = await repoUpdatePlatformMetric(requestId, platformMetricPayload);
    await updatedImplementationOfEarningsAnalysis(requestId);

    return platformMetric;
  } catch (err: any) {
    logger.error(
      `Error occured while updating platform metric for admission request ${requestId}. ${err?.message}`,
      err?.stack
    );
    throw err;
  }
};

export const notifyCustomerAboutHomeVisitAppointmentBooking = async (
  requestId: string,
  appointmentMetaData: {
    meetingLink: string;
    date: string;
    startTime: string;
  }
) => {
  try {
    const { meetingLink, date, startTime } = appointmentMetaData;

    const admissionRequest = await repoGetAdmissionRequestById(requestId);
    const {
      personalData: { phone, email },
    } = admissionRequest;
    await sendHomeVisitAppointmentScheduledMessage({
      date,
      startTime,
      requestId: requestId,
      phone: phone!,
      meetingLink: getMeetingIdFromMeetingLink(meetingLink)!,
      type: 'homeVisitAppointmentScheduled',
    });

    await sendHomeVisitAppointmentScheduledEmail({
      date,
      startTime,
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      meetingLink,
      requestId: requestId,
    });
  } catch (error) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentBooking] Home visit appointment scheduled message failed to send to clientId ${requestId} due to error: ${error}`
    );
    throw error;
  }
};

export const notifyCustomerAboutHomeVisitAppointmentOneNightAgo = async (
  requestId: string,
  appointmentMetaData: {
    date: string;
    startTime: string;
    meetingLink: string;
  }
) => {
  try {
    const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);
    const {
      personalData: { phone, firstName },
    } = admissionRequest;

    const { date, startTime, meetingLink } = appointmentMetaData;
    await sendHomeVisitAppointmentReminderMessageOneNightAgoCron({
      name: firstName!,
      date,
      startTime,
      meetingLink: getMeetingIdFromMeetingLink(meetingLink)!,
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentReminderOneNightAgo',
    });

    await sendHomeVisitAppointmentReminderEmailOneNightAgo({
      email: admissionRequest.personalData.email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
      date,
      startTime,
      meetingLink,
    });
  } catch (error: any) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentOneNightAgo] - Home visit appointment reminder one night ago message failed to send to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const notifyCustomerAboutHomeVisitAppointmentAboutFiveMinutesBefore = async (
  requestId: string,
  appointmentMetaData: {
    date: string;
    startTime: string;
    meetingLink: string;
  }
) => {
  try {
    const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);
    const {
      personalData: { phone, firstName, email },
    } = admissionRequest;

    const { date, startTime, meetingLink } = appointmentMetaData;
    await sendHomeVisitAppointmentReminderMessageAboutFiveMinutes({
      name: firstName!,
      date,
      startTime,
      meetingLink: getMeetingIdFromMeetingLink(meetingLink)!,
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentReminderAboutFiveMinutes',
    });

    await sendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo({
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
      date,
      startTime,
      meetingLink,
    });
  } catch (error: any) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentAboutFiveMinutesBefore] - Home visit appointment reminder about five minutes ago message failed to send to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const notifyCustomerAboutHomeVisitAppointmentFinish = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { firstName, phone, email },
    } = currentAdmissionRequest;

    await sendHomeVisitAppointmentFinishMessage({
      name: firstName!,
      phone: phone!,
      type: 'homeVisitAppointmentFinish',
    });

    await sendHomeVisitAppointmentFinishEmail({
      email: email!,
      requestId: requestId,
    });
  } catch (error) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentFinish] - Home visit appointment finish message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const addAvalDataToAdmissionRequest = async (
  requestId: string,
  requestAvalData: RequestAvalData
): Promise<AdmissionRequest> => {
  const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId);
  logger.info(`[addAvalDataToAdmissionRequest] - adding aval data`);
  const saved = await repoAddRequestAvalData(currentAdmissionRequest.id!, requestAvalData);
  logger.info(`[addAvalDataToAdmissionRequest] - aval data added`);
  return saved;
};

export const notifyCustomerAboutHomeVisitAppointmentNoShow = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { firstName, phone },
    } = currentAdmissionRequest;

    await sendHomeVisitAppointmentNoShowMessage({
      name: firstName!,
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentNoShow',
    });

    await sendHomeVisitAppointmentNoShowMessageEmail({
      name: firstName!,
      email: currentAdmissionRequest.personalData.email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
    });
  } catch (error) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentNoShow] - Home visit appointment finish message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const notifyCustomerAboutHomeVisitAppointmentApology = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { firstName, phone, email },
    } = currentAdmissionRequest;

    await sendHomeVisitAppointmentApologyMessage({
      name: firstName!,
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentApology',
    });

    await sendHomeVisitAppointmentApologyEmail({
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
    });
  } catch (error) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentApology] - Home visit appointment finish message failed to send to clientId ${requestId} due to error: ${error}`
    );
  }
};

export const notifyCustomerAboutHomeVisitAppointmentCancel = async (requestId: string) => {
  try {
    const currentAdmissionRequest = await getAdmissionRequestByIdOrThrow(requestId as unknown as string);

    const {
      personalData: { phone, email },
    } = currentAdmissionRequest;

    await sendHomeVisitAppointmentCancelMessage({
      phone: phone!,
      requestId: requestId,
      type: 'homeVisitAppointmentCancel',
    });

    await sendHomeVisitAppointmentCancelEmail({
      email: email!,
      customerWebAppLink: `${DRIVER_WEB_APP_URL}/${requestId}/schedule-home-visit?step=reschedule`,
      requestId: requestId,
    });
  } catch (error: any) {
    logger.error(
      `[notifyCustomerAboutHomeVisitAppointmentCancel] - Home visit appointment cancel message failed to send to clientId ${requestId}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

export const resetHomeVisitScheduleLinkSendDateToToday = async (requestId: string, userId: string) => {
  const current = await getAdmissionRequestByIdOrThrow(requestId);
  const previousHomeVisitScheduleLinkSendDate = current.homeVisitScheduleLinkSendDate;

  await repoUpdateHomeVisitAppointmentSchedulingLinkSendDate(current.id!, new Date());
  logger.info(
    `[updateHomeVisitScheduleLinkSendDate] - home visit schedule link send date updated for ${requestId}`
  );
  const event = new Event({
    userId: userId,
    entityId: requestId,
    entityType: EntityType.admission_request,
    actionType: ActionType['admission_request.homeVisitScheduleLinkSendDate.reset'],
    message: `Fecha de envío del enlace de programación de la visita domiciliaria restablecida, fecha anterior: ${previousHomeVisitScheduleLinkSendDate}`,
  });
  await repoSaveEvent(event);
};
