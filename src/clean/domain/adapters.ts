/* eslint-disable prettier/prettier */
import {
  AdmissionRequest,
  Earning,
  Metric,
  RequestPersonalData,
  Document,
  RequestDocument,
  RequestPalenca,
  PalencaAccount,
  HomeVisit,
  DailyEarning,
  WeeklyEarning,
  EarningsAnalysis,
  RequestDocumentsAnalysis,
  Event,
  User,
  RiskAnalysis,
  RiskAnalysisData,
  RiskAnalysisDataVariables,
  Scorecard,
  ScorecardDetail,
  ScorecardDetailCategory,
  PalencaAccountRetrieval,
  PlatformMetric,
  RequestAvalData,
  ModelScores,
  ModelResult,
} from './entities';
import {
  AdmissionRequestMongoI,
  EarningsAnalysisMongoI,
  PalencaAccountMongoI,
  RequestDocumentsAnalysisMongoI,
  RequestDocumentsMongoI,
  RequestHomeVisitMongoI,
  RequestPalencaMongoI,
  RequestPersonalDataMongoI,
  RiskAnalysisMongoI,
  ScorecardDetailMongoI,
  ScorecardMongoI,
  ScorecardCategoryMongoI,
  PalencaAccountRetrievalMongoI,
  RequestAvalDataMongoI,
  ModelScoresMongoI,
  ModelResultMongoI,
} from '../../models/admissionRequestSchema';
import { PalencaEarning, PalencaMetrics } from '../data/palencaModels';
import {
  CurrencyCode,
  GigPlatform,
  HomeVisitStatus,
  MediaStatus,
  MediaType,
  PalencaAccountStatus,
  EarningsAnalysisStatus,
  RequestDocumentStatus,
  RequestDocumentsAnalysisStatus,
  RequestPersonalDataStepStatus,
  ResidentOwnershipStatus,
  AdmissionRequestDocumentType,
  AdmissionRequestRejectionReason,
  EntityType,
  ActionType,
  ScorecardVariableName,
  AnalysisStatus,
  ScorecardVersion,
  RiskCategory,
  PalencaRetrievalStatus,
  MLModels,
} from './enums';
import { EarningMongoI, IDailyEarning, IWeeklyEarning } from '../../models/earningSchema';
import { MetricMongoI } from '../../models/metricsSchema';
import { DocumentMongoI } from '../../models/documentSchema';
import { EventMongoI } from '../../models/eventSchema';
import { UserMongoI } from '../../models/userSchema';
import { RiskAnalysisDataMongoI } from '../../models/riskAnalysisDataSchema';
import { convertTimeStringToDays } from '../lib/palenca';
import { ISchedule } from '@/models/Schedules';
import { ISlots } from '@/models/Slots';
import { IAppointment } from '@/models/appointment';

export const palencaAccountRetrievalMongoAdapter = (
  palencaAccountRetrieval: PalencaAccountRetrievalMongoI
): PalencaAccountRetrieval => {
  const entity = new PalencaAccountRetrieval({
    status: palencaAccountRetrieval.status as PalencaRetrievalStatus,
  });

  return entity;
};

export const palencaAccountAdapter = (palencaAccount: PalencaAccountMongoI): PalencaAccount => {
  const palencaAccountEntity = new PalencaAccount({
    accountId: palencaAccount.accountId,
    platform: palencaAccount.platform as GigPlatform,
    earnings: palencaAccountRetrievalMongoAdapter(palencaAccount.earnings),
    metrics: palencaAccountRetrievalMongoAdapter(palencaAccount.metrics),
    status: palencaAccount.status as PalencaAccountStatus,
    createdAt: new Date(palencaAccount.createdAt),
  });
  return palencaAccountEntity;
};

export const requestPalencaAdapter = (requestPalenca: RequestPalencaMongoI): RequestPalenca => {
  const accounts = requestPalenca.accounts ? requestPalenca.accounts.map(palencaAccountAdapter) : [];

  const requestPalencaEntity = new RequestPalenca({
    widgetId: requestPalenca.widgetId,
    externalId: requestPalenca.externalId,
    accounts,
  });
  return requestPalencaEntity;
};

const transformReferences = (references: any) => {
  return {
    reference1Name: references ? references?.reference1Name : '',
    reference1Phone: references ? references?.reference1Phone : '',
    reference1Relationship: references ? references?.reference1Relationship : '',
    reference1Address: references ? references?.reference1Address : '',
    reference2Name: references ? references?.reference2Name : '',
    reference2Phone: references ? references?.reference2Phone : '',
    reference2Relationship: references ? references?.reference2Relationship : '',
    reference2Address: references ? references?.reference2Address : '',
    reference3Name: references ? references?.reference3Name : '',
    reference3Phone: references ? references?.reference3Phone : '',
    reference3Relationship: references ? references?.reference3Relationship : '',
    reference3Address: references ? references?.reference3Address : '',
  };
};

export const requestPersonalDataMongoAdapter = (
  requestPersonalData: RequestPersonalDataMongoI
): RequestPersonalData => {
  const entity = new RequestPersonalData({
    status: requestPersonalData.status as RequestPersonalDataStepStatus,
    firstName: requestPersonalData.firstName,
    lastName: requestPersonalData.lastName,
    email: requestPersonalData.email,
    phone: requestPersonalData.phone,
    birthdate: requestPersonalData.birthdate,
    taxId: requestPersonalData.taxId,
    nationalId: requestPersonalData.nationalId,
    postalCode: requestPersonalData.postalCode,
    city: requestPersonalData.city,
    state: requestPersonalData.state,
    neighborhood: requestPersonalData.neighborhood,
    street: requestPersonalData.street,
    streetNumber: requestPersonalData.streetNumber,
    department: requestPersonalData.department,
    country: requestPersonalData.country,
    vehicleSelected: requestPersonalData.vehicleSelected,
    ssn: requestPersonalData.ssn,
    rideShareTotalRides: requestPersonalData.rideShareTotalRides,
    avgEarningPerWeek: requestPersonalData.avgEarningPerWeek,
    termsAndConditions: requestPersonalData.termsAndConditions,
    dataPrivacyConsentForm: requestPersonalData.dataPrivacyConsentForm,
    nationality: requestPersonalData.nationality,
    age: requestPersonalData.age,
    occupation: requestPersonalData.occupation,
    homePhone: requestPersonalData.homePhone,
    timeInResidency: requestPersonalData.timeInResidency,
    municipality: requestPersonalData.municipality,
    maritalStatus: requestPersonalData.maritalStatus,
    dependents: requestPersonalData.dependents,
    spouseOrPartnerIncome: requestPersonalData.spouseOrPartnerIncome,
    partnerSourceOfIncome: requestPersonalData.partnerSourceOfIncome,
    partnerName: requestPersonalData.partnerName,
    partnerPhone: requestPersonalData.partnerPhone,
    noOfDependents: requestPersonalData.noOfDependents,
    dependendsInfo: requestPersonalData.dependendsInfo?.map((_dependent) => {
      return {
        dependendName: _dependent.dependendName,
        dependendPhone: _dependent.dependendPhone,
        dependentRelationship: _dependent.dependentRelationship,
      };
    }),
    ownACar: requestPersonalData.ownACar,
    carLeasingtime: requestPersonalData.carLeasingtime,
    carMake: requestPersonalData.carMake,
    carModel: requestPersonalData.carModel,
    ownDebt: requestPersonalData.ownDebt,
    outStandingDebt: requestPersonalData.outStandingDebt,
    doesDebtAffectPersonalFinance: requestPersonalData.doesDebtAffectPersonalFinance,
    references: transformReferences(requestPersonalData.references),
    learnAboutOcn: requestPersonalData.learnAboutOcn,
    referrer:
      requestPersonalData.referrer && requestPersonalData.referrer.name
        ? {
          name: requestPersonalData.referrer.name,
          phone: requestPersonalData.referrer.phone,
          email: requestPersonalData.referrer.email,
        }
        : undefined,
    doesItApplyToElectricCars: requestPersonalData.doesItApplyToElectricCars,
    ficoScore: requestPersonalData.ficoScore,
    criminalBackgroundCheck: requestPersonalData.criminalBackgroundCheck,
    motorVehicleRecordCheck: requestPersonalData.motorVehicleRecordCheck,
    privacyPolicy: requestPersonalData.privacyPolicy,
    ocnBackgroundAndCreditCheckForApplication: requestPersonalData.ocnBackgroundAndCreditCheckForApplication,
  });
  return entity;
};

export const documentMongoAdapter = (document: DocumentMongoI, signedUrl?: string): Document => {
  const url = signedUrl ? signedUrl : null;
  const entity = new Document({
    id: document._id.toString(),
    fileName: document.originalName,
    path: document.path,
    type: document.type as MediaType,
    status: document.status as MediaStatus,
    mimeType: document.mimeType,
    url,
    createdAt: new Date(document.createdAt),
    updatedAt: new Date(document.updatedAt),
  });

  return entity;
};

export const requestDocumentAdapter = (document: RequestDocumentsMongoI): RequestDocument => {
  const entity = new RequestDocument({
    mediaId: document.mediaId ? document.mediaId.toString() : null,
    status: document.status as RequestDocumentStatus,
    type: document.type as AdmissionRequestDocumentType,
  });

  return entity;
};

export const requestDocumentWithMediaAdapter = (
  document: RequestDocumentsMongoI,
  media?: Document
): RequestDocument => {
  const availableMedia = media ? media : null;
  const entity = new RequestDocument({
    mediaId: document.mediaId ? document.mediaId.toString() : null,
    status: document.status as RequestDocumentStatus,
    type: document.type as AdmissionRequestDocumentType,
    media: availableMedia,
  });

  return entity;
};

export const requestDocumentsAnalysisWithMediaAdapter = (
  status: RequestDocumentsAnalysisStatus, //?
  documents: RequestDocument[]
): RequestDocumentsAnalysis => {
  const entity = new RequestDocumentsAnalysis({
    status: status as RequestDocumentsAnalysisStatus,
    documents,
  });
  return entity;
};

export const requestDocumentsAnalysisMongoAdapter = (
  requestDocuments: RequestDocumentsAnalysisMongoI
): RequestDocumentsAnalysis => {
  const documents = requestDocuments.documents.map(requestDocumentAdapter);

  const entity = new RequestDocumentsAnalysis({
    status: requestDocuments.status as RequestDocumentsAnalysisStatus,
    documents,
  });
  return entity;
};

export const homeVisitMongoAdapter = (
  homeVisit: RequestHomeVisitMongoI,
  media?: Document[] | null
): HomeVisit => {
  const availableMedia = media ? media : null;
  const entity = new HomeVisit({
    isAddressProvidedByApplicant: homeVisit.isAddressProvidedByApplicant,
    residentOwnershipStatus: homeVisit.residentOwnershipStatus as ResidentOwnershipStatus,
    hasGarage: homeVisit.hasGarage,
    comments: homeVisit.comments,
    images: homeVisit.images,
    status: homeVisit.status as HomeVisitStatus,
    responsible: homeVisit.responsible,
    visitDate: homeVisit.visitDate,
    media: availableMedia,
    visitTime: homeVisit.visitTime,
    houseInformation: homeVisit.houseInformation,
    proofOfPropertyOwnership: homeVisit.proofOfPropertyOwnership,
    visitorEmailAddress: homeVisit.visitorEmailAddress,
    doesProofOfAddressMatchLocation: homeVisit.doesProofOfAddressMatchLocation,
    characteristicsOfGarage: homeVisit.characteristicsOfGarage,
    behaviourOfCustomerDuringCall: homeVisit.behaviourOfCustomerDuringCall,
    homeVisitStepsStatus: homeVisit.homeVisitStepsStatus,
    reasonOfRejection: homeVisit.reasonOfRejection,
    suggestedStatus: homeVisit.suggestedStatus,
    statusReason: homeVisit.statusReason,
  });

  return entity;
};

export const dailyEarningMongoAdapter = (dailyEarning: IDailyEarning): DailyEarning => {
  const entity = new DailyEarning({
    amount: dailyEarning.amount,
    countTrips: dailyEarning.countTrips,
    earningDate: dailyEarning.earningDate,
    currency: dailyEarning.currency as CurrencyCode,
  });
  return entity;
};

export const weeklyEarningMongoAdapter = (weeklyEarning: IWeeklyEarning): WeeklyEarning => {
  const dailyEarnings = weeklyEarning.dailyEarnings.map(dailyEarningMongoAdapter);
  const entity = new WeeklyEarning({
    totalAmount: weeklyEarning.totalAmount,
    totalTrips: weeklyEarning.totalTrips,
    fromDate: weeklyEarning.fromDate,
    toDate: weeklyEarning.toDate,
    week: weeklyEarning.week,
    year: weeklyEarning.year,
    currency: weeklyEarning.currency as CurrencyCode,
    dailyEarnings,
  });
  return entity;
};

export const earningsAnalysisMongoAdapter = (earningsAnalysis: EarningsAnalysisMongoI): EarningsAnalysis => {
  const entity = new EarningsAnalysis({
    totalEarnings: earningsAnalysis.totalEarnings,
    earnings: earningsAnalysis.earnings.map(weeklyEarningMongoAdapter),
    platforms: earningsAnalysis.platforms,
    status: earningsAnalysis.status as EarningsAnalysisStatus,
  });
  return entity;
};

export const scorecardCategoryMongoAdapter = (
  scorecardCategory: ScorecardCategoryMongoI
): ScorecardDetailCategory => {
  const entity = new ScorecardDetailCategory({
    threshold: scorecardCategory.threshold as { min: number; max: number },
    riskCategory: scorecardCategory.riskCategory as RiskCategory,
    riskScore: scorecardCategory.riskScore,
    weight: scorecardCategory.weight,
    result: scorecardCategory.result,
  });

  return entity;
};

export const scorecardDetailMongoAdapter = (scorecardDetail: ScorecardDetailMongoI): ScorecardDetail => {
  const entity = new ScorecardDetail({
    variable: scorecardDetail.variable as ScorecardVariableName,
    category: scorecardCategoryMongoAdapter(scorecardDetail.category),
  });

  return entity;
};

export const scorecardMongoAdapter = (scorecard: ScorecardMongoI): Scorecard => {
  const entity = new Scorecard({
    totalScore: scorecard.totalScore,
    scaledScore: scorecard.scaledScore,
    minScore: scorecard.minScore,
    maxScore: scorecard.maxScore,
    minScaledScore: scorecard.minScaledScore,
    maxScaledScore: scorecard.maxScaledScore,
    details: scorecard.details.map(scorecardDetailMongoAdapter),
  });
  return entity;
};

export const modelResultMongoAdapter = (modelResult: ModelResultMongoI): ModelResult => {
  if (!modelResult) {
    return null as unknown as ModelResult;
  }
  return new ModelResult({
    modelName: modelResult.modelName,
    status: modelResult.status as AnalysisStatus,
    modelScore: modelResult.modelScore,
    modelFeatureScores: modelResult.modelFeatureScores,
    modelWeights: modelResult.modelWeights,
    modelResult: modelResult.modelResult,
  });
};

export const modelScoresMongoAdapter = (modelScores: ModelScoresMongoI): ModelScores => {
  const entity = new ModelScores({
    [MLModels.RIDESHARE_PERFORMANCE]: modelResultMongoAdapter(modelScores[MLModels.RIDESHARE_PERFORMANCE]),
    [MLModels.FINANCIAL_ASSESSMENT]: modelResultMongoAdapter(modelScores[MLModels.FINANCIAL_ASSESSMENT]),
    [MLModels.PERSONAL_INFORMATION]: modelResultMongoAdapter(modelScores[MLModels.PERSONAL_INFORMATION]),
    [MLModels.HOMEVISIT_INFORMATION]: modelResultMongoAdapter(modelScores[MLModels.HOMEVISIT_INFORMATION]),
    // [MLModels.DRIVING_AND_LEGAL_HISTORY]: 
    //   modelResultMongoAdapter(modelScores[MLModels.DRIVING_AND_LEGAL_HISTORY]),
  });
  return entity;
};


export const riskAnalysisMongoAdapter = (riskAnalysis: RiskAnalysisMongoI): RiskAnalysis => {
  const scorecard = riskAnalysis.scorecard ? scorecardMongoAdapter(riskAnalysis.scorecard) : null;

  const entity = new RiskAnalysis({
    status: riskAnalysis.status as AnalysisStatus,
    scorecardVersion: riskAnalysis.scorecardVersion as ScorecardVersion,
    scorecard,
  });
  return entity;
};

export const screenshotsAdapter = (_screenshots: any) => {
  return _screenshots.map((_val: any) => {
    return {
      platform: _val.platform,
      url: _val.document.path,
      name: _val.document.originalName,
    };
  });
};

export const requestAvalDataMongoAdapter = (
  requestAvalData: RequestAvalDataMongoI
): RequestAvalData => {
  return new RequestAvalData({
    name: requestAvalData.name,
    phone: requestAvalData.phone,
    email: requestAvalData.email,
    location: requestAvalData.location,
  });
}

export const admissionRequestMongoAdapter = (
  admissionRequestMongo: AdmissionRequestMongoI
): AdmissionRequest => {
  const palenca = requestPalencaAdapter(admissionRequestMongo.palenca);
  const personalData = requestPersonalDataMongoAdapter(admissionRequestMongo.personalData);
  const documentsAnalysis = requestDocumentsAnalysisMongoAdapter(admissionRequestMongo.documentsAnalysis);
  const earningsAnalysis = earningsAnalysisMongoAdapter(admissionRequestMongo.earningsAnalysis);
  const homeVisit = admissionRequestMongo.homeVisit
    ? homeVisitMongoAdapter(admissionRequestMongo.homeVisit)
    : undefined;
  const riskAnalysis = riskAnalysisMongoAdapter(admissionRequestMongo.riskAnalysis);
  const modelScores = admissionRequestMongo.modelScores
    ? modelScoresMongoAdapter(admissionRequestMongo.modelScores)
    : undefined;
  const screenshots = admissionRequestMongo.screenshots
    ? screenshotsAdapter(admissionRequestMongo.screenshots)
    : [];
  const avalData = admissionRequestMongo.avalData
    ? requestAvalDataMongoAdapter(admissionRequestMongo.avalData)
    : undefined;

  const admissionRequest = new AdmissionRequest({
    id: admissionRequestMongo._id.toString(),
    status: admissionRequestMongo.status,
    convertedToAssociate: admissionRequestMongo.convertedToAssociate || false, 
    rejectionReason: admissionRequestMongo.rejectionReason as AdmissionRequestRejectionReason,
    palenca,
    personalData,
    documentsAnalysis,
    homeVisit: homeVisit,
    earningsAnalysis: earningsAnalysis,
    riskAnalysis: riskAnalysis,
    modelScores: modelScores,
    screenshots: screenshots,
    createdAt: new Date(admissionRequestMongo.createdAt),
    updatedAt: new Date(admissionRequestMongo.updatedAt),
    source: admissionRequestMongo.source,
    clientIpAddress: admissionRequestMongo.clientIpAddress,
    homeVisitScheduleLinkSendDate: admissionRequestMongo.homeVisitScheduleLinkSendDate,
    avalData: avalData,
    hubspot: admissionRequestMongo.hubspot,
    hilos: admissionRequestMongo.hilos,
    typeOfPreapproval: admissionRequestMongo.typeOfPreapproval,
    agentId: admissionRequestMongo.agentId,
  });
  return admissionRequest;
};

export const earningPalencaAdapter = (
  earning: PalencaEarning,
  platform: GigPlatform,
  requestId: string
): Earning => {
  const earningEntity = new Earning({
    amount: earning.amount || 0,
    currency: earning.currency,
    earningDate: new Date(earning.earning_date),
    cashAmount: earning.cash_amount || 0,
    countTrips: earning.count_trips || 0,
    requestId,
    platform,
  });
  return earningEntity;
};

export const earningMongoAdapter = (earning: EarningMongoI): Earning => {
  const earningEntity = new Earning({
    id: earning._id.toString(),
    amount: earning.amount,
    currency: earning.currency as CurrencyCode,
    earningDate: new Date(earning.earningDate),
    cashAmount: earning.cashAmount,
    countTrips: earning.countTrips,
    requestId: earning.requestId.toString(),
    platform: earning.platform as GigPlatform,
    createdAt: new Date(earning.createdAt),
    updatedAt: new Date(earning.updatedAt),
  });
  return earningEntity;
};

export const metricPalencaAdapter = (
  metric: PalencaMetrics,
  platform: GigPlatform,
  requestId: string
): Metric => {
  const metricEntity = new Metric({
    requestId,
    acceptanceRate: metric.acceptance_rate,
    cancellationRate: metric.cancellation_rate,
    rating: metric.rating,
    lifetimeTrips: metric.lifetime_trips,
    timeSinceFirstTrip: convertTimeStringToDays(metric.time_since_first_trip),
    activationStatus: metric.activation_status,
    platform,
  });
  return metricEntity;
};

export const metricMongoAdapter = (metric: MetricMongoI): Metric => {
  const metricEntity = new Metric({
    id: metric._id.toString(),
    requestId: metric.requestId.toString(),
    acceptanceRate: metric.acceptanceRate,
    cancellationRate: metric.cancellationRate,
    rating: metric.rating,
    lifetimeTrips: metric.lifetimeTrips,
    timeSinceFirstTrip: metric.timeSinceFirstTrip,
    activationStatus: metric.activationStatus,
    platform: metric.platform as GigPlatform,
    createdAt: new Date(metric.createdAt),
    updatedAt: new Date(metric.updatedAt),
  });
  return metricEntity;
};

export const userAdapter = (user: UserMongoI): User => {
  const userEntity = new User({
    id: user._id.toString(),
    name: user.name,
    image: user.image ? user.image.toString() : user.image,
  });
  return userEntity;
};

export const eventMongoAdapter = (event: EventMongoI): Event => {
  const user = event.user ? userAdapter(event.user) : undefined;
  const userId = event.user ? event.user._id.toString() : event.user;

  const eventEntity = new Event({
    id: event._id.toString(),
    userId: userId,
    entityId: event.entityId.toString(),
    entityType: event.entityType as EntityType,
    actionType: event.actionType as ActionType,
    message: event.message,
    createdAt: new Date(event.createdAt),
    user: user,
  });
  return eventEntity;
};

export const riskAnalysisDataMongoAdapter = (riskAnalysisMongo: RiskAnalysisDataMongoI): RiskAnalysisData => {
  const entity = new RiskAnalysisData({
    id: riskAnalysisMongo._id.toString(),
    requestId: riskAnalysisMongo.requestId.toString(),
    variables: riskAnalysisMongo.variables as RiskAnalysisDataVariables,
    createdAt: new Date(riskAnalysisMongo.createdAt),
    updatedAt: new Date(riskAnalysisMongo.updatedAt),
  });
  return entity;
};

export const PlatformMetricMongoAdapter = (platformMetric: MetricMongoI): PlatformMetric => {
  const platformMetricEntity = new PlatformMetric({
    requestId: platformMetric._id.toString(),
    acceptanceRate: platformMetric.acceptanceRate,
    cancellationRate: platformMetric.cancellationRate,
    rating: platformMetric.rating,
    lifetimeTrips: platformMetric.lifetimeTrips,
    timeSinceFirstTrip: platformMetric.timeSinceFirstTrip,
    activationStatus: platformMetric.activationStatus,
  });
  return platformMetricEntity;
};

export const ScheduleMongoAdapter = (
  schedule: ISchedule,
  blockSlotsErrors?: Array<Record<string, string>>,
  addedSlotsErrors?: Array<Record<string, string>>
) => {
  return {
    id: schedule._id.toString(),
    user: schedule.user.toString(),
    name: schedule.name,
    weeklySchedule: {
      monday: schedule.weeklySchedule.monday,
      tuesday: schedule.weeklySchedule.tuesday,
      wednesday: schedule.weeklySchedule.wednesday,
      thursday: schedule.weeklySchedule.thursday,
      friday: schedule.weeklySchedule.friday,
      saturday: schedule.weeklySchedule.saturday,
      sunday: schedule.weeklySchedule.sunday,
    },
    maxSimultaneousAppointments: schedule.maxSimultaneousAppointments,
    timezone: schedule.timezone,
    breakTimes: schedule.breakTimes?.map((breakTime) => ({
      start: breakTime.start,
      end: breakTime.end,
    })),
    bufferTime: schedule.bufferTime,
    duration: schedule.duration,
    maxAdvanceBookingDays: schedule.maxAdvanceBookingDays,
    minBookingNoticeHours: schedule.minBookingNoticeHours,
    blockSlotsErrors: blockSlotsErrors,
    addedSlotsErrors: addedSlotsErrors,
  };
};

export const SlotsMongoAdapter = (slots: ISlots | Array<ISlots>) => {

  if(Array.isArray(slots)) {
    return slots.map(slot => ({
      id: slot._id.toString(),
      date: slot.date,
      startTime: slot.startTime.toISOString(),
      endTime: slot.endTime.toISOString(),
      isAvailable: slot.isAvailable,
      timezone: slot.timezone,
    }))
  }
  return {
    id: slots._id.toString(),
    date: slots.date,
    startTime: slots.startTime.toISOString(),
    endTime: slots.endTime.toISOString(),
    isAvailable: slots.isAvailable,
    timezone: slots.timezone,
  }
};

export const AppointmentMongoAdapter = (appointments: IAppointment | Array<IAppointment>, 
  isAdminPopulation = false) => {
   
  if(Array.isArray(appointments)){
    return appointments.map(appointment => ({
      id: appointment._id.toString(),
      user: appointment.user.toString(),
      startTime: appointment.startTime.toISOString(),
      endTime: appointment.endTime.toISOString(),
      status: appointment.status,
      description: appointment.description,
      admissionRequestId: appointment.admissionRequestId,
      title: appointment.title,
      date: appointment.date,
      slot: appointment.slot.toString(),
      meetingLink: appointment.meetingLink,
      ...(isAdminPopulation && { statusHistory: appointment?.statusHistory?.reverse() }),
    }))
   }

   const appointment = {
    id: appointments._id.toString(),
    startTime: appointments.startTime.toISOString(),
    endTime: appointments.endTime.toISOString(),
    status: appointments.status,
    description: appointments.description,
    admissionRequestId: appointments.admissionRequestId,
    title: appointments.title,
    date: appointments.date,
    slot: appointments.slot.toString(),
    meetingLink: appointments.meetingLink,
    user: appointments.user ? appointments.user : undefined,
    ...(isAdminPopulation && { statusHistory: appointments?.statusHistory?.reverse() }),
   }

   return appointment;
}
