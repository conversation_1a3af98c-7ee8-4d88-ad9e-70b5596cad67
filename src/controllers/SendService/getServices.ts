import AwaitingInsuranceStock from '@/models/awaitingInsuranceSchema';
import { genericMessages } from '../../constants';
import ServiceStock from '../../models/serviceStockSchema';
import { AsyncController } from '../../types&interfaces/types';

export const getServicesByVehicleId: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  try {
    const stockServices = await ServiceStock.find({ stockId: vehicleId }, null, {
      sort: { createdAt: 1 },
    });
    return res.status(200).send({ stockServices });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};

export const getInsuranceByVehicleId: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  try {
    const insuranceServices = await AwaitingInsuranceStock.find({ stockId: vehicleId }).sort({
      createdAt: -1,
    });
    return res.status(200).send({ insuranceServices });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};
