/* eslint-disable @typescript-eslint/naming-convention */
import type { AsyncController } from '../../types&interfaces/types';
import { AdmissionRequestMongo } from '../../models/admissionRequestSchema';

export const getTypeOfStatus: AsyncController = async (req, res) => {
  const requestId = req.params.requestId;
  const request = await AdmissionRequestMongo.findOne({ _id: requestId });
  if (!request) {
    return res.status(404).send('Request not found');
  }
  const typeOfPreapproval = request.typeOfPreapproval;
  return res.status(200).send({
    message: typeOfPreapproval,
  });
};
