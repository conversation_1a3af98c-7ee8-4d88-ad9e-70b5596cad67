import type { AsyncController } from '../types&interfaces/types';
import { genericMessages, userRoles } from '../constants';
import { gpsActionAndStock, gpsStatus, setEvents } from '../services/gpsActions';
import { getUserData } from '../services/getUserData';
import StockVehicle from '../models/StockVehicleSchema';
import GpsStatusSchema from '../models/gpsStatusSchema';

export const changeGPSStatus: AsyncController = async (req, res) => {
  const { gps, comando, historyData } = req.body;
  const { userId: adminId } = req.userId;

  if (!gps || !comando || !adminId) {
    return res.status(400).json({ message: genericMessages.errors.missingParams });
  }
  try {
    const user = await getUserData(adminId);
    if (!user) {
      throw new Error(genericMessages.errors.missingParams);
    }
    if (!userRoles.admin.includes(user.role)) {
      throw new Error(genericMessages.errors.unauthorized);
    }
    const props = {
      gps,
      comando,
      user,
      historyData,
    };
    const result = await gpsActionAndStock(props);
    return res.status(200).json({ message: result });
  } catch (error: any) {
    console.error(error.message);
    return res.status(500).json({ error: error.message });
  }
};

export const getGpsStatus: AsyncController = async (req, res) => {
  const { gps, report = 'engineStatus' } = req.params;

  if (!gps) {
    return res.status(400).json({ message: genericMessages.errors.missingParams });
  }
  try {
    const result = await gpsStatus(gps.toString(), report.toString()); // Cast 'gps' to string
    return res.status(200).json({ message: result });
  } catch (error: any) {
    console.error(error);
    return res.status(500).json({ error: error });
  }
};

export const sendAllGpsStatusCommand: AsyncController = async (req, res) => {
  const gpsAndStatusResult = [];
  try {
    const gpsList = await StockVehicle.find({ gpsNumber: { $exists: true } }, { gpsNumber: 1, _id: 0 });
    const gpsListArray = gpsList.map((gps) => gps.gpsNumber);
    for (const gps of gpsListArray) {
      if (gps) {
        console.log(gps);
        try {
          const gpsStatusResult = await gpsStatus(gps, 'engineStatus'); // Cast 'gps' to string
          gpsAndStatusResult.push({ gps, gpsStatusResult });
          const newGpsStatus = new GpsStatusSchema({ gps, status: gpsStatusResult });
          await newGpsStatus.save();
        } catch (error: any) {
          console.error(error);
          gpsAndStatusResult.push({ gps, error });
          new GpsStatusSchema({ gps, status: error }).save();
        }
      }
    }
    console.log(gpsAndStatusResult);
    return res.status(200).json({ message: 'iniciando proceso de envio de estados' });
  } catch (error: any) {
    console.error(error);
    return res.status(500).json({ error: error });
  }
};
export const blockCarAllCars: AsyncController = async (req, res) => {
  try {
    return res.status(200).json();
  } catch (error: any) {
    console.error(error);
    return res.status(500).json({ error: error });
  }
};

export const setEvent: AsyncController = async (req, res) => {
  try {
    const { gps, event } = req.body;
    if (!gps || !event) {
      return res.status(400).json({ message: genericMessages.errors.missingParams });
    }
    const result = await setEvents(gps, event);
    return res.status(200).json({ message: result });
  } catch (error: any) {
    console.error(error);
    return res.status(500).json({ error: error });
  }
};
