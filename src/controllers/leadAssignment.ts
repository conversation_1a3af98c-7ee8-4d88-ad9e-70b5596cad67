/* eslint-disable @typescript-eslint/naming-convention */
import { Request, Response } from 'express';
import { Types, Error as MongooseError } from 'mongoose';
import { ZodError, z } from 'zod';
import { AdmissionRequestMongo } from '../models/admissionRequestSchema';
import { logger } from '../clean/lib/logger';

// Allowed fields for filtering/sorting/selection
const ALLOWED_FIELDS = [
  '_id',
  'createdAt',
  'personalData.firstName',
  'personalData.lastName',
  'status',
  'typeOfPreapproval',
  'documentsAnalysis.status',
  'personalData.phone',
  'personalData.email',
  'personalData.state',
  'personalData.city',
  'personalData.age',
  'agentId',
  'agentName', // Newly added
];

// Zod schema for query parameters
const QuerySchema = z
  .object({
    page: z.coerce.number().int().min(1).default(1),
    limit: z.coerce.number().int().min(1).max(100).default(10),
    sortField: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    fields: z.string().optional(),
    // Explicitly validate _id
    _id: z
      .string()
      .optional()
      .refine((val) => !val || Types.ObjectId.isValid(val), {
        message: 'Invalid ObjectId format for _id',
      }),
    agentId: z
      .string()
      .optional()
      .refine((val) => !val || Types.ObjectId.isValid(val), {
        message: 'Invalid ObjectId format for agentId',
      }),
    agentName: z.string().optional(),
  })
  .catchall(z.unknown());

// Prevent NoSQL injection by sanitizing query operators
const sanitizeQuery = (query: Record<string, any>): Record<string, any> => {
  const sanitized: Record<string, any> = {};
  for (const [key, value] of Object.entries(query)) {
    if (
      key.startsWith('$') &&
      !['$regex', '$options'].includes(key) // Allow safe internal operators
    ) {
      throw new Error(`Invalid query parameter: ${key}`);
    }
    if (
      typeof value === 'object' &&
      !Array.isArray(value) &&
      value !== null &&
      key !== '_id' &&
      key !== 'agentId' &&
      key !== 'agentName'
    ) {
      sanitized[key] = sanitizeQuery(value);
    } else {
      sanitized[key] = value;
    }
  }
  return sanitized;
};

// Parse complex filters like age[gte]=25
const parseFilters = (filters: Record<string, any>): Record<string, any> => {
  const query: Record<string, any> = {};
  Object.entries(filters).forEach(([key, value]) => {
    if (!key.includes('[') && !ALLOWED_FIELDS.includes(key) && key !== 'agentName') return;

    // Handle agentName filter
    if (key === 'agentName') {
      query.agentName = { $regex: value as string, $options: 'i' };
      return;
    }

    // Handle nested fields with operators
    const fieldPath = key.replace(/\.$/, '');
    if (/\[.*?\]/.test(fieldPath)) {
      const match = fieldPath.match(/(.*?)$$(.*?)$$/);
      if (match) {
        const [, field, operator] = match;
        const validOperators = ['gte', 'lte', 'gt', 'lt', 'ne', 'in', 'nin'];
        if (!validOperators.includes(operator)) return;

        const fieldValue =
          operator === 'in' || operator === 'nin'
            ? value.split(',')
            : isNaN(Number(value))
              ? value
              : Number(value);

        query[field] = {
          ...query[field],
          [`$${operator}`]: fieldValue,
        };
      }
    } else if (value) {
      // Special case for ObjectId
      if (key === '_id') {
        query._id = new Types.ObjectId(value as string);
      } else if (key === 'agentId') {
        query.agentId = new Types.ObjectId(value as string);
      } else if (key === 'createdAt' && value.includes(',')) {
        const [start, end] = value.split(',');
        query.createdAt = {
          ...(start ? { $gte: new Date(start) } : {}),
          ...(end ? { $lte: new Date(end) } : {}),
        };
      } else if (
        [
          'personalData.firstName',
          'personalData.lastName',
          'personalData.phone',
          'personalData.email',
          'personalData.state',
          'personalData.city',
        ].includes(key)
      ) {
        query[key] = { $regex: value as string, $options: 'i' };
      } else if (key === 'status') {
        query.status = { $in: value.split(',').map((v: string) => v.trim()) };
      } else {
        query[key] = value;
      }
    }
  });
  return query;
};

export const getAdmissionRequests = async (req: Request, res: Response): Promise<any> => {
  try {
    // Validate query parameters with Zod
    const result = QuerySchema.safeParse(req.query);
    if (!result.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: result.error.errors.map((e) => ({
          path: e.path.join('.'),
          message: e.message,
        })),
      });
    }

    const { page, limit, sortField, sortOrder, fields } = result.data;

    // Extract filter params including agentName
    const filterParams = Object.entries(req.query).reduce(
      (acc, [key, value]) => {
        if (ALLOWED_FIELDS.includes(key) || key === 'agentName') {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, any>
    );

    // Validate sort field
    if (sortField && !ALLOWED_FIELDS.includes(sortField)) {
      return res.status(400).json({
        error: 'Invalid sort field',
        details: `Allowed fields: ${ALLOWED_FIELDS.join(', ')}`,
      });
    }

    // Validate field selection
    if (fields) {
      const fieldList = fields.split(',');
      if (fieldList.some((f) => !ALLOWED_FIELDS.includes(f))) {
        return res.status(400).json({
          error: 'Invalid fields requested',
          details: `Allowed fields: ${ALLOWED_FIELDS.join(', ')}`,
        });
      }
    }

    // Build query
    const rawQuery = parseFilters(filterParams);
    const query = sanitizeQuery(rawQuery);

    // Pagination
    const skip = (page - 1) * limit;

    // Aggregation pipeline to join with UserMongo
    const pipeline = [];

    // Join with User collection to get agent name
    pipeline.push({
      $lookup: {
        from: 'users',
        localField: 'agentId',
        foreignField: '_id',
        as: 'agentInfo',
      },
    });

    // Extract agent name
    pipeline.push({
      $addFields: {
        agentName: { $arrayElemAt: ['$agentInfo.name', 0] },
      },
    });

    // Remove temporary field
    pipeline.push({ $project: { agentInfo: 0 } });

    // Match main filter (now agentName is available)
    if (Object.keys(query).length > 0) {
      pipeline.push({ $match: query });
    }

    // Count total matching documents
    const countPipeline = [...pipeline, { $count: 'total' }];
    const countResult = await AdmissionRequestMongo.aggregate(countPipeline).exec();
    const total = countResult.length > 0 ? countResult[0].total : 0;
    const totalPages = Math.ceil(total / limit);

    // Sorting logic
    if (sortField) {
      if (sortField === 'agentName') {
        pipeline.push({
          $sort: {
            agentName: (sortOrder === 'desc' ? -1 : 1) as 1 | -1,
          },
        });
      } else {
        pipeline.push({
          $sort: {
            [sortField]: (sortOrder === 'desc' ? -1 : 1) as 1 | -1,
          },
        });
      }
    }

    // Pagination
    pipeline.push({ $skip: skip });
    pipeline.push({ $limit: limit });

    // Final projection for field selection
    if (fields) {
      const selectedFields = fields.split(',').reduce(
        (acc, field) => {
          acc[field] = 1;
          return acc;
        },
        {} as Record<string, number>
      );
      pipeline.push({ $project: selectedFields });
    }

    // Execute aggregation
    const paginatedData = await AdmissionRequestMongo.aggregate(pipeline).exec();

    // Transform data before sending response
    const simplifiedData = paginatedData.map((doc) => ({
      _id: doc._id,
      status: doc.status,
      documentsAnalysis: {
        status: doc.documentsAnalysis?.status || null,
      },
      personalData: {
        firstName: doc.personalData?.firstName || null,
        lastName: doc.personalData?.lastName || null,
        phone: doc.personalData?.phone || null,
        email: doc.personalData?.email || null,
        city: doc.personalData?.city || null,
        state: doc.personalData?.state || null,
        age: doc.personalData?.age || null,
      },
      typeOfPreapproval: doc.typeOfPreapproval,
      agentId: doc.agentId,
      agentName: doc.agentName || null,
      createdAt: doc.createdAt,
    }));

    return res.status(200).json({
      data: simplifiedData,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error: any) {
    logger.error('Error fetching admission requests:', error);
    if (error instanceof MongooseError.CastError) {
      return res.status(400).json({
        error: 'Invalid ID format',
        details: error.message,
      });
    }
    if (error instanceof ZodError) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.errors.map((e) => ({
          path: e.path.join('.'),
          message: e.message,
        })),
      });
    }
    if (error.message.includes('Invalid fields requested')) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.message,
      });
    }
    if (error.message.includes('Invalid query parameter')) {
      return res.status(400).json({
        error: 'Invalid query',
        details: error.message,
      });
    }
    return res.status(500).json({
      error: 'Internal Server Error',
      details: error.message,
    });
  }
};
