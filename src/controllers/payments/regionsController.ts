import RegionsPayments from '../../models/regionPaymentsSchema';
import { AsyncController } from '../../types&interfaces/types';

export const addRegion: AsyncController = async (req, res) => {
  const { region, reactivationFeeID } = req.body;
  try {
    const newRegionPayments = new RegionsPayments({
      region,
      reactivationFee: reactivationFeeID,
    });

    await newRegionPayments.save();
    return res.status(200).send({ newRegionPayments });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ error });
  }
};

export const addModelRegions: AsyncController = async (req, res) => {
  const { regionCode } = req.params;
  const { rentID, assistanceID, model } = req.body;
  try {
    const region = await RegionsPayments.findOne({ region: regionCode });
    if (!region) {
      return res.status(404).send({ message: 'Region not found' });
    }

    region.models[model] = {
      rentID,
      assistanceID,
    };

    region.markModified('models');
    await region.save();

    return res.status(200).send({ model: region.models });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ error });
  }
};
