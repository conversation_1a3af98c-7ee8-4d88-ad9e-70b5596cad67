import axios from 'axios';
import { RASAYEL_TOKEN } from '../../constants';
import { AsyncController } from '../../types&interfaces/types';

export const gigstackWebhookCdmx: AsyncController = async (req, res) => {
  const { data } = req.body;
  const requestBody = {
    query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
    operationName: 'TemplateProactiveMessageCreate',
    variables: {
      input: {
        messageTemplateId: 30664,
        // channelId: 2094,
        channelId: 2026,
        components: [
          {
            parameters: [
              { text: data.client.name, type: 'TEXT' },
              { text: data.shortURL, type: 'TEXT' },
            ],
            type: 'BODY',
          },
        ],
        receiver: data.client.phone,
      },
    },
  };
  const config = {
    headers: {
      Authorization: `Basic ${RASAYEL_TOKEN}`,
    },
  };
  try {
    const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
    return res.status(200).send(rasayel.data);
  } catch (error) {
    return res.status(400);
  }
};

export const gigstackWebhookGdl: AsyncController = async (req, res) => {
  const { data } = req.body;
  const requestBody = {
    query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
    operationName: 'TemplateProactiveMessageCreate',
    variables: {
      input: {
        messageTemplateId: 30665,
        channelId: 2026,
        components: [
          {
            parameters: [
              { text: data.client.name, type: 'TEXT' },
              { text: data.shortURL, type: 'TEXT' },
            ],
            type: 'BODY',
          },
        ],
        receiver: data.client.phone,
      },
    },
  };
  const config = {
    headers: {
      Authorization: `Basic ${RASAYEL_TOKEN}`,
    },
  };
  try {
    const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
    return res.status(200).send(rasayel.data);
  } catch (error) {
    return res.status(400);
  }
};

export const gigstackWebhookMty: AsyncController = async (req, res) => {
  const { data } = req.body;
  const requestBody = {
    query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
    operationName: 'TemplateProactiveMessageCreate',
    variables: {
      input: {
        messageTemplateId: 30666,
        channelId: 2027,
        components: [
          {
            parameters: [
              { text: data.client.name, type: 'TEXT' },
              { text: data.shortURL, type: 'TEXT' },
            ],
            type: 'BODY',
          },
        ],
        receiver: data.client.phone,
      },
    },
  };
  const config = {
    headers: {
      Authorization: `Basic ${RASAYEL_TOKEN}`,
    },
  };
  try {
    const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
    return res.status(200).send(rasayel.data);
  } catch (error) {
    return res.status(400);
  }
};

export const gigstackWebhookMoka: AsyncController = async (req, res) => {
  const { data } = req.body;
  const requestBody = {
    query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
    operationName: 'TemplateProactiveMessageCreate',
    variables: {
      input: {
        messageTemplateId: 40041,
        channelId: 4915,
        components: [
          {
            parameters: [
              { text: data.client.name, type: 'TEXT' },
              { text: data.shortURL, type: 'TEXT' },
            ],
            type: 'BODY',
          },
        ],
        receiver: data.client.phone,
      },
    },
  };
  const config = {
    headers: {
      Authorization: `Basic ${RASAYEL_TOKEN}`,
    },
  };
  try {
    const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
    return res.status(200).send(rasayel.data);
  } catch (error) {
    return res.status(400);
  }
};
