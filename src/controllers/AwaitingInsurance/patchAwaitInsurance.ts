// import { genericMessages, stockVehiclesText } from '../../constants';
// import StockVehicle from '../../models/StockVehicleSchema';
// import { AsyncController } from '../../types&interfaces/types';

// export const sendAwaitingInsuranceVehicleId: AsyncController = async (req, res) => {
//   const { id } = req.params;
//   const { dateIn, comments } = req.body;

//   try {
//     const vehicle = await StockVehicle.findById(id);

//     if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

//     vehicle.status = 'awaiting-insurance';

//     return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated, vehicle });
//   } catch (error) {
//     return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
//   }
// };
