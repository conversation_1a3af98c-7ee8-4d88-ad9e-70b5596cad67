import type { AsyncController } from '../types&interfaces/types';
import axios from 'axios';
import { createWire4BankAccount, createWire4OldBankAccount } from '../services/createWire4BankAccount';
import { getWire4SpeiIncoming, oldGetWire4SpeiIncoming } from '../services/getWire4SpeiIncoming';
import { STP_CONSTANTS, wire4Data, genericMessages } from '../constants';
import Wire4Payment from '../models/wire4PaymentsSchema';
import Associate from '../models/associateSchema';
import AssociatePayments from '../models/associatePayments';
import StockVehicle from '../models/StockVehicleSchema';
import { updateWalletMonexGig } from '../services/updateWalletMonexGig';
import Wire4Webhook from '../models/wire4WebhookSchema';
import Wire4EasyAccounts from '../models/wire4easyAccounts';
import GigPayments from '../models/gigPaymentsSchema';
import GigPaymentsTransaction from '../models/gigPaymentsTransactionsSchema';
import getGigPaymentById from '../services/getGigPaymentById';
import { ValidRegion, tokenAssignGigstack } from '../services/tokenAssignGigstack';
import { getWire4AllAccounts } from '../services/getWire4AllAccounts';
import { updateAccountProject } from '../services/updateAccountPaymentProject';
import * as fs from 'fs';
import { promisify } from 'util';
import moment from 'moment';
import { PAYMENTS_API_URL, PAYMENTS_API_KEY } from '../constants/payments-api';
import MainContractSchema from '@/models/mainContractSchema';
import SoldPayment from '@/models/soldPayments';
import { logger } from '@/clean/lib/logger';

export const webhook: AsyncController = async (req, res) => {
  const webhookData = new Wire4Webhook({ body: req.body });
  await webhookData.save();
  const { object, data } = req.body;
  if (!object || !data || !data.depositant_email) return res.status(200).json({});
  try {
    if (object === wire4Data.transfers.response.spei.incomming) {
      await Wire4Payment.create(req.body);
      if (data.depositant_clabe) {
        const gigPayments = await GigPayments.find({
          'body.client.metadata.clabe': data.depositant_clabe,
          isPaid: false,
        }).sort({ createdAt: -1 });
        const overpayingIsAlowed = gigPayments.length === 1;
        for (const gigPayment of gigPayments) {
          console.log(gigPayment.body.total, data.amount, gigPayment.body.client.company);
          const permitCents = 0.5;
          const pertmitOverpaying = 20;
          // if only have one pending payment and the amount is greater than the payment
          const overPaymentAndOnePendingPayment = gigPayment.body.total <= data.amount && overpayingIsAlowed;
          // if the amount is greater than the payment
          const overPayment =
            gigPayment.body.total <= data.amount && gigPayment.body.total + pertmitOverpaying >= data.amount;
          // if the amount is less than the payment
          const underPayment = gigPayment.body.total - permitCents <= data.amount;

          if (overPaymentAndOnePendingPayment || overPayment || underPayment) {
            const region = gigPayment.region?.toString() || '';
            const gigToken = tokenAssignGigstack(region as ValidRegion);
            const config = {
              headers: {
                Authorization: `Bearer ${gigToken}`,
                'Content-Type': 'application/json',
              },
            };
            const gigBody = {
              id: gigPayment.body.id,
              paymentForm: '03',
            };
            try {
              const gigApiResponse = await axios.put(
                'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/markaspaid',
                gigBody,
                config
              );
              await GigPayments.findOneAndUpdate(
                {
                  _id: gigPayment._id,
                },
                {
                  isPaid: true,
                },
                {
                  new: true,
                }
              );
              await GigPaymentsTransaction.create({
                monexData: req.body,
                gigstackData: gigPayment,
                gigstackApiResponse: gigApiResponse.data,
              });
              break;
            } catch (error: any) {
              if (error.response.data.message === 'El pago ya está pagado') {
                await GigPayments.findOneAndUpdate(
                  {
                    _id: gigPayment._id,
                  },
                  {
                    isPaid: true,
                  },
                  {
                    new: true,
                  }
                );
              }
              await GigPaymentsTransaction.create({
                monexData: req.body,
                gigstackData: gigPayment,
                gigstackApiResponse: error.response.data,
              });
              console.error(error);
            }
          } else {
            await GigPaymentsTransaction.create({
              monexData: req.body,
              gigstackData: gigPayment,
              gigstackApiResponse: {
                error: 'El monto del pago no coincide con el monto del pago de Gigstack',
              },
            });
          }
        }
      }
      updateWalletMonexGig(data);
      return res.status(200).json({});
    }
    return res.status(200);
  } catch (error) {
    console.error(error);
    return res.status(200).json({});
  }
};

export const createBankAccount: AsyncController = async (req, res) => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { email } = req.body;
  if (!email) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  const associatedPaymentData = await AssociatePayments.findOne({ associateEmail: email });
  if (!associatedPaymentData) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
  if (associatedPaymentData.monexClabe !== null) {
    console.error({ associatedPaymentData });
    return res.status(409).json({
      error: `${genericMessages.errors.users.driverAccountFound} ${associatedPaymentData.monexClabe}`,
    });
  }
  const associatedData = await Associate.findOne({ email });
  if (!associatedData) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
  const { firstName, lastName, vehiclesId } = associatedData;
  const carInfo = await StockVehicle.findById(
    vehiclesId.length > 0 ? vehiclesId[0] : [vehiclesId.length - 1]
  );
  if (carInfo === null) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
  const alias = carInfo.extensionCarNumber
    ? `${carInfo.carNumber}-${carInfo.extensionCarNumber}`
    : carInfo.carNumber;

  const userData = JSON.stringify({
    alias,
    currency_code: wire4Data.bankAccount.currency_code,
    email: [associatedData.email],
    name: `${firstName} ${lastName}`,
  });

  try {
    const response = await createWire4BankAccount(userData);
    if (response.error) {
      console.error(response.error);
      return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
    }
    await AssociatePayments.findOneAndUpdate(
      {
        associateEmail: email,
      },
      {
        monexClabe: response.clabe,
      },
      {
        new: true,
      }
    );

    return res.status(200).json(response);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const createEasyAccount: AsyncController = async (req, res) => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { email, nombre, contrato } = req.body;
  if (!email || !nombre || !contrato)
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });

  const userData = JSON.stringify({
    alias: contrato,
    currency_code: wire4Data.bankAccount.currency_code,
    email: [email],
    name: nombre,
  });

  try {
    const response = await createWire4BankAccount(userData);
    if (response.error) {
      console.error(response.error);
      return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
    }
    await AssociatePayments.findOneAndUpdate(
      {
        associateEmail: email,
      },
      {
        monexClabe: response.clabe,
      },
      {
        new: true,
      }
    );

    const wire4Account = {
      associateEmail: email,
      clabe: response.clabe,
      contract: contrato,
    };

    await Wire4EasyAccounts.create(wire4Account);

    return res.status(200).json(response);
  } catch (error: any) {
    console.error('ERROR', error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};
export const soldPayment: AsyncController = async (req, res) => {
  const contract = req.params.contract;
  if (!contract) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });

  const contractData = await MainContractSchema.findOne({ contractNumber: contract });
  if (!contractData || !contractData.associatedId || !contractData.totalPrice) {
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
  const { associatedId, finalPrice } = contractData;

  const associateData = await Associate.findById(associatedId);
  if (!associateData) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });

  const { email, firstName, lastName, clientId } = associateData;

  const userData = JSON.stringify({
    alias: contract,
    currency_code: wire4Data.bankAccount.currency_code,
    email: [email.replace('@', '+sold@')],
    name: `${firstName} ${lastName}`,
  });

  try {
    const response = await createWire4OldBankAccount(userData);
    if (response.error) {
      console.error(response.error);
      logger.info(`[soldPayment] Error: ${response.error}`);
      return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
    }
    await AssociatePayments.findOneAndUpdate(
      {
        associateId: associateData._id,
      },
      {
        soldClabe: response.clabe,
      },
      {
        new: true,
      }
    );

    const wire4Account = {
      associateEmail: email,
      clabe: response.clabe,
      contract,
    };

    const soldClabe = await Wire4EasyAccounts.create(wire4Account);

    try {
      const createPayment = await axios.post(
        `${PAYMENTS_API_URL}/payments/sold-payment`,
        {
          clientId,
          clabe: soldClabe.clabe,
          total: finalPrice,
        },
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );

      await SoldPayment.create({ paymentDetail: createPayment.data });
      return res.status(200).json({
        message: 'Clabe creada y pago registrado',
        payment: createPayment.data,
      });
    } catch (error: any) {
      console.error('ERROR', error);
      logger.info(`[soldPayment] Error: ${error}`);
      return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
    }
  } catch (error: any) {
    console.error('ERROR', error);
    logger.info(`[soldPayment] Error: ${error}`);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const updateSoldPayment: AsyncController = async (req, res) => {
  const { id } = req.params;
  if (!id) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  const payment = await SoldPayment.findById(id);
  if (!payment) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  await SoldPayment.findByIdAndUpdate(id, req.body);
  return res.status(200).json({ message: 'Pago actualizado' });
};

export const getBankAccounts: AsyncController = async (req, res) => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { email } = req.params;
  if (!email) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  const associatedPaymentData = await AssociatePayments.findOne({ associateEmail: email });
  if (!associatedPaymentData) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
  if (associatedPaymentData.monexClabe === null) {
    return res.status(409).json({
      error: `${genericMessages.errors.users.driverAccountNotFound}`,
    });
  }
  return res.status(200).json({ clabe: associatedPaymentData.monexClabe });
};

export const getSpeiIncoming: AsyncController = async (req, res) => {
  const { beginDate, endDate } = req.params;
  if (!beginDate || !endDate) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  try {
    const response = await getWire4SpeiIncoming(beginDate, endDate);
    if (response.error) {
      console.error(response.error);
      return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
    }
    return res.status(200).json(response);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const oldGetSpeiIncoming: AsyncController = async (req, res) => {
  const { beginDate, endDate } = req.params;
  if (!beginDate || !endDate) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  try {
    const response = await oldGetWire4SpeiIncoming(beginDate, endDate);
    if (response.error) {
      console.error(response.error);
      return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
    }
    return res.status(200).json(response);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const getPayments: AsyncController = async (req, res) => {
  const { contrato } = req.params;
  if (!contrato) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  try {
    const GigPaymentsData = await GigPayments.find({ 'body.client.company': contrato });
    if (!GigPaymentsData) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    const numberOfPayments = GigPaymentsData.length;
    if (numberOfPayments === 0) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    const paymentData = await getGigPaymentById(
      GigPaymentsData[numberOfPayments - 1].body.id,
      GigPaymentsData[numberOfPayments - 1].region?.toString() || ''
    );
    const monexData = await Wire4Webhook.find({
      'body.data.depositant_clabe': paymentData.client.metadata.clabe,
    });
    const numbersOfMonexPayments = monexData.length;
    if (numbersOfMonexPayments === 0)
      return res.status(500).json({ error: genericMessages.errors.payments.monexPaymentNotFound });

    const response = {
      'Último pago': {
        Monex: {
          'Fecha de pago': monexData[numbersOfMonexPayments - 1].body.created,
          Monto: monexData[numbersOfMonexPayments - 1].body.data.amount,
          'ID del pago': monexData[numbersOfMonexPayments - 1].body.id,
          Status: monexData[numbersOfMonexPayments - 1].body.status,
          Driver: {
            Nombre: monexData[numbersOfMonexPayments - 1].body.data.depositant,
            CLABE: monexData[numbersOfMonexPayments - 1].body.data.depositant_clabe,
            Email: monexData[numbersOfMonexPayments - 1].body.data.depositant_email,
          },
          'Datos del pago': {
            'Monex description': monexData[numbersOfMonexPayments - 1].body.data.monex_description,
            'Monex transaction_id': monexData[numbersOfMonexPayments - 1].body.data.monex_transaction_id,
            'Clave de rastreo': monexData[numbersOfMonexPayments - 1].body.data.calve_rastreo,
          },
        },
        Gigstack: {
          'Fecha de creación del link pago': GigPaymentsData[numberOfPayments - 1].createdAt,
          Monto: GigPaymentsData[numberOfPayments - 1].body.total,
          'ID del pago': GigPaymentsData[numberOfPayments - 1].body.id,
          Status: paymentData.status,
          Driver: {
            Nombre: paymentData.client.name,
            CLABE: paymentData.client.metadata?.clabe,
            Email: paymentData.client.email,
            Phone: paymentData.client.phone,
          },
        },
      },
      Pagos: {
        MONEX: {
          'Links de pagos en la bd': numbersOfMonexPayments,
          'Detalle de los links de pagos': monexData,
        },
        Gigstack: {
          'Links de pagos en la bd': numberOfPayments,
          'Detalle de los links de pagos': GigPaymentsData,
        },
      },
    };
    return res.status(200).json(response);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const getTheLast5Payments: AsyncController = async (req, res) => {
  const { contrato } = req.params;
  if (!contrato) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  try {
    const GigPaymentsData = await GigPayments.find({ 'body.client.company': contrato }).sort({
      createdAt: -1,
    });
    if (!GigPaymentsData) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    const numberOfPayments = GigPaymentsData.length;
    if (numberOfPayments === 0) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    const paymentsData = GigPaymentsData.slice(0, 5);
    const APIResponse = {
      succeeded: 'Pagado',
      requires_payment_method: 'Pendiente',
      canceled: 'Cancelado',
    };
    const response = [];
    for (const payment of paymentsData) {
      try {
        const paymentData = await getGigPaymentById(payment.body.id, payment.region?.toString() || '');
        response.push({
          creationDate: moment(payment.createdAt).format('DD/MM/YYYY'),
          total: `$ ${payment.body.total}`,
          payDate: paymentData.succeededTimestamp
            ? moment(paymentData.succeededTimestamp).format('DD/MM/YYYY')
            : '-',
          status: APIResponse[paymentData.status as keyof typeof APIResponse] as string,
          link: paymentData.shortURL,
          driver: {
            nombre: paymentData.client.name,
            clabe: paymentData.client.metadata?.clabe,
            email: paymentData.client.email,
            phone: paymentData.client.phone,
          },
        });
      } catch (error) {
        console.error(error);
      }
      // response.push(paymentData);
    }
    return res.status(200).json(response);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const findPaymentByReference: AsyncController = async (req, res) => {
  const { reference } = req.params;
  if (!reference) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  try {
    const monexData = await Wire4Webhook.find({
      'body.data.reference': reference,
    });
    if (monexData.length === 0) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    return res.status(200).json(monexData);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const findPaymentByDescription: AsyncController = async (req, res) => {
  const { description } = req.params;
  if (!description) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  try {
    const monexData = await Wire4Webhook.find({
      'body.data.description': description,
    });
    if (monexData.length === 0) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    return res.status(200).json(monexData);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const findPaymentByClaveRastreo: AsyncController = async (req, res) => {
  const { claveRastreo } = req.params;
  console.log(claveRastreo);
  if (!claveRastreo) return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  try {
    const monexData = await Wire4Webhook.find({
      'body.data.clave_rastreo': claveRastreo,
    });
    if (monexData.length === 0) return res.status(500).json({ error: STP_CONSTANTS.errors.conciliation });
    return res.status(200).json(monexData);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const getAllMonexAccounts: AsyncController = async (req, res) => {
  const { suscription } = req.params;
  const validSuscription = ['i80', 'onecarnow'];

  if (!validSuscription.includes(suscription)) return res.status(400).json({ error: 'Invalid suscription' });

  const checkSuscription = suscription === 'i80' ? 'i80' : 'onecarnow';
  const getData = await getWire4AllAccounts(checkSuscription);
  return res.status(200).json(getData);
};

export const updateClabeByCsv: AsyncController = async (req, res) => {
  const csv: Express.Multer.File | undefined = req.file;

  // Validación inicial del CSV
  if (!csv) {
    return res.status(400).json({ error: STP_CONSTANTS.errors.missingBody });
  }
  const readFileAsync = promisify(fs.readFile);
  try {
    // Procesamiento del CSV
    const csvContent = await readFileAsync(csv.path, 'utf-8');

    // Procesar el CSV
    const csvData = csvContent
      .split('\n')
      .slice(1)
      .map((line: string) => line.trim().split(','));

    // Iterar sobre cada línea del CSV
    for (const lineData of csvData) {
      const [contract, subContract, fondo] = lineData;
      console.log(contract, subContract, fondo);

      //Validación de campos obligatorios
      if (!contract || !fondo || !subContract) {
        return res.status(400).json({ error: STP_CONSTANTS.errors.missingBody });
      }

      await updateAccountProject(contract, Number(subContract), fondo);
    }

    return res.status(200).json({ message: 'Clabes actualizadas' });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const getAllPayments: AsyncController = async (req, res) => {
  try {
    const thirtyDaysAgo = moment().subtract(15, 'days').toDate();

    const monexData = await Wire4Webhook.aggregate(
      [
        { $match: { createdAt: { $gte: thirtyDaysAgo } } },
        { $sort: { createdAt: -1 } },
        {
          $project: {
            'body.data.reference': 1,
            'body.data.amount': 1,
            'body.data.depositant': 1,
            'body.data.depositant_clabe': 1,
            'body.data.depositant_email': 1,
            'body.data.alias': 1,
            'body.data.rfc': 1,
            'body.data.description': 1,
            'body.data.clave_rastreo': 1,
            'body.data.monex_description': 1,
            'body.data.sender_bank': 1,
            'body.data.sender_name': 1,
            'body.data.sender_account': 1,
            'body.data.sender_rfc': 1,
          },
        },
      ],
      { allowDiskUse: true }
    );
    return res.status(200).json(monexData);
  } catch (error) {
    console.error('Error in getAllPayments:', error);
    return res.status(500).json({ error: STP_CONSTANTS.errors.missingBody });
  }
};

export const sendWhatsApp: AsyncController = async (req, res) => {
  const contracts = [
    '1117',
    // '2001',
    // '3219',
    // '15022',
    // '3264',
    // '1468',
    // '1486',
    // '3285',
    // '12014',
    // '1478',
    // '1516',
    // '4046',
    // '5265',
    // '5266',
    // '5280',
    // '12011',
    // '18008',
    // '1503',
    // '3368',
    // '3369',
    // '5257',
    // '5275',
    // '5281',
    // '5291',
    // '5292',
    // '5293',
    // '5294',
    // '5295',
    // '5296',
    // '5297',
    // '15036',
    // '15037',
    // '15039',
    // '1467',
    // '1489',
    // '1513',
    // '3362',
    // '3363',
    // '3364',
    // '3365',
    // '3366',
    // '3370',
    // '5285',
    // '5298',
    // '15040',
    // '15046',
    // '15047',
    // '16020',
    // '19012',
    // '2066',
    // '1496',
    // '3299',
    // '20003',
  ];

  contracts.forEach(async (contract) => {
    let number = '';
    let name = '';
    let associateId = '';
    try {
      const getStockVehicle = await StockVehicle.findOne({ carNumber: contract });
      if (!getStockVehicle) return;
      const vehiclesId = getStockVehicle._id;
      await Associate.findOne({ vehiclesId }).then(async (associate) => {
        if (!associate) return;
        number = associate.phone;
        name = associate.firstName;
        associateId = associate.clientId;

        // console.log({ number, name, associateId });
      });
    } catch (error) {
      console.error(error);
    }
    try {
      const { data } = await axios.get(
        `${PAYMENTS_API_URL}/payments?status=pending&clientId=${associateId}`,
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );
      if (data?.data[0]?.status === 'pending') {
        await axios.post(
          'https://api.hilos.io/api/channels/whatsapp/template/2ce47da8-ddfa-425c-9b70-db9645ac8f53/send',
          {
            phone: number,
            variables: [name, data.data[0].id],
          },
          {
            headers: {
              Authorization: `Token ${process.env.HILOS_API_KEY}`,
              'Content-Type': 'application/json',
            },
          }
        );
      }
    } catch (error) {
      console.error(error);
    }
  });
  return res.status(200).json({ message: 'Enviando mensajes' });
};
