import type { AsyncController } from '../types&interfaces/types';
import { AdmissionRequestMongo } from '../models/admissionRequestSchema';
import { dealUpdate } from '../services/hubspot';
import { logger } from '../clean/lib/logger';
import { DRIVER_WEB_APP_URL } from '@/constants';
import { sendEmailToMXCustomerAfterRegistering } from '@/modules/platform_connections/emailFunc';
import HubspotBatch from '@/models/hubspotBatch';

enum ApprovalTypes {
  PREAPPROVED = 'preapproved',
  PRE_OWNED = 'pre-owned',
  MG3 = 'MG3',
  DOWN_PAYMENT = 'down payment',
  DEPOSIT = 'deposit',
}

const approvalHubspotMapping: { [key: string]: string } = {
  [ApprovalTypes.PREAPPROVED]: 'Nuevo',
  [ApprovalTypes.PRE_OWNED]: 'Semi-Nuevo',
  [ApprovalTypes.DEPOSIT]: 'Nuevo con Depósito',
  [ApprovalTypes.DOWN_PAYMENT]: 'Nuevo con Enganche',
  [ApprovalTypes.MG3]: 'Pre-Aprobado MG3',
};

export const addPreapproval: AsyncController = async (req, res) => {
  const { requestId, preapproval, reason, suggestedPreapproval } = req.body;
  logger.info(`[addPreapproval] Adding preapproval ${preapproval} to request ${requestId}`);
  if (!requestId || !preapproval) {
    logger.error(`[addPreapproval] Missing body`);
    return res.status(400).send({ message: 'Missing body' });
  }
  try {
    const admissionRequest = await AdmissionRequestMongo.findById(requestId);
    if (!admissionRequest) {
      logger.error(`[addPreapproval] Admission request not found`);
      return res.status(404).send({ message: 'Admission request not found' });
    }
    admissionRequest.typeOfPreapproval = preapproval;
    if (suggestedPreapproval) {
      admissionRequest.suggestedTypeOfPreapproval = suggestedPreapproval;
    }
    if (reason) {
      admissionRequest.preApprovalReason = reason;
    }
    await admissionRequest.save();

    const hubspotBtach = await HubspotBatch.findOne({
      requestId: admissionRequest.id,
    });
    if (hubspotBtach?.dealId !== undefined) {
      const properties: any = {};

      const preapprovalValue = approvalHubspotMapping[preapproval];
      if (preapprovalValue) properties.condiciones_de_aprobacion = preapprovalValue;

      if (!preapprovalValue) properties.condiciones_de_aprobacion = 'qualifiedtobuy';

      await dealUpdate({
        dealId: hubspotBtach.dealId,
        properties,
      });
    }

    return res.status(201).send({ message: 'Preapproval added' });
  } catch (error) {
    logger.error(`[addPreapproval] Error`, error);
    return res.status(500).send({ message: 'Error', error });
  }
};

export const updateRequest: AsyncController = async (req, res) => {
  const { requestId, properties } = req.body;
  logger.info(`[updateRequest] Updating request ${requestId} with properties`, properties);
  if (!requestId || !properties) {
    logger.error(`[updateRequest] Missing body`);
    return res.status(400).send({ message: 'Missing body' });
  }
  try {
    const admissionRequest = await AdmissionRequestMongo.findById(requestId);
    if (!admissionRequest) {
      logger.error(`[updateRequest] Admission request not found`);
      return res.status(404).send({ message: 'Admission request not found' });
    }
    await AdmissionRequestMongo.findByIdAndUpdate(requestId, properties);
    if (properties['personalData.email'] && properties['personalData.firstName']) {
      await sendEmailToMXCustomerAfterRegistering({
        customerEmail: properties['personalData.email'],
        customerName: `${properties['personalData.firstName']}`,
        customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${requestId}`,
      });
    }
    return res.status(200).send({ message: 'Request updated' });
  } catch (error) {
    logger.error(`[updateRequest] Error`, error);
    return res.status(500).send({ message: 'Error', error });
  }
};

export const getRequestByPhone: AsyncController = async (req, res) => {
  const { phone } = req.params;
  logger.info(`[getRequestByPhone] Getting request by phone ${phone}`);
  if (!phone) {
    logger.error(`[getRequestByPhone] Missing body`);
    return res.status(400).send({ message: 'Missing body' });
  }
  try {
    const admissionRequest = await AdmissionRequestMongo.findOne({ 'personalData.phone': phone });
    if (!admissionRequest) {
      logger.error(`[getRequestByPhone] Admission request not found`);
      return res.status(404).send({ message: 'Admission request not found' });
    }
    return res.status(200).send({ admissionRequest });
  } catch (error) {
    logger.error(`[getRequestByPhone] Error`, error);
    return res.status(500).send({ message: 'Error', error });
  }
};
