import axios from 'axios';
import { uploadFile } from '../../aws/s3';
import { associateText, genericMessages, stockVehiclesText } from '../../constants';
import StockVehicle, {
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleCategoryType,
  VehicleStatusType,
  VehicleSubCategory,
  VehicleSubCategoryType,
} from '../../models/StockVehicleSchema';
import AssociatePayments from '../../models/associatePayments';
import Associate from '../../models/associateSchema';
import Document from '../../models/documentSchema';
import OverHauling from '../../models/overHaulingSchema';
import TempSuscriptionPayments from '../../modules/TempSuscriptionPayments/model/tempSuscriptionPayment.model';
import { removeEmptySpacesNameFile } from '../../services/removeEmptySpaces';
// import { ValidRegion, gigstackRequests } from '../../services/tokenAssignGigstack';
import { AsyncController } from '../../types&interfaces/types';
import { CustomChangeStatusError, Status, changeStatusFunctions, statusObj } from './cases';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../constants/payments-api';
import { logger } from '../../clean/lib/logger';

export const availableChangeStatus = ['in-service', 'awaiting-insurance', 'legal-process'];

export const availableChangeCategory = [
  'workshop',
  'insurance',
  'legal',
  'delivered',
  'sold',
  'utilitary',
  'adendum',
];

export const changeStockStatus: AsyncController = async (req, res) => {
  const { vehicleId: id } = req.params;
  const {
    status,
    stopPayments = true,
    category,
    subCategory,
  }: { status: Status; stopPayments: boolean; category: Status; subCategory: string } = req.body;

  if (!status) return res.status(400).send({ message: 'Status es requerido' });
  console.log('INCOMING REQUEST -------------------------------------------');
  try {
    const stockVehicle = await StockVehicle.findById(id);
    if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    const lastDriver = await Associate.findById(stockVehicle.drivers[stockVehicle.drivers.length - 1]);
    if (!lastDriver) return res.status(404).send({ message: associateText.errors.associateNotFound });

    stockVehicle.canFinishProcess = false;

    await changeStatusFunctions[category].create({ req, res }, stockVehicle, lastDriver._id);

    if (stopPayments) {
      // Se detienen los pagos de la suscripción en el else
      const associatePayment = await AssociatePayments.findOne({ associateId: lastDriver._id });
      if (associatePayment) {
        const associate = await Associate.findById(lastDriver._id);

        if (associate && associate.clientId) {
          await axios.patch(
            `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
            {
              status: false,
            },
            {
              headers: {
                Authorization: `Bearer ${PAYMENTS_API_KEY}`,
              },
            }
          );
        }
      }
    }

    stockVehicle.updateHistory.push({
      step: `VEHICULO ENVIADO A ${statusObj[status]}`,
      userId: req.userId.userId,
      description: '',
    });
    if (availableChangeStatus.includes(status)) {
      stockVehicle.status = status as VehicleStatusType;
    }
    stockVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    stockVehicle.category = category as VehicleCategoryType;
    stockVehicle.subCategory = subCategory
      ? (subCategory as VehicleSubCategoryType)
      : VehicleSubCategory.default;
    await stockVehicle.save();
    return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated, stockVehicle });
  } catch (error: any) {
    logger.error('[changeStockStatus] error: ' + JSON.stringify(error));
    if (error instanceof CustomChangeStatusError) {
      return res.status(error.customStatus).send({ message: error.message });
    }
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};

const finishStatus: { [key: string]: string } = {
  'in-service': 'Taller',
  'awaiting-insurance': 'Espera de Seguro',
  'legal-process': 'Proceso legal',
  legal: 'Proceso legal',
  workshop: 'Taller',
  insurance: 'Seguro',
  delivered: 'Entregado',
  sold: 'Vendido',
  utilitary: 'Utilitario',
  adendum: 'Adendum',
};

export const returnToActiveAfterChangeStatus: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  console.log('[returnToActiveAfterChangeStatus] ----- BODY', req.body);
  const { cancelStatus } = req.body;
  console.log('parsed cancelStatus', JSON.parse(cancelStatus));
  try {
    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    const lastDriver = await Associate.findById(vehicle.drivers[vehicle.drivers.length - 1]);
    if (!lastDriver) return res.status(404).send({ message: associateText.errors.associateNotFound });

    const currentCategory = vehicle.category;

    if (!availableChangeCategory.includes(currentCategory))
      return res.status(400).send({
        message: `El estatus actual no corresponde con el flujo`,
      });

    if (JSON.parse(cancelStatus)) {
      await changeStatusFunctions[currentCategory as Status].cancel(req, vehicle, lastDriver);
      await TempSuscriptionPayments.findOneAndUpdate(
        { associateId: lastDriver._id, stockId: vehicle._id },
        { status: 'inactive' }
      );

      await axios.patch(
        `${PAYMENTS_API_URL}/subscriptions/status/${lastDriver.clientId}`,
        { status: true },
        { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
      );

      vehicle.updateHistory.push({
        step: `CANCELACIÓN DE ESTATUS, VEHICULO REGRESADO A ACTIVO`,
        userId: req.userId.userId,
        description: `Vehiculo regresado a activo despues de estar en ${finishStatus[currentCategory] || ''}`,
        filter: 'status',
      });
      vehicle.canFinishProcess = false;
      vehicle.status = 'active';
      vehicle.vehicleStatus = UpdatedVehicleStatus.active;
      vehicle.category = VehicleCategory.default;
      vehicle.subCategory = VehicleSubCategory.default;
      lastDriver.active = true;
      await vehicle.save();
      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated, vehicle });
    }
    await changeStatusFunctions[currentCategory as Status].finish(req, vehicle, lastDriver);

    // const associatePayment = await AssociatePayments.findOne({ associateId: lastDriver._id });

    // if (associatePayment) {
    // await gigstackRequests({
    //   endpoint: '/recurring2/status',
    //   region: associatePayment.region as ValidRegion,
    //   method: 'put',
    //   values: {
    //     id: associatePayment.suscriptionId,
    //     status: 'active',
    //   },
    // });
    // const associate = await Associate.findById(lastDriver._id);
    // if (associate && associate.clientId) {
    //   await axios.patch(
    //     `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
    //     {
    //       status: true,
    //     },
    //     {
    //       headers: {
    //         Authorization: `Bearer ${PAYMENTS_API_KEY}`,
    //       },
    //     }
    //   );
    // }
    // }

    vehicle.updateHistory.push({
      step: `VEHICULO REGRESADO A ACTIVO`,
      userId: req.userId.userId,
      description: `Vehiculo regresado a activo despues de estar en ${finishStatus[currentCategory] || ''}`,
      filter: 'status',
    });

    vehicle.status = 'active';
    vehicle.vehicleStatus = UpdatedVehicleStatus.active;
    vehicle.category = VehicleCategory.default;
    vehicle.subCategory = VehicleSubCategory.default;
    lastDriver.active = true;
    await lastDriver.save();
    await vehicle.save();
    return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated, vehicle });
  } catch (error) {
    console.log('[returnToActiveAfterChangeStatus] error', error);
    if (error instanceof CustomChangeStatusError) {
      return res.status(error.customStatus).send({ message: error.message });
    }
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};

export const sendOverHaulingByVehicleId: AsyncController = async (req, res) => {
  try {
    const { vehicleId } = req.params;
    const { dateIn, comments, subCategory } = req.body;

    const files = req.files as Express.Multer.File[];

    if (!files || files.length < 1) return res.status(400).send({ message: 'Se requieren 5 imagenes' });

    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    if (vehicle.category !== VehicleCategory.stock)
      return res.status(400).send({ message: 'No se puede realizar este proceso en el estatus actual' });

    const countOverHauling = await OverHauling.countDocuments({ stockId: vehicleId });
    const overHauling = new OverHauling({
      dateIn,
      stockId: vehicleId,
    });

    if (countOverHauling === 0) {
      overHauling.sameVehicleCount = 1;
    }
    if (countOverHauling > 0) {
      overHauling.sameVehicleCount = countOverHauling + 1;
    }

    const fields: { [key: string]: any } = {
      inImgs: {
        path: 'inImgs',
      },
      'inImgs[]': {
        path: 'inImgs',
      },
      quotationDoc: {
        path: 'quotationDoc',
      },
    };

    for (const file of files) {
      const removeSpaces = removeEmptySpacesNameFile(file);

      const directory = file.fieldname === 'quotationDoc' ? 'quotationDoc' : fields[file.fieldname].path;

      const s3Res = await uploadFile(
        file,
        removeSpaces,
        `overhauling/${vehicle.carNumber}/${overHauling.sameVehicleCount}/${directory}/`
      );
      const path = s3Res.input.Key;
      const document = new Document({
        originalName: removeSpaces,
        path,
        vehicleId,
      });
      await document.save();
      if (file.fieldname === 'inImgs' || file.fieldname === 'inImgs[]') {
        overHauling.inImgs.push({ _id: document._id });
      }

      if (file.fieldname === 'quotationDoc') overHauling.quotationDoc = document._id;
    }

    if (comments) {
      overHauling.comments = comments;
    }
    vehicle.status = 'overhauling';
    vehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    vehicle.category = VehicleCategory.revision;
    vehicle.subCategory = subCategory as VehicleSubCategoryType;
    vehicle.updateHistory.push({
      step: 'VEHICULO ENVIADO A REVISIÓN',
      userId: req.userId.userId,
      description: '',
      filter: 'status',
      group: 'contract-info',
    });

    await overHauling.save();
    await vehicle.save();
    return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};

export const finishOverHaulingByVehicleId: AsyncController = async (req, res) => {
  try {
    const { vehicleId } = req.params;
    const { dateOut, comments } = req.body;

    const files = req.files as Express.Multer.File[];

    const outImgs = files.filter((file) => file.fieldname === 'outImgs[]');
    const invoice = files.filter((file) => file.fieldname === 'invoiceFile');

    if (!dateOut) return res.status(400).send({ message: 'La fecha de salida es requerida' });
    if (!files || files.length < 1) return res.status(400).send({ message: 'Se requieren 5 imagenes' });
    // if (!invoice || invoice.length < 1) return res.status(400).send({ message: 'Se requiere la factura' });

    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    if (vehicle.category !== VehicleCategory.revision)
      return res.status(400).send({ message: 'No se puede realizar este proceso en el estatus actual' });

    const overHauling = await OverHauling.findOne({ stockId: vehicleId }, null, {
      sort: { sameVehicleCount: -1 },
    });

    if (!overHauling) return res.status(404).send({ message: 'No se ha encontrado el registro de revisión' });

    overHauling.dateOut = dateOut;
    // console.log('Body: ', req.body);
    // overHauling.invoiceAmount = req.body.invoiceAmount;
    if (comments) {
      overHauling.commentsOut = comments;
    }
    for (const file of outImgs) {
      const removeSpaces = removeEmptySpacesNameFile(file);
      const s3Res = await uploadFile(
        file,
        removeSpaces,
        `overhauling/${vehicle.carNumber}/${overHauling.sameVehicleCount}/outImgs/`
      );
      const path = s3Res.input.Key;
      const document = new Document({
        originalName: removeSpaces,
        path,
        vehicleId,
      });
      await document.save();
      overHauling.outImgs.push({ _id: document._id });
    }

    if (req.body.invoiceAmount) overHauling.invoiceAmount = req.body.invoiceAmount;
    if (req.body.hasInvoice) overHauling.hasInvoice = req.body.hasInvoice;
    if (invoice && invoice.length > 1) {
      // If invoice is not empty we upload it
      const removeSpaces = removeEmptySpacesNameFile(invoice[0]);
      const s3Res = await uploadFile(
        invoice[0],
        removeSpaces,
        `overhauling/${vehicle.carNumber}/${overHauling.sameVehicleCount}/invoice/`
      );
      const path = s3Res.input.Key;
      const invoiceDoc = new Document({
        originalName: removeSpaces,
        path,
        vehicleId,
      });
      await invoiceDoc.save();
      overHauling.invoiceFile = invoiceDoc._id;
    }

    vehicle.status = 'stock';
    vehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    vehicle.category = VehicleCategory.stock;
    vehicle.subCategory = VehicleSubCategory.default;
    vehicle.updateHistory.push({
      step: 'REVISIÓN FINALIZADA',
      userId: req.userId.userId,
      description: '',
      filter: 'status',
      group: 'contract-info',
    });
    await overHauling.save();
    await vehicle.save();
    return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};
