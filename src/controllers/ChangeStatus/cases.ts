/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable max-params */
/* eslint-disable consistent-return */
import { Response } from 'express';
import ServiceStock from '../../models/serviceStockSchema';
import LegalProcessStock from '../../models/legalProcessSchema';
import StockVehicle, {
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleStatus,
} from '../../models/StockVehicleSchema';
import AwaitingInsuranceStock from '../../models/awaitingInsuranceSchema';
import { uploadFile } from '../../aws/s3';
import { removeEmptySpacesNameFile } from '../../services/removeEmptySpaces';
import Document from '../../models/documentSchema';
import Associate from '../../models/associateSchema';
import { Types } from 'mongoose';
import AssociatePayments from '../../models/associatePayments';
import { MyRequest } from '../../types&interfaces/interfaces';
import StockVehicleStatusHistory from '../../models/stockVehicleStatusHistory';

export const stock = new StockVehicle({});
const associateIns = new Associate({});

export type Status =
  | 'in-service'
  | 'awaiting-insurance'
  | 'legal-process'
  | 'workshop'
  | 'insurance'
  | 'legal'
  | 'sold'
  | 'delivered'
  | 'utilitary'
  | 'adendum';

// export const statusObj = {
//   'in-service': 'TALLER',
//   'awaiting-insurance': 'ESPERO DE SEGURO',
//   'legal-process': 'PROCESO LEGAL',
// };
// Update the statusObj if it exists
export const statusObj = {
  'in-service': 'SERVICIO',
  'awaiting-insurance': 'ESPERA DE SEGURO',
  'legal-process': 'PROCESO LEGAL',
  workshop: 'TALLER',
  insurance: 'ESPERA DE SEGURO',
  legal: 'PROCESO LEGAL',
  sold: 'VENDIDO',
  delivered: 'ENTREGADO',
  utilitary: 'UTILITARIO',
  adendum: 'ADENDUM',
};

interface StatusFunctions<T> {
  /**
   * Creates a new record in the collection based on the provided status.
   * @param {Request} req - Express Request object.
   * @param {Response} res - Express Response object.
   * @param {Object} vehicle - Vehicle object.
   * @returns {Promise<void>} A promise that resolves when the creation is complete.
   */
  create: (
    { req, res }: { req: MyRequest; res: Response },
    vehicle: T,
    associateId: Types.ObjectId
  ) => Promise<any | undefined | void>;
  /**
   * Finishes a process based on the provided status.
   * @param {Request} req - Express Request object.
   * @param {string} stockId - Vehicle stock id.
   * @param {string} carNumber - Vehicle car number.
   * @param {Object} associate - Associate object.
   */
  finish: (req: MyRequest, vehicle: typeof stock, associate: typeof associateIns) => Promise<any>;
  /**
   * Cancels a process based on the provided status.
   * @param {Request} req - Express Request object.
   * @param {string} vehicle - Vehicle instance
   * @param {Object} associate - Associate object.
   */
  cancel: (req: MyRequest, vehicle: typeof stock, associate: typeof associateIns) => Promise<any>;
}

interface CustomChangeStatusErrorProps {
  customStatus: number;
}
/**
 * Custom error class for handling errors in the change status processes.
 */

export class CustomChangeStatusError extends Error implements CustomChangeStatusErrorProps {
  public customStatus: number;

  constructor(message: string, customStatus: number) {
    super(message);
    this.name = this.constructor.name;
    this.customStatus = customStatus;
    Error.captureStackTrace(this, this.constructor);
  }

  toString() {
    return `${this.name}: ${this.message} (customStatus: ${this.customStatus})`;
  }
}

type UploadArrayOfFiles = (
  files: Express.Multer.File[],
  status: Status,
  vehicle: typeof stock,
  associate: typeof associateIns,
  createdCount: string
) => Promise<{ _id: Types.ObjectId }[]>;

const uploadArrayOfFiles: UploadArrayOfFiles = async (files, status, vehicle, associate, createdCount) => {
  const docsIdsArray: { _id: Types.ObjectId }[] = [];
  for (const file of files) {
    if (file && file.fieldname !== 'adendumDoc') {
      const nameFile = removeEmptySpacesNameFile(file);
      // const path = `associate/${carNumber}/${associate.email}/${status}-${createdCount}`;
      const path = `adendums/${vehicle.carNumber}/${associate.email}/${status}-${createdCount}/serviceImgs`;
      const res = await uploadFile(file, nameFile, path);
      const newDoc = new Document({
        originalName: nameFile,
        associateId: associate._id,
        path: res.input.Key,
      });
      await newDoc.save();
      docsIdsArray.push({ _id: newDoc._id });
    } else if (file && file.fieldname === 'adendumDoc') {
      const nameFile = removeEmptySpacesNameFile(file);
      const path = `adendums/${vehicle.carNumber}/${associate.email}/${status}-${createdCount}/adendumDoc`;
      const res = await uploadFile(file, nameFile, path);
      const newDoc = new Document({
        originalName: nameFile,
        associateId: associate._id,
        path: res.input.Key,
      });
      await newDoc.save();
      // docsIdsArray.push(newDoc._id);
      associate.adendumDocs.push({ _id: newDoc._id });
    }
  }
  return docsIdsArray;
};

type ChangeStatusObject<T> = Record<Status, StatusFunctions<T>>;

/**
 * Object containing functions for changing the status of a vehicle in different processes.
 * Each process has a 'create' and 'finish' function for handling the status change.
 */

export const changeStatusFunctions: ChangeStatusObject<typeof stock> = {
  /* IN SERVICE */
  'in-service': {
    create: async ({ req }, vehicle, associateId) => {
      const { dateIn, dateOut } = req.body;
      if (!vehicle.adendumServiceCount) vehicle.adendumServiceCount = 1;
      else vehicle.adendumServiceCount += 1;
      if (!dateIn || !dateOut) throw new CustomChangeStatusError('Campos faltantes', 400);
      const newServiceStock = new ServiceStock({
        dateIn,
        dateOut,
        comments: req.body.comments,
        stockId: vehicle._id,
        associateId,
      });

      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,
        previousCategory: vehicle.status,

        previousStep: vehicle.step.stepName,
        status: UpdatedVehicleStatus.inactive,
        category: req.body.category,
        subCategory: req.body.subCategory,
        step: vehicle.step.stepName,
        comments: req.body.comments,
        dateIn,
        isCompleted: false,
        tentativeDateOut: dateOut,
      });
      await newLog.save();
      await newServiceStock.save();

      return;
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      if ((req.files as Express.Multer.File[])?.length < 5)
        throw new CustomChangeStatusError('No se han subido los archivos requeridos', 400);
      const existsPrevious = await ServiceStock.findOne({ stockId: vehicle._id }, null, {
        sort: { createdAt: 1 },
      });
      const associatePayment = await AssociatePayments.findOne({ associateId: associate._id });
      if (associatePayment) {
        associatePayment.lengthAddedBefore = 0;
        associatePayment.adendumGenerated = false;
        await associatePayment.save();
      }
      if (!existsPrevious || vehicle.status !== VehicleStatus['in-service'])
        throw new CustomChangeStatusError('No existe un proceso anterior', 400);
      const files = req.files as Express.Multer.File[];
      if (existsPrevious) {
        const docsIdsArray = await uploadArrayOfFiles(
          files,
          VehicleStatus['in-service'],
          vehicle,
          associate,
          vehicle.adendumServiceCount.toString() || '1'
        );
        existsPrevious.images = docsIdsArray;
        existsPrevious.dateFinished = req.body.dateFinished;
        existsPrevious.isCanceled = false;

        await existsPrevious.save();
      }
      vehicle.status = UpdatedVehicleStatus.active;

      const findLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      });

      if (findLog) {
        findLog.isCompleted = true;
        await findLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: findLog.category,
          previousSubCategory: findLog.subCategory,
          previousStep: findLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });
        await newLog.save();
      }

      await associate.save();
      return;
    },
    cancel: async (req, vehicle, associate) => {
      await ServiceStock.findOneAndUpdate(
        { stockId: vehicle._id, associateId: associate._id },
        { isCanceled: true }
      );

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }
    },
  },
  /* AWAITING INSURANCE */
  'awaiting-insurance': {
    create: async ({ req }, vehicle, associateId) => {
      const { date } = req.body;
      if (!date) throw new CustomChangeStatusError('Campos faltantes', 400);
      const newAwaiting = new AwaitingInsuranceStock({
        date,
        comments: req.body.comments,
        stockId: vehicle._id,
        associateId,
      });

      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,
        previousCategory: vehicle.status,

        status: UpdatedVehicleStatus.inactive,
        comments: req.body.comments,
        isCompleted: false,
        category: req.body.category,
        subCategory: req.body.subCategory,
        previousStep: vehicle.step.stepName,
        step: vehicle.step.stepName,
        dateIn: date,
        // dateOut,
        tentativeDateOut: date,
      });

      await newLog.save();
      await newAwaiting.save();
      return;
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      const existsPrevious = await AwaitingInsuranceStock.findOne({ stockId: vehicle._id }, null, {
        sort: { createdAt: 1 },
      });
      if (!existsPrevious || vehicle.status !== VehicleStatus['awaiting-insurance'])
        throw new CustomChangeStatusError('No existe un proceso anterior', 400);

      await saveAdendumDocs(req, vehicle, associate);

      existsPrevious.dateFinished = req.body.dateFinished;
      existsPrevious.isCanceled = false;
      await existsPrevious.save();

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.isCompleted = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }

      return;
    },
    cancel: async (req, vehicle, associate) => {
      await AwaitingInsuranceStock.findOneAndUpdate(
        { stockId: vehicle._id, associateId: associate._id },
        { isCanceled: true }
      );

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }
    },
  },
  /* LEGAL PROCESS */
  'legal-process': {
    create: async ({ req }, vehicle, associateId) => {
      const { date } = req.body;
      if (!date) throw new CustomChangeStatusError('Campos faltantes', 400);
      const newLegalProcess = new LegalProcessStock({
        date,
        pausePayments: req.body.pausePayments,
        stockId: vehicle._id,
        associateId,
      });
      await newLegalProcess.save();

      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,
        previousCategory: vehicle.status,

        status: UpdatedVehicleStatus.inactive,
        comments: req.body.comments,
        isCompleted: false,
        category: req.body.category,
        subCategory: req.body.subCategory,
        previousStep: vehicle.step.stepName,
        step: vehicle.step.stepName,
        dateIn: date,
        // dateOut,
        tentativeDateOut: date,
      });

      await newLog.save();

      return;
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      const existsPrevious = await LegalProcessStock.findOne({ stockId: vehicle._id }, null, {
        sort: { createdAt: 1 },
      });
      if (!existsPrevious || vehicle.status !== VehicleStatus['legal-process'])
        throw new CustomChangeStatusError('No existe un proceso anterior', 400);

      await saveAdendumDocs(req, vehicle, associate);
      existsPrevious.isCanceled = false;
      existsPrevious.dateFinished = req.body.dateFinished;
      await existsPrevious.save();

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.isCompleted = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }

      return;
    },
    cancel: async (req, vehicle, associate) => {
      await LegalProcessStock.findOneAndUpdate(
        { stockId: vehicle._id, associateId: associate._id },
        { isCanceled: true }
      );

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: vehicle.status,
          previousSubCategory: foundLog.subCategory,
          previousStep: vehicle.step.stepName,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }
    },
  },
  workshop: {
    create: async ({ req }, vehicle, associateId) => {
      const { dateIn, dateOut } = req.body;
      if (!vehicle.adendumServiceCount) vehicle.adendumServiceCount = 1;
      else vehicle.adendumServiceCount += 1;
      if (!dateIn || !dateOut) throw new CustomChangeStatusError('Campos faltantes', 400);
      const newServiceStock = new ServiceStock({
        dateIn,
        dateOut,
        comments: req.body.comments,
        stockId: vehicle._id,
        associateId,
      });

      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,

        previousStep: vehicle.step.stepName,
        status: UpdatedVehicleStatus.inactive,
        category: req.body.category,
        subCategory: req.body.subCategory,
        step: vehicle.step.stepName,
        comments: req.body.comments,
        dateIn,
        isCompleted: false,
        tentativeDateOut: dateOut,
      });
      await newLog.save();
      await newServiceStock.save();

      return;
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      if ((req.files as Express.Multer.File[])?.length < 5)
        throw new CustomChangeStatusError('No se han subido los archivos requeridos', 400);
      const existsPrevious = await ServiceStock.findOne({ stockId: vehicle._id }, null, {
        sort: { createdAt: 1 },
      });
      const associatePayment = await AssociatePayments.findOne({ associateId: associate._id });
      if (associatePayment) {
        associatePayment.lengthAddedBefore = 0;
        associatePayment.adendumGenerated = false;
        await associatePayment.save();
      }
      if (!existsPrevious || vehicle.category !== VehicleCategory.workshop)
        throw new CustomChangeStatusError('No existe un proceso anterior', 400);
      const files = req.files as Express.Multer.File[];
      if (existsPrevious) {
        const docsIdsArray = await uploadArrayOfFiles(
          files,
          'in-service',
          vehicle,
          associate,
          vehicle.adendumServiceCount.toString() || '1'
        );
        existsPrevious.images = docsIdsArray;
        existsPrevious.dateFinished = req.body.dateFinished;
        existsPrevious.isCanceled = false;

        await existsPrevious.save();
      }
      vehicle.status = UpdatedVehicleStatus.active;

      const findLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      });

      if (findLog) {
        findLog.isCompleted = true;
        await findLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: findLog.category,
          previousSubCategory: findLog.subCategory,
          previousStep: findLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });
        await newLog.save();
      }

      await associate.save();
      return;
    },
    cancel: async (req, vehicle, associate) => {
      await ServiceStock.findOneAndUpdate(
        { stockId: vehicle._id, associateId: associate._id },
        { isCanceled: true }
      );

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }
    },
  },
  /* AWAITING INSURANCE */
  insurance: {
    create: async ({ req }, vehicle, associateId) => {
      const { date } = req.body;
      if (!date) throw new CustomChangeStatusError('Campos faltantes', 400);
      const newAwaiting = new AwaitingInsuranceStock({
        date,
        comments: req.body.comments,
        stockId: vehicle._id,
        associateId,
      });

      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,

        status: UpdatedVehicleStatus.inactive,
        comments: req.body.comments,
        isCompleted: false,
        category: req.body.category,
        subCategory: req.body.subCategory,
        previousStep: vehicle.step.stepName,
        step: vehicle.step.stepName,
        dateIn: date,
        // dateOut,
        tentativeDateOut: date,
      });

      await newLog.save();
      await newAwaiting.save();
      return;
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      const existsPrevious = await AwaitingInsuranceStock.findOne({ stockId: vehicle._id }, null, {
        sort: { createdAt: 1 },
      });
      if (!existsPrevious || vehicle.category !== VehicleCategory.insurance)
        throw new CustomChangeStatusError('No existe un proceso anterior', 400);

      await saveAdendumDocs(req, vehicle, associate);

      existsPrevious.dateFinished = req.body.dateFinished;
      existsPrevious.isCanceled = false;
      await existsPrevious.save();

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.isCompleted = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }

      return;
    },
    cancel: async (req, vehicle, associate) => {
      await AwaitingInsuranceStock.findOneAndUpdate(
        { stockId: vehicle._id, associateId: associate._id },
        { isCanceled: true }
      );

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }
    },
  },
  /* LEGAL PROCESS */
  legal: {
    create: async ({ req }, vehicle, associateId) => {
      const { date } = req.body;
      if (!date) throw new CustomChangeStatusError('Campos faltantes', 400);
      const newLegalProcess = new LegalProcessStock({
        date,
        pausePayments: req.body.pausePayments,
        stockId: vehicle._id,
        associateId,
      });
      await newLegalProcess.save();

      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,

        status: UpdatedVehicleStatus.inactive,
        comments: req.body.comments,
        isCompleted: false,
        category: req.body.category,
        subCategory: req.body.subCategory,
        previousStep: vehicle.step.stepName,
        step: vehicle.step.stepName,
        dateIn: date,
        // dateOut,
        tentativeDateOut: date,
      });

      await newLog.save();

      return;
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      const existsPrevious = await LegalProcessStock.findOne({ stockId: vehicle._id }, null, {
        sort: { createdAt: 1 },
      });
      if (!existsPrevious || vehicle.category !== VehicleCategory.legal)
        throw new CustomChangeStatusError('No existe un proceso anterior', 400);

      await saveAdendumDocs(req, vehicle, associate);
      existsPrevious.isCanceled = false;
      existsPrevious.dateFinished = req.body.dateFinished;
      await existsPrevious.save();

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.isCompleted = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }

      return;
    },
    cancel: async (req, vehicle, associate) => {
      await LegalProcessStock.findOneAndUpdate(
        { stockId: vehicle._id, associateId: associate._id },
        { isCanceled: true }
      );

      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: vehicle.status,
          previousSubCategory: foundLog.subCategory,
          previousStep: vehicle.step.stepName,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
        });

        await newLog.save();
      }
    },
  },
  sold: {
    create: async ({ req }, vehicle, associateId) => {
      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,

        previousStep: vehicle.step.stepName,
        status: UpdatedVehicleStatus.inactive,
        category: VehicleCategory.sold,
        subCategory: req.body.subCategory,
        step: vehicle.step.stepName,
        comments: req.body.comments,
        isCompleted: false,
        dateIn: new Date(),
      });
      await newLog.save();
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.isCompleted = true;
        foundLog.dateOut = req.body.dateFinished;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
          dateIn: new Date(),
        });
        await newLog.save();
      }
    },
    cancel: async (req, vehicle, associate) => {
      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
          dateIn: new Date(),
        });
        await newLog.save();
      }
    },
  },
  delivered: {
    create: async ({ req }, vehicle, associateId) => {
      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,

        previousStep: vehicle.step.stepName,
        status: UpdatedVehicleStatus.inactive,
        category: 'VehicleCategory.delivered',
        subCategory: req.body.subCategory,
        step: vehicle.step.stepName,
        comments: req.body.comments,
        isCompleted: false,
        dateIn: new Date(),
      });
      await newLog.save();
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.isCompleted = true;
        foundLog.dateOut = req.body.dateFinished;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
          dateIn: new Date(),
        });
        await newLog.save();
      }
    },
    cancel: async (req, vehicle, associate) => {
      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
          dateIn: new Date(),
        });
        await newLog.save();
      }
    },
  },
  utilitary: {
    create: async ({ req }, vehicle, associateId) => {
      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,

        previousStep: vehicle.step.stepName,
        status: UpdatedVehicleStatus.inactive,
        category: VehicleCategory.utilitary,
        subCategory: req.body.subCategory,
        step: vehicle.step.stepName,
        comments: req.body.comments,
        isCompleted: false,
        dateIn: new Date(),
      });
      await newLog.save();
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.isCompleted = true;
        foundLog.dateOut = req.body.dateFinished;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
          dateIn: new Date(),
        });
        await newLog.save();
      }
    },
    cancel: async (req, vehicle, associate) => {
      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
          dateIn: new Date(),
        });
        await newLog.save();
      }
    },
  },
  adendum: {
    create: async ({ req }, vehicle, associateId) => {
      const newLog = new StockVehicleStatusHistory({
        stockId: vehicle._id,
        associateId,
        userId: req.userReq.userId,
        previousStatus: UpdatedVehicleStatus.active,

        previousStep: vehicle.step.stepName,
        status: UpdatedVehicleStatus.inactive,
        category: VehicleCategory.adendum,
        subCategory: req.body.subCategory,
        step: vehicle.step.stepName,
        comments: req.body.comments,
        isCompleted: false,
        dateIn: new Date(),
      });
      await newLog.save();
    },
    finish: async (req, vehicle, associate) => {
      if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.isCompleted = true;
        foundLog.dateOut = req.body.dateFinished;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
          dateIn: new Date(),
        });
        await newLog.save();
      }
    },
    cancel: async (req, vehicle, associate) => {
      const foundLog = await StockVehicleStatusHistory.findOne({
        stockId: vehicle._id,
        status: UpdatedVehicleStatus.inactive,
        isCompleted: false,
      }).sort({ createdAt: -1 });

      if (foundLog) {
        foundLog.canceledAt = new Date();
        foundLog.canceledReason = req.body.canceledReason;
        foundLog.isCanceled = true;
        await foundLog.save();

        const newLog = new StockVehicleStatusHistory({
          stockId: vehicle._id,
          associateId: associate._id,
          userId: req.userReq.userId,
          previousStatus: UpdatedVehicleStatus.inactive,
          previousCategory: foundLog.category,
          previousSubCategory: foundLog.subCategory,
          previousStep: foundLog.step,
          status: UpdatedVehicleStatus.active,
          isCompleted: true,
          dateIn: new Date(),
        });
        await newLog.save();
      }
    },
  },
};

async function saveAdendumDocs(req: MyRequest, vehicle: typeof stock, associate: typeof associateIns) {
  const files = req.files as Express.Multer.File[];

  if (!files || files.length < 1) return; // No files to upload, return, not necessary to upload anything anymore

  const nameFile = removeEmptySpacesNameFile(files[0]);
  const path = `adendums/${vehicle.carNumber}/${associate.email}/${vehicle.status}/adendumDoc`;
  const res = await uploadFile(files[0], nameFile, path);

  const newDoc = new Document({
    originalName: nameFile,
    associateId: associate._id,
    path: res.input.Key,
  });
  await newDoc.save();
  associate.adendumDocs.push({ _id: newDoc._id });
}

// type FinishProcessObject = Record<Status, FinishProcessFunction>;

// export const finishProcess: FinishProcessObject = {
//   'in-service': {
//     finish: async (req, stockId, carNumber, associate) => {
//       if (!req.body.dateFinished) throw new CustomChangeStatusError('Campos faltantes', 400);
//       if ((req.files as Express.Multer.File[])?.length < 1)
//         throw new CustomChangeStatusError('No se han subido archivos', 400);
//       const existsPrevious = await ServiceStock.findOne({ stockId }, null, {
//         sort: { createdAt: -1 },
//       });
//       if (!existsPrevious) throw new CustomChangeStatusError('No existe un proceso anterior', 400);
//       const files = req.files as Express.Multer.File[];
//       if (existsPrevious) {
//         const docsIdsArray = await uploadArrayOfFiles(
//           files,
//           'in-service',
//           carNumber,
//           associate,
//           existsPrevious.createdCount.toString()
//         );
//         existsPrevious.images = docsIdsArray;
//         await existsPrevious.save();
//       }

//       return;
//     },
//   },
//   'awaiting-insurance': {
//     finish: async (req, stockId, carNumber, associate) => {
//       console.log(req.body, stockId, carNumber, associate);
//       return;
//     },
//   },
//   'legal-process': {
//     finish: async (req, stockId, carNumber, associate) => {
//       console.log(req.body, stockId, carNumber, associate);

//       return;
//     },
//   },
// };
