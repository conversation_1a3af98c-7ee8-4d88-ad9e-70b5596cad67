import { Types } from 'mongoose';
import { steps, stockVehiclesText } from '../../constants';
import StockVehicle, {
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleSubCategory,
} from '../../models/StockVehicleSchema';
import Readmission from '../../models/readmissionSchema';
import { parseBody } from '../../services/intex';
import { getCurrentDateTime } from '../../services/timestamps';
import { AsyncController } from '../../types&interfaces/types';
import fs from 'fs';
import { removeEmptySpacesNameFile } from '../../services/removeEmptySpaces';
import { uploadFile } from '../../aws/s3';
import Document from '../../models/documentSchema';
import Associate from '../../models/associateSchema';
// import AssociatePayments from '../../models/associatePayments';
// import { ValidRegion, gigstackRequests } from '../../services/tokenAssignGigstack';
import axios from 'axios';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../constants/payments-api';

function removeUploadFiles(files: Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] }) {
  if (files) {
    const filesArray: Express.Multer.File[] = Object.values(files).flat(Infinity);
    filesArray.forEach((file) => {
      if (file && file.path) {
        fs.unlink(file.path, (err) => {
          if (err) console.log(err);
          else console.log(`file ${file.originalname} removed`);
        });
      }
    });
  }
}

export const createReadmission: AsyncController = async (req, res) => {
  // const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };
  const { id } = req.params;
  const { userId } = req.userId;

  const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };

  console.log('files', files);
  const promissoryNoteTermination =
    'promissoryNoteTermination' in files ? files.promissoryNoteTermination[0] : undefined;
  const agreementTermination = 'agreementTermination' in files ? files.agreementTermination[0] : undefined;

  // if (req.body) {
  //   return res.status(200).send({ message: 'ok' });
  // }

  if (!promissoryNoteTermination)
    return res.status(404).send({ message: 'Archivo Pagare de terminación requerido' });

  if (!agreementTermination)
    return res.status(404).send({ message: 'Archivo de acuerdo de terminación requerido' });

  try {
    const stockVehicle = await StockVehicle.findById(id);
    if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    stockVehicle.readmissionDate = req.body.readmissionDate;
    stockVehicle.readmissionReason = req.body.readmissionReason;

    stockVehicle.step.stepName = steps.readmissions.name;
    stockVehicle.step.stepNumber = steps.readmissions.number;
    stockVehicle.status = 'readmissions';
    stockVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    stockVehicle.category = VehicleCategory.collection;
    stockVehicle.subCategory = VehicleSubCategory['in-recovery'];

    const reason: string = req.body.readmissionReason;
    const reasonsObj: { [key: string]: string } = {
      'no-payment': 'Falta de pago',
      canceled: 'Se cancelo',
      other: 'Otra razón',
    };

    const lastDriver = stockVehicle.drivers.slice(-1)[0];

    if (!lastDriver)
      return res
        .status(404)
        .send({ message: 'No se puede reasignar debido a que no existe un conductor en este vehiculo' });

    // const
    const uploaded = await uploadFile(
      promissoryNoteTermination,
      promissoryNoteTermination.originalname,
      `readmissions/${stockVehicle.carNumber}/${lastDriver._id}/pagareTermination/`
    );
    const key = uploaded.input.Key!;

    const promissoryNoteDocument = new Document({
      originalName: promissoryNoteTermination.originalname,
      path: key,
      associateId: lastDriver._id,
      vehicleId: stockVehicle._id,
    });

    await promissoryNoteDocument.save();

    const uploaded2 = await uploadFile(
      agreementTermination,
      agreementTermination.originalname,
      `readmissions/${stockVehicle.carNumber}/${lastDriver._id}/agreementTermination/`
    );

    const key2 = uploaded2.input.Key!;

    const agreementDocument = new Document({
      originalName: agreementTermination.originalname,
      path: key2,
      vehicleId: stockVehicle._id,
      associateId: lastDriver._id,
    });

    await agreementDocument.save();

    let objectToCreate: any = {
      km: req.body.km || stockVehicle.km.toString(),
      readmissionReason: reason,
      readmissionDate: req.body.readmissionDate,
      description: reason ? reasonsObj[reason] || '' : '',
      stockVehicleId: new Types.ObjectId(id),
      associateId: lastDriver._id,
      contractNumber: stockVehicle.extensionCarNumber
        ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
        : stockVehicle.carNumber,
      terminationFiles: {
        promissoryNote: promissoryNoteDocument._id,
        agreement: agreementDocument._id,
      },
    };

    if (stockVehicle.extensionCarNumber) {
      objectToCreate.extensionContractNumber = stockVehicle.extensionCarNumber;
    }

    // check if readmission already exists for this associate and vehicle
    // if exists, update the readmission, if not, create a new one

    const readmissionExists = await Readmission.findOne({
      associateId: lastDriver._id,
      stockVehicleId: stockVehicle._id,
    });

    if (readmissionExists) {
      readmissionExists.readmissionReason = reason;
      readmissionExists.readmissionDate = req.body.readmissionDate;
      readmissionExists.description = reason ? reasonsObj[reason] || '' : '';
      await readmissionExists.save();
    } else {
      await Readmission.create(objectToCreate);
    }

    await Associate.findByIdAndUpdate(lastDriver, { active: false });

    stockVehicle.updateHistory.push({
      userId,
      step: 'SOLICITUD DE REINGRESO',
      description: reason ? reasonsObj[reason] || '' : '',
      time: getCurrentDateTime(),
    });

    const associate = await Associate.findById(lastDriver._id);

    let description: string = '';
    // console.log('associatePayment', associatePayment);
    if (associate && associate.clientId) {
      if (associate && associate.clientId) {
        try {
          const data = await axios.patch(
            `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
            {
              status: false,
            },
            {
              headers: {
                Authorization: `Bearer ${PAYMENTS_API_KEY}`,
              },
            }
          );

          if (data) {
            description = 'Se pauso el pago del asociado';
          }
        } catch (error) {
          console.log('error', error);
        }
      }
    }

    await stockVehicle.save();

    return res.status(200).send({ message: 'Vehiculo enviado a reingresos', description });
  } catch (error) {
    // else return res.status(201).send({ message: 'No existe, puede crearse' });
    console.log(error);
    return res.status(500).send({ message: 'Hubo un error', error });
  }
};

type FieldName = 'promissoryNote' | 'readmissionDoc' | 'contractCanceled' | 'signedPromissoryNote';

export const returnToStock: AsyncController = async (req, res) => {
  const { id } = req.params;

  const files = req.files as { [fieldname: string]: Express.Multer.File[] };
  try {
    const readmissionVehicle = await StockVehicle.findById(id);

    if (!readmissionVehicle) return res.status(200).send({ message: 'Vehiculo no encontrado' });

    readmissionVehicle.status = 'stock';
    readmissionVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    readmissionVehicle.category = VehicleCategory.stock;
    readmissionVehicle.subCategory = VehicleSubCategory.default;
    readmissionVehicle.step = {
      stepName: steps.vehicleReady.name,
      stepNumber: steps.vehicleReady.number,
    };

    const extension = readmissionVehicle.extensionCarNumber;
    if (!extension) {
      readmissionVehicle.extensionCarNumber = 2;
    } else {
      readmissionVehicle.extensionCarNumber = extension + 1;
    }

    readmissionVehicle.newCar = false;

    // const awsDirectory =
    const documentFields = [
      { fieldName: 'kmImgs', directory: 'kmImgs' },
      { fieldName: 'evidenceImgs', directory: 'evidenceImgs' },
      { fieldName: 'promissoryNote', directory: 'promissoryNote' },
      { fieldName: 'readmissionDoc', directory: 'readmissionDoc' },
      { fieldName: 'contractCanceled', directory: 'contractCanceled' },
      { fieldName: 'signedPromissoryNote', directory: 'signedPromissoryNote' },
      { fieldName: 'agreementSigned', directory: 'agreementSigned' },
      { fieldName: 'recessionSigned', directory: 'recessionSigned' },
    ];

    const bodyParsed = parseBody(req.body);
    const { km, comments } = bodyParsed;
    if (!km) return res.status(404).send({ message: 'Kilometros requeridos' });

    readmissionVehicle.km = km;
    // removeUploadFiles(files);

    const currentAssociateId = readmissionVehicle.drivers[readmissionVehicle.drivers.length - 1]._id;
    if (!currentAssociateId) return res.status(404).send({ message: 'No existe un asociado' });
    const lastAssociate = await Associate.findById(currentAssociateId);

    if (!lastAssociate) return res.status(404).send({ message: 'No se encontro al asociado' });
    // const readmissions =
    if (files) {
      const readmission = await Readmission.findOne({
        associateId: lastAssociate._id,
        stockVehicleId: readmissionVehicle._id,
      });
      if (readmission) {
        readmission.km = km;
        if (comments) {
          readmission.comments = comments;
        }
      }
      for (const field of documentFields) {
        if (field.fieldName in files) {
          const fieldConversion = field.fieldName as unknown as keyof typeof files;
          const fieldFiles = files[fieldConversion].length > 0 ? files[fieldConversion] : undefined;

          const file = files[fieldConversion] ? files[fieldConversion][0] : undefined;
          // Check if the field is an array of files, if so, iterate over the files and upload them
          if ((field.fieldName === 'kmImgs' || field.fieldName === 'evidenceImgs') && fieldFiles) {
            for (const f of fieldFiles) {
              const removeSpacesFileName = removeEmptySpacesNameFile(f);
              // eslint-disable-next-line prettier/prettier
              const s3Path = `readmissions/${readmissionVehicle.carNumber}/${currentAssociateId}/${field.directory}/`;
              const command = await uploadFile(f, removeSpacesFileName, s3Path);
              const path = command.input.Key;
              const documentPath = path;
              const document = new Document({
                originalName: removeSpacesFileName,
                path: documentPath,
                associateId: currentAssociateId,
                vehicleId: readmissionVehicle._id,
              });

              if (readmission) {
                // const terminationFiles: any = readmission.terminationFiles || {};
                if (field.directory === 'kmImgs') {
                  readmission.kmImgs.push(document._id);
                }
                if (field.directory === 'evidenceImgs') {
                  readmission.evidenceImgs.push(document._id);
                }
              }

              await document.save();
            }
          } else if (file) {
            const removeSpacesFileName = removeEmptySpacesNameFile(file);
            const s3Path = `readmissions/${readmissionVehicle.carNumber}/${currentAssociateId}/${field.directory}/`;
            const command = await uploadFile(file, removeSpacesFileName, s3Path);

            const documentPath = command.input.Key;

            const document = new Document({
              originalName: removeSpacesFileName,
              path: documentPath,
              associateId: currentAssociateId,
            });
            if (readmission) {
              const filedTyped = field.fieldName as FieldName;
              const terminationFiles: typeof readmission.terminationFiles =
                readmission.terminationFiles || {};
              if (filedTyped === 'promissoryNote') {
                // readmission.terminationFiles.promissoryNote = document._id;
                terminationFiles.promissoryNoteSigned = document._id;
              }
              if (field.fieldName === 'agreementSigned') {
                terminationFiles.agreementSigned = document._id;
              }
              if (field.fieldName === 'recessionSigned') {
                terminationFiles.recessionSigned = document._id;
              } else {
                readmission[filedTyped] = document._id;
              }
              readmission.terminationFiles = terminationFiles;
            }
            await document.save();
          }
        }
      }
      await readmission?.save();
    }

    readmissionVehicle.readmissionDate = '';
    readmissionVehicle.readmissionReason = '';

    readmissionVehicle.updateHistory.push({
      userId: req.userId.userId,
      step: 'VEHICULO REINGRESADO A STOCK',
      description: '',
      time: getCurrentDateTime(),
    });

    await readmissionVehicle.save();
    return res.status(200).send({ message: 'Vehiculo devuelto a stock', readmissionVehicle });
  } catch (error) {
    removeUploadFiles(files);
    return res.status(500).send({ message: 'Hubo un error', error });
  }
};
