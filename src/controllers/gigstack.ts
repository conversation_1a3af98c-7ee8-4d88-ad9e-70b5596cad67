import { GIGSTACK_TOKEN, RASAYEL_TOKEN, associatePaymentsConsts } from '../constants';
import AssociatePayments from '../models/associatePayments';
import { AsyncController } from '../types&interfaces/types';
import axios from 'axios';
import { ValidRegion, tokenAssignGigstack } from '../services/tokenAssignGigstack';
import RegionsPayments from '../models/regionPaymentsSchema';
import { saveGigErrors } from '../services/saveGigError';
import GigPayments from '../models/gigPaymentsSchema';

export const gigstackWebhookCdmx: AsyncController = async (req, res) => {
  const { data } = req.body;
  const requestBody = {
    query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
    operationName: 'TemplateProactiveMessageCreate',
    variables: {
      input: {
        messageTemplateId: 30664,
        // channelId: 2094, <- original CDMX
        channelId: 2026,

        components: [
          {
            parameters: [
              { text: data.client.name, type: 'TEXT' },
              { text: data.shortURL, type: 'TEXT' },
            ],
            type: 'BODY',
          },
        ],
        receiver: data.client.phone,
      },
    },
  };
  const config = {
    headers: {
      Authorization: `Basic ${RASAYEL_TOKEN}`,
    },
  };
  try {
    const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
    return res.status(200).send(rasayel.data);
  } catch (error) {
    await saveGigErrors('gigstackWebhookCDMX', data, error);
    return res.status(400);
  }
};

export const gigstackWebhookGdl: AsyncController = async (req, res) => {
  const { data } = req.body;
  const requestBody = {
    query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
    operationName: 'TemplateProactiveMessageCreate',
    variables: {
      input: {
        messageTemplateId: 30665,
        channelId: 2026,
        components: [
          {
            parameters: [
              { text: data.client.name, type: 'TEXT' },
              { text: data.shortURL, type: 'TEXT' },
            ],
            type: 'BODY',
          },
        ],
        receiver: data.client.phone,
      },
    },
  };
  const config = {
    headers: {
      Authorization: `Basic ${RASAYEL_TOKEN}`,
    },
  };
  try {
    const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
    return res.status(200).send(rasayel.data);
  } catch (error) {
    await saveGigErrors('gigstackWebhookGdl', data, error);
    return res.status(400);
  }
};

export const gigstackWebhookMty: AsyncController = async (req, res) => {
  const { data } = req.body;
  const requestBody = {
    query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
    operationName: 'TemplateProactiveMessageCreate',
    variables: {
      input: {
        messageTemplateId: 30666,
        channelId: 2027,
        components: [
          {
            parameters: [
              { text: data.client.name, type: 'TEXT' },
              { text: data.shortURL, type: 'TEXT' },
            ],
            type: 'BODY',
          },
        ],
        receiver: data.client.phone,
      },
    },
  };
  const config = {
    headers: {
      Authorization: `Basic ${RASAYEL_TOKEN}`,
    },
  };
  try {
    const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
    return res.status(200).send(rasayel.data);
  } catch (error) {
    await saveGigErrors('gigstackWebhookMty', data, error);
    return res.status(400);
  }
};

export const gigstackWebhookMoka: AsyncController = async (req, res) => {
  const { data } = req.body;
  const requestBody = {
    query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
    operationName: 'TemplateProactiveMessageCreate',
    variables: {
      input: {
        messageTemplateId: 40041,
        channelId: 4915,
        components: [
          {
            parameters: [
              { text: data.client.name, type: 'TEXT' },
              { text: data.shortURL, type: 'TEXT' },
            ],
            type: 'BODY',
          },
        ],
        receiver: data.client.phone,
      },
    },
  };
  const config = {
    headers: {
      Authorization: `Basic ${RASAYEL_TOKEN}`,
    },
  };
  try {
    const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
    return res.status(200).send(rasayel.data);
  } catch (error) {
    await saveGigErrors('gigstackWebhookMoka', data, error);
    return res.status(400);
  }
};

export const getPendingPayments: AsyncController = async (req, res) => {
  const { region } = req.params;

  if (!(region in associatePaymentsConsts.gigstackValues)) {
    return res.status(400).json({ error: 'Región no válida' });
  }
  try {
    const gigToken = tokenAssignGigstack(region as ValidRegion);

    const config = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const response = await axios.get(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/list?limit=100&status=requires_payment_method',
      config
    );

    const simplifiedData = response.data.data.map((item: any) => ({
      fecha: item.fecha,
      numeroTelefono: item.numeroTelefono,
      montoPagar: item.montoPagar,
      shortUrl: item.shortUrl,
    }));

    return res.status(200).send(simplifiedData);
  } catch (error) {
    console.error(error);
    await saveGigErrors('getPendingPayments', {}, error);
    return res.status(400).send({ message: 'Ocurrio un error', error });
  }
};

export const paymentLink: AsyncController = async (req, res) => {
  const { region } = req.params;

  if (!(region in associatePaymentsConsts.gigstackValues)) {
    await saveGigErrors('paymentLink', { region }, 'Región no válida');
    return res.status(400).json({ error: 'Región no válida' });
  }

  try {
    const gigToken = tokenAssignGigstack(region as ValidRegion);

    const data = {
      client: {
        name: 'Antonio Test',
        email: '<EMAIL>',
      },
      items: [
        {
          name: 'a',
          description: '',
          quantity: 1,
          total: 1000,
        },
      ],
      currency: 'MXN',
      custom_method_types: ['bank'],
    };
    const config = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };
    const response = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/create',
      data,
      config
    );
    return res.status(200).send({ message: 'Link de pago creado exitosamente', data: response.data });
  } catch (error) {
    console.error(error);
    await saveGigErrors('paymentLink', {}, error);
    return res.status(400).send({ message: 'Ocurrio un error', error });
  }
};

export const updateStatus: AsyncController = async (req, res) => {
  const { data } = req.body;
  const { regionCode } = req.params;
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    if (!associatePayments) {
      await saveGigErrors('updateStatus', { data, regionCode }, associatePaymentsConsts.errors.payment404);
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }

    const region = await RegionsPayments.findOne({ region: regionCode });

    if (!region) {
      await saveGigErrors(
        'updateStatus',
        { data, regionCode },
        associatePaymentsConsts.errors.notValidRegion
      );
      return res.status(404).send({ message: associatePaymentsConsts.errors.notValidRegion });
    }
    console.log({ data });
    const index = associatePayments.paymentNumber;

    const paymentToUpdate = associatePayments.paymentsArray[index];
    const blockObj = associatePayments.paymentsArray[index].blockObj;
    const otherPayment = associatePayments.otherPayment;
    const updatedPayment = [];

    const isBlockPayment = data.items.some((item: any) => item.id === region.reactivationFee);

    console.log({ paymentToUpdate, otherPayment });

    if (paymentToUpdate?.paymentId === data.id || paymentToUpdate?.paymentId === data.fid) {
      console.log("I shouldn't be here");
      paymentToUpdate.status = data.status;
      updatedPayment.push(paymentToUpdate);
    } else if (
      isBlockPayment &&
      blockObj &&
      (blockObj.paymentId === data.fid || blockObj.paymentId === data.id)
    ) {
      console.log('nonsense block condition');
      blockObj.status = data.status;
      updatedPayment.push(blockObj);
    } else {
      console.log('other payment condition', data.id, data.status, data.fid);
      otherPayment.map((payment) => {
        if (payment.transactionId === data.id || payment.transactionId === data.fid) {
          payment.status = data.status;
        }
      });
    }
    if (
      associatePayments.block === false &&
      paymentToUpdate.status === associatePaymentsConsts.gigstackValues.successStatus
    ) {
      associatePayments.paymentNumber++;

      await associatePayments.save();

      return res.status(200).send({
        message: associatePaymentsConsts.success.paymentUpdated,
        paymentToUpdate,
      });
    } else if (
      blockObj &&
      blockObj?.status === associatePaymentsConsts.gigstackValues.successStatus &&
      paymentToUpdate.status === associatePaymentsConsts.gigstackValues.successStatus &&
      associatePayments.block === true
    ) {
      associatePayments.block = false;
      associatePayments.paymentNumber++;

      return res.status(200).send({
        message: associatePaymentsConsts.success.paymentUpdated,
        paymentToUpdate,
        blockObj,
      });
    }

    await associatePayments?.save();

    return res.status(200).send({ message: associatePaymentsConsts.success.paymentUpdated, updatedPayment });
  } catch (error) {
    await saveGigErrors('updadeStatus', data, error);
    console.error(error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const weeklyCheckPayment: AsyncController = async (req, res) => {
  const { data } = req.body;

  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    if (!associatePayments) {
      await saveGigErrors('weeklyCheckPayment', { data }, associatePaymentsConsts.errors.payment404);
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }

    const index = associatePayments.paymentNumber;

    const weekPayment = associatePayments?.paymentsArray[index];

    if (weekPayment.status !== 'succeeded') {
      //Ingresar alguna logica para suscribir al asociado a recibir recordatorios recurrentes
    }
    return res.status(200).send({ message: associatePaymentsConsts.success.paymentVerification });
  } catch (error) {
    console.error(error);
    await saveGigErrors('weeklyCheckPayment', data, error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const blockPayment: AsyncController = async (req, res) => {
  const { data } = req.body;
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    if (!associatePayments) {
      await saveGigErrors('blockPayment', { data }, associatePaymentsConsts.errors.payment404);
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }

    const payment = {
      client: {
        name: data.name,
        email: data.client.email,
      },
      items: [
        {
          name: 'Cargo por reactivacion',
          description: '',
          quantity: 1,
          total: 86,
        },
      ],
      currency: 'MXN',
      custom_method_types: ['bank'],
    };
    const config = {
      headers: {
        Authorization: `Bearer ${GIGSTACK_TOKEN}`,
        'Content-Type': 'application/json',
      },
    };

    const index = associatePayments.paymentNumber;
    const response = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/create',
      payment,
      config
    );

    const blockObj = associatePayments?.paymentsArray[index].blockObj;
    if (blockObj) {
      associatePayments.block = true;
      const date = new Date(response.data.data.timestamp);
      blockObj.status = response.data.data.status;
      blockObj.url = response.data.data.shortUrl;
      blockObj.paymentId = response.data.data.fid;
      blockObj.date = date.toString();
    }

    await associatePayments.save();

    return res.status(200).send({
      message: associatePaymentsConsts.success.blockPayment,
      payment: blockObj,
    });
  } catch (error) {
    console.error(error);
    await saveGigErrors('blockPayments', data, error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const blockPaymentPacificTime: AsyncController = async (req, res) => {
  const { data } = req.body;
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    if (!associatePayments) {
      await saveGigErrors('blockPaymentPacificTime', { data }, associatePaymentsConsts.errors.payment404);
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }

    const payment = {
      client: {
        name: data.name,
        email: data.client.email,
      },
      items: [
        {
          name: 'Cargo por reactivacion',
          description: '',
          quantity: 1,
          total: 86,
        },
      ],
      currency: 'MXN',
      custom_method_types: ['bank'],
    };
    const config = {
      headers: {
        Authorization: `Bearer ${GIGSTACK_TOKEN}`,
        'Content-Type': 'application/json',
      },
    };

    const index = associatePayments.paymentNumber;

    const response = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/create',
      payment,
      config
    );

    const blockObj = associatePayments?.paymentsArray[index].blockObj;
    if (blockObj) {
      associatePayments.block = true;

      const date = new Date(response.data.data.timeStamp);
      blockObj.status = response.data.data.status;
      blockObj.url = response.data.data.shortUrl;
      blockObj.paymentId = response.data.data.id;
      blockObj.date = date.toString();
    }

    await associatePayments.save();

    return res.status(200).send({
      message: associatePaymentsConsts.success.blockPayment,
      payment: response,
    });
  } catch (error) {
    console.error(error);
    await saveGigErrors('blockPaymentPacificTime', data, error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const reminderPayment: AsyncController = async (req, res) => {
  const { data } = req.body;
  try {
    let succeded = 0;
    let failed = 0;
    await Promise.all(
      data.map(async (payment: any) => {
        // const associatePayments = await AssociatePayments.findOne({ associateEmail: payment.client.email });
        if (payment.status !== 'succeeded') {
          const requestBody = {
            query: `mutation TemplateProactiveMessageCreate($input: MessageProactiveTemplateCreateInput!){ templateProactiveMessageCreate(input: $input) { clientMutationId } }`,
            operationName: 'TemplateProactiveMessageCreate',
            variables: {
              input: {
                messageTemplateId: 31588,
                // channelId: 2094,
                channelId: 2026,
                components: [
                  {
                    parameters: [
                      { text: payment.client.name, type: 'TEXT' },
                      { text: payment.shortURL, type: 'TEXT' },
                    ],
                    type: 'BODY',
                  },
                ],
                receiver: payment.client.phone,
              },
            },
          };
          const config = {
            headers: {
              Authorization: `Basic ${RASAYEL_TOKEN}`,
            },
          };
          try {
            const rasayel = await axios.post('https://api.rasayel.io/api/graphql', requestBody, config);
            if (rasayel.data) {
              succeded++;
            } else {
              failed++;
            }
          } catch (error) {
            console.error(error);
            failed++;
          }
        }
      })
    );
    return res
      .status(200)
      .send({ message: `Enviados ${succeded} recordatorios con exito, no enviados ${failed}` });
  } catch (error) {
    console.error(error);
    await saveGigErrors('reminderPayment', data, error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const cancelGigstack: AsyncController = async (req, res) => {
  const { data } = req.body;
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    if (!associatePayments) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }
    //Todo: Logica para manejar casos de que cancelemos el pago de la semana, tiene que haber movimientos dentro del array si es semanmal

    //Calculo del balance
    associatePayments.balance += data.items[0].total;
    await associatePayments.save();

    return res.status(200).send({ message: associatePaymentsConsts.success.paymentCancelled });
  } catch (error) {
    console.error(error);
    await saveGigErrors('cancelGigstack', data, error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const markaspaid: AsyncController = async (req, res) => {
  const { data, region } = req.body;
  if (!(region as ValidRegion)) {
    return res.status(400).json({ error: 'Región no válida' });
  }
  if (!data) res.status(400).send({ message: associatePaymentsConsts.errors.error });

  const gigToken = tokenAssignGigstack(region);

  const config = {
    headers: {
      Authorization: `Bearer ${gigToken}`,
      'Content-Type': 'application/json',
    },
  };
  const responseData: any[] = [];
  for (const item of data) {
    const gigBody = {
      id: item,
      paymentForm: '03',
    };
    try {
      const response = await axios.put(
        'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/markaspaid',
        gigBody,
        config
      );
      responseData.push({
        id: item,
        message: associatePaymentsConsts.success.paymentUpdated,
        payment: response.data,
      });
    } catch (error: any) {
      console.error(error);
      await saveGigErrors('markaspaid', associatePaymentsConsts.errors.error, error.response.data);
      responseData.push({
        id: item,
        message: associatePaymentsConsts.errors.error,
        error: error.response.data,
      });
    }
  }

  return res.status(200).send({ message: responseData });
};

export const updateMarkaspaid: AsyncController = async (req, res) => {
  const getPayments = await GigPayments.find({ isPaid: false });
  for (const payment of getPayments) {
    const gigToken = tokenAssignGigstack(payment.region as ValidRegion);
    const config = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    try {
      const gigPaymentUpdate = await axios.get(
        `https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/view?id=${payment.body.id}`,
        config
      );
      console.log(gigPaymentUpdate.data.status, gigPaymentUpdate.data.id, payment.body.id);
      if (gigPaymentUpdate.data.status === 'succeeded' || gigPaymentUpdate.data.status === 'canceled') {
        payment.isPaid = true;
        await payment.save();
      }
    } catch (error: any) {
      console.error(error);
      if (error.response.data.message === 'El pago no existe') await payment.remove();
    }
  }
  return res.status(200).send({ message: 'Pagos actualizados' });
};

export const updateStatusGigstackDeleted: AsyncController = async (req, res) => {
  const { data } = req.body;
  try {
    await GigPayments.deleteMany({ 'body.id': data.id }).then(() => {
      return res.status(200).send({ message: 'Pagos eliminados' });
    });
    return res.status(200).send();
  } catch (error) {
    console.error(error);
    await saveGigErrors('updateStatusGigstackDeleted', data, error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};
