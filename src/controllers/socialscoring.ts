import { repoGetMediaSignedUrl } from '@/clean/data/s3Repositories';
import { retrieveMostRecent12WeeksEarnings } from '@/clean/domain/usecases';
import { AdmissionRequestMongo } from '@/models/admissionRequestSchema';
import { MetricMongo } from '@/models/metricsSchema';
import DocumentMongo from '@/models/documentSchema';
import { AdmissionRequest } from '@/clean/domain/entities';

export async function getHomeVisitModelBody(requestId: string) {
  const request = await AdmissionRequestMongo.findOne({ _id: requestId });
  return request;
}

export async function getPreApprovalBody(requestId: string) {
  const request = await AdmissionRequestMongo.findOne({ _id: requestId });
  if (!request || !request.earningsAnalysis) {
    throw new Error('Request not found');
  }

  const earnings = request.earningsAnalysis.earnings.map((earning) => earning?.totalAmount);
  const mostRecent12Weeks = await retrieveMostRecent12WeeksEarnings(requestId);
  const metrics = await MetricMongo.findOne({ requestId });

  return {
    ...request.toObject(),
    metrics: metrics?.toObject(),
    earnings,
    platform_deposits: mostRecent12Weeks.length,
  };
}

export async function getFinancialAssessmentBody(admissionRequest: AdmissionRequest) {
  const earnings = admissionRequest.earningsAnalysis.earnings;
  const validEarnings = earnings?.filter((earning) => earning?.totalAmount !== undefined) ?? [];
  const weeklyEarnings =
    validEarnings.length > 0
      ? validEarnings.reduce((sum, earning) => sum + earning.totalAmount, 0) / validEarnings.length
      : 0;

  const pdfArray = await Promise.all(
    admissionRequest.documentsAnalysis.documents
      .filter((doc) => doc.type.includes('bank_statement'))
      .map(async (doc) => {
        const media = await DocumentMongo.findById(doc.mediaId);

        if (!media) {
          return null;
        }

        const signedUrl = await repoGetMediaSignedUrl(media.path);
        return signedUrl;
      })
  );

  return {
    customer_id: admissionRequest.id,
    weekly_earnings: weeklyEarnings,
    pdf_array: pdfArray,
  };
}
