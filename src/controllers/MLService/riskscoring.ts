import { Async<PERSON>ontroller } from '@/types&interfaces/types';
import { getPreApprovalBody } from '../socialscoring';
import { SOCIAL_SCORING_URL } from '@/constants/onboarding';
import { logger } from '@/clean/lib/logger';

export const getRiskScore: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;

    const body = await getPreApprovalBody(requestId);

    const response = await fetch(`${SOCIAL_SCORING_URL}/risk-scoring`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(body),
    });
    const data = await response.json();

    if (data.status === 'success') {
      return res.status(200).send({
        ...data,
      });
    } else {
      logger.error(`[getRiskScore] Error in socialscoring service status: ${data.message}`);
      throw new Error(data.message);
    }
  } catch (error) {
    logger.error(`[getRiskScore] Error in fetching riskscoring for rideshare performance: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing social score',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
