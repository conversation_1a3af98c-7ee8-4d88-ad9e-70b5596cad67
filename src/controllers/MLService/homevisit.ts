import { Async<PERSON>ontroller } from '@/types&interfaces/types';
import { logger } from '@/clean/lib/logger';
import { getHomeVisitModelBody } from '../socialscoring';
import { SOCIAL_SCORING_URL } from '@/constants/onboarding';

export const getHomevisitStackingclfScoring: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;
    const requestBody = await getHomeVisitModelBody(requestId);
    if (!requestBody || !requestBody.earningsAnalysis) {
      logger.warn(`[getHomevisitStackingclfScoring] Request not found ${requestId}`);
      return res.status(404).send('Request not found');
    }

    const response = await fetch(`${SOCIAL_SCORING_URL}/homevisit/stacking-clf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(requestBody),
    });
    const data = await response.json();

    const score = data.probability;
    const message = `Probability: ${(score * 100).toFixed(2)}% for approval`;
    const status = data.message.toLowerCase();

    return res.status(200).send({
      ...data,
      message,
      status,
      score,
    });
  } catch (error) {
    logger.error(`[getHomevisitStackingclfScoring] Error in getSocialScoring: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing social scoring',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

export const getHomevisitVotingclfScoring: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;
    const requestBody = await getHomeVisitModelBody(requestId);
    if (!requestBody || !requestBody.earningsAnalysis) {
      logger.warn(`[getHomevisitVotingclfScoring] Request not found ${requestId}`);
      return res.status(404).send('Request not found');
    }

    const response = await fetch(`${SOCIAL_SCORING_URL}/homevisit/voting-clf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(requestBody),
    });
    const data = await response.json();

    const score = data.probability;
    const message = `Probability: ${(score * 100).toFixed(2)}% for approval`;
    const status = data.message.toLowerCase();

    return res.status(200).send({
      ...data,
      message,
      status,
      score,
    });
  } catch (error) {
    logger.error(`[getHomevisitVotingclfScoring] Error in getSocialScoring: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing social scoring',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
