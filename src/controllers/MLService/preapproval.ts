import { Async<PERSON>ontroller } from '@/types&interfaces/types';
import { getPreApprovalBody } from '../socialscoring';
import { SOCIAL_SCORING_URL } from '@/constants/onboarding';
import { logger } from '@/clean/lib/logger';

export const getPreApproval: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;

    const body = await getPreApprovalBody(requestId);

    const response = await fetch(`${SOCIAL_SCORING_URL}/v2/pre-approval`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(body),
    });
    const data = await response.json();
    let message = data.result;
    let status = '';

    if (data.result.includes(':')) {
      [message, status] = data.result.split(':');
      status = status.trim();
    }

    return res.status(200).send({
      message,
      status,
    });
  } catch (error) {
    logger.error(`[getPreApproval] Error in getPreApproval: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing pre-approval',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
