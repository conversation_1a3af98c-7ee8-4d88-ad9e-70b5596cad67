/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import cron from 'node-cron';
import { GIGSTACK_TOKEN, RUN_FETCH_VEHICLE_VIOLATION_CRON } from './constants';
import { createBlockPayment, createRemindersGigstack } from './services/cronGigstackFunctions';
import { notificationEmailForCustomersWhoHasNotStartedOnboardingWithInTwoDays } from './clean/domain/usecases';
import { calendarService } from './modules/Calendar/services/calendar.service';
import { vehicleViolationService } from './modules/VehicleViolation/services/vehicleViolation.service';
import { logger } from './clean/lib/logger';

const initScheduleJobs = () => {
  // const reminderPayment = cron.schedule(
  //   '0 9-16/4 * * 1',
  //   async () => {
  //     createRemindersGigstack(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // const reminderPaymentQro = cron.schedule(
  //   '0 9-16/4 * * 1',
  //   async () => {
  //     createRemindersGigstack(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // const reminderPaymentGdl = cron.schedule(
  //   '0 9-16/4 * * 1',
  //   async () => {
  //     createRemindersGigstack(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // const reminderPaymentTij = cron.schedule(
  //   '0 9-16/4 * * 1',
  //   async () => {
  //     createRemindersGigstack(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Tijuana' }
  // );
  // const reminderPaymentMty = cron.schedule(
  //   '0 9-16/4 * * 1',
  //   async () => {
  //     createRemindersGigstack(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // const blockPayment = cron.schedule(
  //   '* * * * *',
  //   async () => {
  //     console.log('blockPayment');
  //     createBlockPayment('GDL');
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // const blockPaymentQro = cron.schedule(
  //   '* * * * *',
  //   async () => {
  //     createBlockPayment(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // const blockPaymentGdl = cron.schedule(
  //   '* * * * *',
  //   async () => {
  //     createBlockPayment(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // const blockPaymentTij = cron.schedule(
  //   '* * * * *',
  //   async () => {
  //     createBlockPayment(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // const blockPaymentMty = cron.schedule(
  //   '* * * * *',
  //   async () => {
  //     createBlockPayment(GIGSTACK_TOKEN);
  //   },
  //   { timezone: 'America/Mexico_City' }
  // );
  // blockPayment.start();
  // blockPaymentQro.start();
  // blockPaymentGdl.start();
  // blockPaymentTij.start();
  // blockPaymentMty.start();
  // reminderPayment.start();
  // reminderPaymentQro.start();
  // reminderPaymentGdl.start();
  // reminderPaymentTij.start();
  // reminderPaymentMty.start();

  const autoNotificationEmailForCustomers = cron.schedule(
    '0 0 * * *', // run every day at 00:00
    async () => {
      await notificationEmailForCustomersWhoHasNotStartedOnboardingWithInTwoDays();
    },
    { timezone: 'America/Mexico_City' }
  );
  autoNotificationEmailForCustomers.start();

  /**
   * this cron will run from monday-friday every night at 10PM to notify customers about their appointment the next day
   */
  const autoNotificationForCustomersAboutHomeVisitAppointmentOneNightAgo = cron.schedule(
    '0 22 * * 1-5', //
    async () => {
      await calendarService.notifyCustomersAboutTheirAppointmentOneNightAgoCron();
    },
    { timezone: 'America/Mexico_City' }
  );

  /**
   * this cron will run every monday-friday from 8:30AM till 5:30PM every half hour
   */
  const autoNotificationForCustomersAboutHomeVisitAppointmentHalfHourAgo = cron.schedule(
    '*/10 8-17 * * 1-5',
    async () => {
      await calendarService.notifyCustomersAboutTheirAppointmentAlmostFiveMinutesBefore();
    },
    { timezone: 'America/Mexico_City' }
  );

  /**
   *  this cron will run every sunday at 8PM to add slots for home visitors for the next two weeks
   */
  const slotsOfNextTwoWeeksForHomeVisitors = cron.schedule(
    '0 17 * * 7',
    async () => {
      await calendarService.addSlotsOfNextTwoWeeksForHomeVisitosCron();
    },
    { timezone: 'America/Mexico_City' }
  );

  autoNotificationForCustomersAboutHomeVisitAppointmentOneNightAgo.start();
  autoNotificationForCustomersAboutHomeVisitAppointmentHalfHourAgo.start();
  slotsOfNextTwoWeeksForHomeVisitors.start();

  if (!RUN_FETCH_VEHICLE_VIOLATION_CRON) {
    logger.info(`[VehicleViolation] - fetchVehiceViolation cron job is disabled`);
    return;
  } else {
    vehicleViolationService.runRandomizedViolationFetchCDMX();
  }
};

export default initScheduleJobs;
