/* eslint-disable space-before-function-paren */
/* eslint-disable no-extend-native */
// eslint-disable-next-line prettier/prettier
export { };

type UserVendorReq = {
  userId: string;
  role: string;
  exp: number;
  iat: number;
  organizationId: string;
  companyId?: string;
  isAdminPlatform?: boolean | undefined;
};

declare global {
  interface Array<T> {
    /**
     * Encuentra el último índice que cumple con una condición dada en un array.
     * @template T El tipo de elementos en el array.
     * @param {function(T, number, T[]): boolean} callback Una función de callback que se ejecuta en cada elemento del array.
     * @returns {number} El índice del último elemento que cumple con la condición dada, o -1 si no se encuentra ningún elemento que coincida.
     */
    findLastIndexCustom(callback: (value: T, index: number, array: T[]) => boolean): number;
  }
  namespace Express {
    interface Request {
      userReq: {
        userId: string;
        role: string;
        exp: number;
        iat: number;
        email?: string;
        isAdminPlatform?: boolean;
      };
      userVendor: UserVendorReq;
    }
  }
}

Array.prototype.findLastIndexCustom = function <T>(
  callback: (value: T, index: number, array: T[]) => boolean
): number {
  for (let i = this.length - 1; i >= 0; i--) {
    if (callback(this[i], i, this)) {
      return i;
    }
  }
  return -1;
};
