// src/globals.ts
export interface TimeRange {
  start: string; // Format "HH:mm"
  end: string; // Format "HH:mm"
}

export interface WeeklySchedule {
  monday?: TimeRange;
  tuesday?: TimeRange;
  wednesday?: TimeRange;
  thursday?: TimeRange;
  friday?: TimeRange;
  saturday?: TimeRange;
  sunday?: TimeRange;
  [key: string]: TimeRange | undefined; // Añade esta línea
}

export interface WorkshopCapacity {
  totalBays: number;
  techniciansPerBay: number;
}

export interface ScheduleConfig {
  weeklySchedule: WeeklySchedule;
  appointmentDuration: number;
  maxSimultaneousAppointments: number;
  timezone: string;
  breakTime?: TimeRange;
  bufferTime?: number;
  capacity: WorkshopCapacity;
}
