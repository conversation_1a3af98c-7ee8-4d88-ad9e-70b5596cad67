import { Router } from 'express';
import {
  acceptInvitation,
  changeForgotPassword,
  loginUser,
  externalLoginUser,
  logout,
  recoverPassword,
  registerExternalUser,
  sendInvitation,
  // registerUser,
  validateTokens,
  googleLogin,
  googleLoginFailureCount,
  signInWithPhoneNumber,
  verifyOTP,
  signInWithPhoneNumberV2,
  verifyOTPV2,
} from '../controllers/auth';
import { verifyToken } from '../middlewares/verifyToken';
import { upload } from '../multer/multer';

const auth = Router();

// auth.post('/register', registerUser);
// Esta comentado y no eliminado por si se requiera.
auth.post('/google-login', googleLogin);
auth.post('/google-login-failure-count', googleLoginFailureCount);
auth.post('/login', loginUser);
auth.post('/logout', logout);
auth.post('/sendInvitation', verifyToken, sendInvitation);
auth.post('/acceptInvitation', upload.single('image'), acceptInvitation);
auth.get('/validateToken', validateTokens);
auth.post('/recoverPassword', recoverPassword);
auth.post('/changePassword', changeForgotPassword);
auth.post('/external/register', verifyToken, registerExternalUser);
auth.post('/external/login', externalLoginUser);

// Onboarding and OCN Mobile Application
auth.post('/sign-in', signInWithPhoneNumber);
auth.post('/verify-otp', verifyOTP);
// Version 2 for OCN Mobile Application backward compatibility
// v2 apis should be removed in future releases
auth.post('/sign-in-v2', signInWithPhoneNumberV2);
auth.post('/verify-otp-v2', verifyOTPV2);

export default auth;
