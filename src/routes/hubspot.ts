import { Router } from 'express';
import {
  createContact,
  createElectricContact,
  webhook,
  updateContact,
  noDocumentsTrigger,
  linkOpened,
  noContactTrigger,
  hilosRequestID,
} from '../controllers/hubspot';

const hubspot = Router();

hubspot.post('/createContact', createContact);
hubspot.post('/createElectricContact', createElectricContact);
hubspot.post('/webhook', webhook);
hubspot.post('/update-contact', updateContact);
hubspot.get('/no-documents-trigger', noDocumentsTrigger);
hubspot.get('/link-opened/:requestId', linkOpened);
hubspot.get('/no-contact-trigger', noContactTrigger);
hubspot.get('/hilos-request-id', hilosRequestID);

export default hubspot;
