import { Router } from 'express';
import {
  webhook,
  createBankAccount,
  createEasyAccount,
  soldPayment,
  getBankAccounts,
  getSpeiIncoming,
  oldGetSpeiIncoming,
  getPayments,
  getTheLast5Payments,
  findPaymentByReference,
  findPaymentByDescription,
  findPaymentByClaveRastreo,
  getAllMonexAccounts,
  updateClabeByCsv,
  getAllPayments,
  sendWhatsApp,
} from '../controllers/wire4';
import { upload } from '../multer/multer';
import { verifyToken } from '../middlewares/verifyToken';

const router = Router();

router.post('/webhook', webhook);
router.post('/createBankAccount', verifyToken, createBankAccount);
router.post('/createEasyAccount', verifyToken, createEasyAccount);
router.get('/sold-payment/:contract', verifyToken, soldPayment);
router.get('/get-bank-accounts/:email', verifyToken, getBankAccounts);
router.get('/get-spei-incoming/:beginDate/:endDate', verifyToken, getSpeiIncoming);
router.get('/old-get-spei-incoming/:beginDate/:endDate', verifyToken, oldGetSpeiIncoming);
router.get('/check-payments/:contrato', verifyToken, getPayments);
router.get('/get-the-last-5-payments/:contrato', verifyToken, getTheLast5Payments);
router.get('/find-payment-by-reference/:reference', verifyToken, findPaymentByReference);
router.get('/find-payment-by-concept/:description', verifyToken, findPaymentByDescription);
router.get('/find-payment-by-clave-rastreo/:claveRastreo', verifyToken, findPaymentByClaveRastreo);
router.get('/get-all-monex-accounts/:suscription', verifyToken, getAllMonexAccounts);
router.post('/update-clabe-by-csv', verifyToken, upload.single('csv'), updateClabeByCsv);
router.get('/get-all-payments', verifyToken, getAllPayments);
router.get('/send-whatsapp', verifyToken, sendWhatsApp);

export default router;
