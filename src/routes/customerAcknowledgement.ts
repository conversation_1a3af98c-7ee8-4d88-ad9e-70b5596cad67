import {
  createCustomerAcknowledgement,
  getCustomerAcknowledgementByAssociateId,
  updateStep,
} from '@/controllers/customerAcknowledgement';
import { Router } from 'express';

const router = Router();

router.post('/acknowledgement', createCustomerAcknowledgement);

router.get('/acknowledgement/request/:associateId', getCustomerAcknowledgementByAssociateId);

// Define REST API endpoints for each step
router.put('/acknowledgement/:id/read-benefits-offered', (req, res) => {
  updateStep(req, res, 'readBenfitsOffered');
});

router.put('/acknowledgement/:id/read-insurance-coverage', (req, res) => {
  updateStep(req, res, 'readInsuranceCoverage');
});

router.put('/acknowledgement/:id/read-maintenance', (req, res) => {
  updateStep(req, res, 'readMaintenance');
});

router.put('/acknowledgement/:id/read-term-conditions', (req, res) => {
  updateStep(req, res, 'readTermConditions');
});

router.put('/acknowledgement/:id/read-theft', (req, res) => {
  updateStep(req, res, 'readTheft');
});

router.put('/acknowledgement/:id/read-accident', (req, res) => {
  updateStep(req, res, 'readAccident');
});

router.put('/acknowledgement/:id/read-payments', (req, res) => {
  updateStep(req, res, 'readPayments');
});

router.put('/acknowledgement/:id/points-acknowledgement', (req, res) => {
  updateStep(req, res, 'pointsAcknowledement');
});

export default router;
