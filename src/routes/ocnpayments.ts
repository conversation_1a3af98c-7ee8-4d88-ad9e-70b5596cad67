import { Router } from 'express';
import * as controllers from '../controllers';
import { verifyToken } from '../middlewares/verifyToken';
import { updateStatusGigstackDeleted } from '../controllers/gigstack';

const payments = Router();

// payments.get('/gig/clients/list', controllers.getClientsList); // Uncomment when needed in local, and comment when deploying
// payments.get('/gig/recurring/list', controllers.getRecurrentPayments); // Uncomment when needed in local, and comment when deploying
payments.post('/gig/webhooks/rasayel/cdmx', controllers.gigstackWebhookCdmx);
payments.post('/gig/webhooks/rasayel/gdl', controllers.gigstackWebhookGdl);
payments.post('/gig/webhooks/rasayel/mty', controllers.gigstackWebhookMty);
payments.post('/gig/webhooks/rasayel/moka', controllers.gigstackWebhookMoka);
payments.post('/gig/webhooks/created/:regionCode', controllers.gigstackPaymentCreated);
payments.post('/gig/webhooks/update/:regionCode', controllers.updateStatusGigstack);
payments.post('/gig/webhooks/success', controllers.paymentHistoryGigstackSuccess);
payments.post('/gig/webhooks/cancel', controllers.cancelGigstack);
payments.post('/gig/webhooks/deleted', updateStatusGigstackDeleted);
// payments.post('/gig/webhooks/history', controllers.gigstackHistory);
payments.post('/wire4/add-transaction', controllers.addTransactionHistoryWire4);
payments.use(verifyToken);
payments.post('/gig/functions/firstPayment/:regionCode', controllers.firstAssociatePayment);
payments.post('/gig/functions/subscription/:regionCode', controllers.gigstackRecurrentPayment);
payments.get(
  '/gig/functions/simplifiedPendingPayments/:regionCode',
  controllers.getPendingPaymentsSimplified
);
payments.post('/gig/functions/createGigUser/:regionCode', controllers.createGigstackAssociate);
payments.post('/region/add-region', controllers.addRegion);
payments.post('/region/add-model/:regionCode', controllers.addModelRegions);
payments.post('/patches/existing-user-to-flow', controllers.existingAssociatePaymentFlow);
payments.post('/patches/massive_patch/:regionCode', controllers.massiveImplementationToUsers);
payments.post('/patches/massive_payments/:regionCode', controllers.paymentFixAssignation);
payments.post('/patches/massive_data', controllers.fixesDataAssociatePayments);
payments.post('/patches/clabe_asignator', controllers.fixesClabeMonexAssociatePayments);
payments.post('/patches/gig_asignator/:regionCode', controllers.gigClabeAssignator);
payments.post('/patches/monex_email', controllers.monexEmailFix);
payments.get('/patches/monex-account/', controllers.addMonexAccountToGig);
export default payments;
