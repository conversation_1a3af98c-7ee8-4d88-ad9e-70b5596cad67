import { Router } from 'express';
import { createCar, getAllCars, getCarByMake, updateCar, deleteCar } from '../controllers/car';
import { validateCar, handleValidationErrors } from '../middlewares/carValidator';

const router = Router();
// API Endpoints with validation
router.post('/cars', validateCar, handleValidationErrors, createCar); // Create a new car
router.get('/cars', getAllCars); // Get all cars
router.get('/cars/:make', getCarByMake); // Get a car by make
router.put('/cars/:make', validateCar, handleValidationErrors, updateCar); // Update a car by make
router.delete('/cars/:make', deleteCar); // Delete a car by make

export default router;
