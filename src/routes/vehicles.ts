import { /* NextFunction, Request, Response, */ Router } from 'express';
import {
  addCar,
  getCatalogVehicles,
  getCatalogVehiclesById,
  getPlatformCatalogVehicles,
  getPersonalCatalogVehicle,
  // keyMiddleware,
} from '../controllers/vehicles';
import { verifyToken } from '../middlewares/verifyToken';
import { upload } from '../multer/multer';

const router = Router();

// function maybe(fn: any) {
//   return function (req: Request, res: Response, next: NextFunction) {
//     if (req.method !== 'GET') {
//       next();
//     } else {
//       fn(req, res, next);
//     }
//   };
// }

// router.use(maybe(keyMiddleware));

router.get('/get', getCatalogVehicles);
router.get('/get/platform', getPlatformCatalogVehicles);
router.get('/get/personal', getPersonalCatalogVehicle);
router.get('/get/:id', getCatalogVehiclesById);

router.use(verifyToken);
// router.patch('/update/:id', updateCatalogVehicle);
router.post('/add', upload.array('images', 5), addCar);
// router.delete('/delete/:id', deleteCatalogVehicle);

export default router;
