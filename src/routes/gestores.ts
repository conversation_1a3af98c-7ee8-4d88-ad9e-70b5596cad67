import { Router } from 'express';
import {
  getAllGestores,
  getAllTramites,
  createProcedimiento,
  getProcedimientosByEmail,
  getGestoresByState,
  getProcedimientosByVehicleId,
} from '../controllers/gestores/gestores';
import { verifyToken } from '../middlewares/verifyToken';

const router = Router();

router.get('/', verifyToken, getAllGestores);
router.get('/:state', verifyToken, getGestoresByState);
router.get('/tramites', verifyToken, getAllTramites);
router.get('/procedimientos/vehicle/:id', verifyToken, getProcedimientosByVehicleId);
router.post('/tramites', verifyToken, createProcedimiento);
router.get('/procedimientos/:email', verifyToken, getProcedimientosByEmail);
export default router;
