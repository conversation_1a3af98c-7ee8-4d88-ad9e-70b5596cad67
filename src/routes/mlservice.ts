import {
  getHomevisitStackingclfScoring,
  getHomevisitVotingclfScoring,
} from '@/controllers/MLService/homevisit';
import { getPreApproval } from '@/controllers/MLService/preapproval';
import { getRiskScore } from '@/controllers/MLService/riskscoring';
import { Router } from 'express';

const router = Router();

router.get('/risk-scoring/:requestId', getRiskScore);
router.get('/homevisit/voting-clf/:requestId', getHomevisitVotingclfScoring);
router.get('/homevisit/stacking-clf/:requestId', getHomevisitStackingclfScoring);
router.get('/pre-approval/:requestId', getPreApproval);

export default router;
