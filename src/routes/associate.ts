import { Router } from 'express';
import { checkSchema } from 'express-validator';
import {
  getAllAssociate,
  getAssociate,
  removeActiveAssociate,
  unassignAssociate,
  updateAddressVerification,
  updateAssociateCurp,
  updateAssociateDriverLicense,
  updateAssociateIne,
  updateBankStatements,
  updateTaxStatus,
  getEmailInfo,
  updateSignedDocs,
  updateAssociateDataById,
  updateGarageImg,
  updateDeliveredImages,
  addAssociateContract,
  updateAssociatePicture,
  inactiveAssociate,
  getAllAssociates,
  addUserToPayFlow,
  getByMonexClabe,
  startPayFlowController,
  updateAssociateMonexClabe,
  updatePaymentServiceNewMonexData,
  getPaymentFlow,
  importAssociateFromAdmissionRequest,
  assignAssociate,
} from '../controllers/associate';
import { upload } from '../multer/multer';
import { associateUnassignValidator, associateUpdateValidation } from '../middlewares/associateValidator';
import associateUpdatingSchema from '../express-validator/associates/associatesUpdateValidation';
import unasignSchema from '../express-validator/associates/associatesUnassignValidation';
import * as fields from '../multer/fields/associate';
import { validateAssociateAssignment } from '@/middlewares/assignAssociateValidator';

const associate = Router();

associate.get('/getAssociate/:id', getAssociate);
associate.get('/getAllAssociates', getAllAssociate);

associate.post('/assignAssociate', validateAssociateAssignment, assignAssociate);

associate.patch('/update/picture/:id', upload.single('picture'), updateAssociatePicture);

associate.patch(
  '/update/ine/:id',
  upload.fields(fields.ineDocs),
  checkSchema(associateUpdatingSchema),
  associateUpdateValidation,
  updateAssociateIne
);

associate.patch(
  '/update/driverLicense/:id',
  upload.fields(fields.driverLicense),
  checkSchema(associateUpdatingSchema),
  associateUpdateValidation,
  updateAssociateDriverLicense
);

associate.patch(
  '/update/curp/:id',
  upload.single('curp'),
  checkSchema(associateUpdatingSchema),
  associateUpdateValidation,
  updateAssociateCurp
);
associate.patch(
  '/update/taxStatus/:id',
  upload.single('taxStatus'),
  checkSchema(associateUpdatingSchema),
  associateUpdateValidation,
  updateTaxStatus
);

associate.patch(
  '/update/addressVerification/:id',
  upload.single('addressVerification'),
  checkSchema(associateUpdatingSchema),
  associateUpdateValidation,
  updateAddressVerification
);

associate.patch(
  '/update/contract/:id',
  upload.fields(fields.contractDocs),
  checkSchema(associateUpdatingSchema),
  associateUpdateValidation,
  addAssociateContract
);

associate.patch(
  '/update/updateBankStatements/:id',
  upload.fields(fields.bankStatementsDocs),
  checkSchema(associateUpdatingSchema),
  associateUpdateValidation,
  updateBankStatements
);

associate.patch('/update/garage/:id', upload.single('garage'), updateGarageImg);

associate.patch('/update/deliveredImages/:id', upload.any(), updateDeliveredImages);

associate.patch('/update/signedDocs/:id', upload.fields(fields.contractDocs), updateSignedDocs);
associate.patch('/update/associateData/:id', upload.any(), updateAssociateDataById);

associate.post('/start-payment-flow', startPayFlowController);
associate.get('/get-payment-flow/:id', getPaymentFlow);
associate.post(
  '/unnasign/:associateId/:vehicleId',
  checkSchema(unasignSchema),
  associateUnassignValidator,
  unassignAssociate
);

associate.post('/removeActive', removeActiveAssociate);
associate.get('/email/:email', getEmailInfo);
associate.get('/inactive', inactiveAssociate);
associate.get('/get-all', getAllAssociates);
associate.post('/add-user-to-paymet-flow', addUserToPayFlow);
associate.get('/clabe/:monexClabe', getByMonexClabe);
associate.get('/update-monex-clabe', updateAssociateMonexClabe);
associate.get('/update-payment-service', updatePaymentServiceNewMonexData);
associate.post('/import-associate', upload.single('avalINE'), importAssociateFromAdmissionRequest);

// associate.get('/')

// esto de abajo es solo para desarrollo por si se llegará a necesitar

// associate.delete('/deleteAll', deleteAllAssociate);
// associate.delete('/deleteSelected', deleteSelectedAssociates);

export default associate;
