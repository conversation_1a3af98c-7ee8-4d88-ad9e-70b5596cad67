const updateDescSchema = {
  model: {
    notEmpty: false,
    errorMessage: 'model no puede estar vacio',
  },
  brand: {
    notEmpty: false,
    errorMessage: 'brand no puede estar vacio',
  },
  version: {
    notEmpty: false,
    errorMessage: 'version no puede estar vacio',
  },
  vin: {
    notEmpty: false,
    errorMessage: 'vin no puede estar vacio',
  },
  vehicleState: {
    notEmpty: false,
    errorMessage: 'vehicleState no puede estar vacio',
  },
  owner: {
    notEmpty: false,
    errorMessage: 'owner no puede estar vacio',
  },
  billAmount: {
    notEmpty: false,
    errorMessage: 'billAmount no puede estar vacio',
  },
  km: {
    notEmpty: false,
    errorMessage: 'km no puede estar vacio',
  },
  region: {
    notEmpty: false,
    errorMessage: 'region no puede estar vacio',
  },
  year: {
    notEmpty: false,
    errorMessage: 'year no puede estar vacio',
  },
  historyData: {
    exists: {
      checkFalsy: true,
      errorMessage: 'historyData es requerido para el historial de actualizaciones',
    },
    isObject: {
      errorMessage: 'historyData debe ser un objeto con propiedades',
      bail: true,
    },
  },
  'historyData.step': {
    notEmpty: true,
    errorMessage: 'step no puede estar vacio',
  },
  'historyData.description': {
    notEmpty: false,
    errorMessage: 'description no puede estar vacio',
  },
  'historyData.userId': {
    notEmpty: true,
    errorMessage: 'userId no puede estar vacio',
  },
};

export default updateDescSchema;
