import { associateText } from '../../constants';
import Associate from '../../models/associateSchema';

const associateUpdatingSchema = {
  assocciateId: {
    custom: {
      options: async (value: string) => {
        if (!value) {
          return Promise.reject(associateText.errors.associateNotFound);
        }
        const associate = await Associate.findById(value);
        if (!associate) {
          return Promise.reject(associateText.errors.associateNotFound);
        }
        return null;
      },
    },
  },
};

export default associateUpdatingSchema;
