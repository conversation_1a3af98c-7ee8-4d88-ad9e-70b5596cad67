import { CountriesEnum } from '../../constants';

const postStockSchema = {
  model: {
    notEmpty: true,
    errorMessage: 'model no puede estar vacio',
  },
  brand: {
    notEmpty: true,
    errorMessage: 'brand no puede estar vacio',
  },
  version: {
    notEmpty: true,
    errorMessage: 'version no puede estar vacio',
  },
  vin: {
    notEmpty: true,
    errorMessage: 'vin no puede estar vacio',
  },
  vehicleState: {
    notEmpty: true,
    errorMessage: 'vehicleState no puede estar vacio',
  },
  owner: {
    notEmpty: true,
    errorMessage: 'owner no puede estar vacio',
  },
  billAmount: {
    notEmpty: true,
    errorMessage: 'billAmount no puede estar vacio',
  },
  km: {
    notEmpty: false,
    errorMessage: 'km no puede estar vacio',
    custom: {
      options: async (value: string, { req }: any) => {
        const country = req.body.country;
        if (country === CountriesEnum.Mexico && !value) {
          return Promise.reject('Kilometers is required when country is Mexico.');
        }
        return null;
      },
    },
  },
  region: {
    notEmpty: true,
    errorMessage: 'region no puede estar vacio',
  },
  year: {
    notEmpty: true,
    errorMessage: 'year no puede estar vacio',
  },

  country: {
    notEmpty: true,
    errorMessage: 'country is required',
  },
  // this state is only for USA,
  state: {
    errorMessage: 'state is required when country is USA.',
    isLength: { options: { min: 0, max: 100 } },
  },

  mi: {
    notEmpty: false,
    errorMessage: 'Miles is required',
    custom: {
      options: async (value: string, { req }: any) => {
        const country = req.body.country;
        if (country === CountriesEnum['United States'] && !value) {
          return Promise.reject('Miles is required when country is United States.');
        }
        return null;
      },
    },
  },
};

export default postStockSchema;
