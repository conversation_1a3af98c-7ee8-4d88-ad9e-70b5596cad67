const stockPatchSchema = {
  historyData: {
    exists: {
      checkFalsy: true,
      errorMessage: 'historyData es requerido para el historial de actualizaciones',
    },
    isObject: {
      errorMessage: 'historyData debe ser un objeto con propiedades',
      bail: true,
    },
  },
  'historyData.step': {
    notEmpty: true,
    errorMessage: 'step no puede estar vacio',
  },
  'historyData.description': {
    notEmpty: true,
    errorMessage: 'description no puede estar vacio',
  },
  'historyData.userId': {
    notEmpty: true,
    errorMessage: 'userId no puede estar vacio',
  },
};
export default stockPatchSchema;
