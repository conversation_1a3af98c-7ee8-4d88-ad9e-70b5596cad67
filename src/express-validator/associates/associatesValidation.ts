import { associateText } from '../../constants';
import StockVehicle from '../../models/StockVehicleSchema';
import Associate from '../../models/associateSchema';

const registrationSchema = {
  firstName: {
    notEmpty: true,
    errorMessage: 'firstName no puede estar vacio',
  },
  lastName: {
    notEmpty: true,
    errorMessage: 'lastName no puede estar vacio',
  },
  email: {
    isEmail: true,
    notEmpty: true,
    errorMessage: 'Email invalido o vacio',
    custom: {
      options: (value: string) => {
        return Associate.findOne({ email: value }).then((user) => {
          if (user?.email) {
            return Promise.reject('Email ya esta en uso');
          }
          return null;
        });
      },
    },
  },
  birthDay: {
    notEmpty: true,
    errorMessage: 'birthDay no puede estar vacio',
  },
  phone: {
    notEmpty: true,
    errorMessage: 'Phone no puede estar vacio',
  },
  addressStreet: {
    notEmpty: true,
    errorMessage: 'addressStreet no puede estar vacio',
  },
  postalCode: {
    notEmpty: true,
    errorMessage: 'postalCode no puede estar vacio',
  },
  city: {
    notEmpty: true,
    errorMessage: 'city no puede estar vacio',
  },
  state: {
    notEmpty: true,
    errorMessage: 'state no puede estar vacio',
  },
  vehicleId: {
    notEmpty: false,
    custom: {
      options: (value: string) => {
        return StockVehicle.findById(value).then((vehicle) => {
          if (!vehicle) {
            return Promise.reject(associateText.errors.vehicleNotFound);
          } /* else if (vehicle.status === 'Activo') {
            return Promise.reject(associateText.errors.vehicleActive);
          }*/
          return null;
        });
      },
    },
  },
  country: {
    notEmpty: true,
    errorMessage: 'Country is required',
  },
};

export const registrationSchemaMX = {
  ...registrationSchema,
  exterior: {
    notEmpty: true,
    errorMessage: 'exterior no puede estar vacio',
  },
  curp: {
    notEmpty: true,
    errorMessage: 'curp no puede estar vacio',
  },
  rfc: {
    notEmpty: true,
    errorMessage: 'rfc no puede estar vacio',
  },
  colony: {
    notEmpty: true,
    errorMessage: 'colony no puede estar vacio',
  },
  delegation: {
    notEmpty: true,
    errorMessage: 'delegation no puede estar vacio',
  },
};

export const registrationSchemaUS = {
  ...registrationSchema,
  ssn: {
    notEmpty: true,
    errorMessage: 'SSN is required',
  },
  rideShareTotalRides: {
    notEmpty: true,
    errorMessage: 'rideShareTotalRides is required',
  },
  avgEarningPerWeek: {
    notEmpty: true,
    errorMessage: 'avgEarningPerWeek is required',
  },
  mobilityPlatforms: {
    notEmpty: true,
    errorMessage: 'mobilityPlatforms is required',
  },
  emergencyContactName: {
    notEmpty: true,
    errorMessage: 'emergencyContactName is required',
  },
  emergencyContactPhone: {
    notEmpty: true,
    errorMessage: 'emergencyContactPhone is required',
  },
  emergencyContactRelation: {
    notEmpty: true,
    errorMessage: 'emergencyContactRelation is required',
  },
};
