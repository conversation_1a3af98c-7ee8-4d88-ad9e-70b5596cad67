import { associatePaymentsConsts } from '../../constants';
import MainContractSchema from '../../models/mainContractSchema';

const tableRegister = {
  contractId: {
    notEmpty: true,
    errorMessage: 'Se requiere el mainContractId',
    custom: {
      options: async (value: string) => {
        const contract = await MainContractSchema.findById(value);
        if (!contract) {
          return Promise.reject(associatePaymentsConsts.errors.mainContract404);
        }
        return null;
      },
    },
  },
};

export default tableRegister;
