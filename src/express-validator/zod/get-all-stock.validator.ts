import { z } from 'zod';
import { VehicleState } from '../../models/StockVehicleSchema';

const CityEnum = z.nativeEnum(VehicleState);

export const getStockQuerySchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => parseInt(val || '0')),
  limit: z
    .string()
    .optional()
    .transform((val) => parseInt(val || '0')),
  status: z.string().optional(),
  vehicleStatus: z.string().optional(),
  category: z.string().optional(),
  subCategory: z.string().optional(),
  listStatus: z.string().optional(),
  city: CityEnum.optional(), // Usar CityEnum para validar el parámetro city
  stepNumber: z
    .string()
    .optional()
    .transform((val) => parseInt(val || '0')),
  isNew: z.string().optional(),
  excludeStatus: z.string().optional(),
  country: z.string().optional(),
  isElectric: z.string().optional(),
  platform: z.string().optional(),
  reason: z.string().optional(),
  // .transform((val) => val === 'true'),
});
