import mongoose from 'mongoose';
import { MONGODB_URI } from '../constants';
import { MongoMemoryServer } from 'mongodb-memory-server';

export const connectDB = async () => {
  try {
    mongoose.set('strictQuery', true);
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (err) {
    console.log('Hubo un error');
    throw err;
  }
};

let mongoServer: MongoMemoryServer | null = null;

export const connectTestDB = async () => {
  try {
    mongoServer = new MongoMemoryServer();
    await mongoServer.ensureInstance();
    const mongoUri = mongoServer.getUri();
    mongoose.set('strictQuery', true);
    await mongoose.connect(mongoUri);
    console.log('Conectado a la base de datos de PRUEBAS');
  } catch (error) {
    console.log('Error al conectarse a la base de datos de PRUEBAS', error);
  }
};

export const disconnectTestDB = async () => {
  try {
    await mongoose.connection.close();
    console.log('Desconexión exitosa de la base de datos de PRUEBAS');
  } catch (error) {
    console.log('Error al desconectarse de la base de datos de PRUEBAS:', error);
  }
};
