import { logger } from '@/clean/lib/logger';
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

const root = process.cwd();
dotenv.config({ path: `${root}/.env.${process.env.NODE_ENV || 'development'}` });

export const CAMPAIGN_DB_URL = process.env.CAMPAIGN_DB_URL!;
export const CAMPAIGN_DB_ANON_PUBLIC_KEY = process.env.CAMPAIGN_DB_ANON_PUBLIC_KEY!;
export const NODE_ENV = process.env.NODE_ENV!;

export const supabase = createClient(CAMPAIGN_DB_URL, CAMPAIGN_DB_ANON_PUBLIC_KEY);

export const saveCampaignTracking = async (sourceOption: string, requestId: string) => {
  if (!CAMPAIGN_DB_URL || !CAMPAIGN_DB_ANON_PUBLIC_KEY) return; // Skip if no Supabase URL or Key, e.g. in local dev

  const res = await supabase
    .from('campaign_tracking')
    .insert([{ sourceOption, requestId, environment: NODE_ENV || 'development' }])
    .select();
  if (res.error) {
    logger.error('[saveSourceOption] Error: ' + JSON.stringify(res.error));
  }
  logger.info('[saveSourceOption] Data: ' + JSON.stringify(res.data));
};
