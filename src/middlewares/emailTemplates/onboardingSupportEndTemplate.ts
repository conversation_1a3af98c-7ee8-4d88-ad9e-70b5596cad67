export type OnboardingSupportEndTemplateProps = {
  url: string;
};

export default function onboardingSupportEndTemplate({ url }: OnboardingSupportEndTemplateProps) {
  const year = new Date().getFullYear();
  return `<!DOCTYPE html>
<html lang="es">
   <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Onboarding Completo / Onboarding Complete</title>
      <style>
         body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #344054;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            text-align: center;
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 150px;
            height: 32px;
        }
        h1 {
            color: #344054;
            font-size: 36px;
            margin-bottom: 20px;
        }
        p {
            margin-bottom: 15px;
            font-size: 16px;
            color: #344054;
        }
        ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }
        ul li {
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 20px;
            font-size: 16px;
            color: #344054;
        }
         .btn {
         width: 164px;
         height: 44px;
         padding-top: 10px;
         padding-right: 18px;
         padding-bottom: 10px;
         padding-left: 18px;
         gap: 8px;
         border-radius: 8px;
         border-width: 1px;
         background-color: #7F56D9;
         border-color: #7F56D9;
         color: #ffffff;
         margin: 16px;
         }
         .email-footer {
            background-color: #6600FA;
            color: #ffffff;
            text-align: center;
            height: 24px;
            line-height: 24px;
            font-size: 12px;
            margin-top: 20px;
         }
      </style>
   </head>
   <body>
      <div class="container">
         <div class="logo">
            <img src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp" alt="OneCarNow Logo">
         </div>
         <h1>🎉 ¡Gracias por completar tu sesión de onboarding! 🎉</h1>
         <p>Puedes acceder a la información compartida sobre tu vehículo en cualquier momento durante el período de tu contrato. Solo haz clic en el siguiente enlace:</p>
         <p>🔗 <a href="${url}" target="_blank">Onboarding Link</a></p>
         <p>Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos. 🚗</p>
         <div class="footer">
            <p>Saludos cordiales,<br>Equipo OCN</p>
         </div>
         <div class="email-footer">
         © Copyright ${year} OCN
         </div>
      </div>
   </body>
</html>`;
}
