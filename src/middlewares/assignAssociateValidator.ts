import { Request, Response, NextFunction } from 'express';
import { logger } from '@/clean/lib/logger';
import { associateText, genericMessages } from '@/constants';
import { Country } from '@/clean/domain/enums';

export const validateAssociateAssignment = (
  req: Request,
  res: Response,
  next: NextFunction
): Response | void => {
  logger.info('[validateAssociateAssignment] Validating associate assignment request');
  const { personalData, documentsAnalysis, avalData, vehicleId, userId, requestId } = req.body;

  // Check required objects exist
  if (!personalData) {
    logger.warn('[validateAssociateAssignment] Missing personal data in request');
    return res.status(400).send({ message: associateText.errors.personalDataMissing });
  }

  if (personalData?.country?.toLowerCase() === Country.mx && !avalData) {
    logger.warn('[validateAssociateAssignment] Missing aval data in request');
    return res.status(400).send({ message: associateText.errors.avalDataMissing });
  }

  if (!documentsAnalysis) {
    logger.warn('[validateAssociateAssignment] Missing documents analysis in request');
    return res.status(400).send({ message: associateText.errors.missingDocs });
  }

  // Check required IDs exist
  if (!vehicleId) {
    logger.warn('[validateAssociateAssignment] Missing vehicle ID in request');
    return res.status(400).send({ message: associateText.errors.vehicleNotFound });
  }

  if (!userId) {
    logger.warn('[validateAssociateAssignment] Missing user ID in request');
    return res.status(400).send({ message: genericMessages.errors.missingId });
  }

  if (!requestId) {
    logger.warn('[validateAssociateAssignment] Missing admission request ID in api request');
    return res.status(400).send({ message: associateText.errors.missingRequestId });
  }

  // Validate personal data fields
  const requiredFields = [
    'firstName',
    'lastName',
    'email',
    'birthdate',
    'phone',
    'street',
    'streetNumber',
    'postalCode',
    'city',
    'state',
    'country',
  ];

  const missingFields = requiredFields.filter((field) => !personalData[field]);
  if (missingFields?.length > 0) {
    logger.warn('[validateAssociateAssignment] Missing required personal data fields', { missingFields });
    return res.status(400).send({
      message: `${associateText.errors.missingFieldsPersonalData} ${missingFields?.join(', ')}. ${
        associateText.errors.provideFieldsPersonalData
      }`,
    });
  }

  // Validate country
  if (!Object.values(Country).includes(personalData?.country?.toLowerCase())) {
    logger.warn('[validateAssociateAssignment] Invalid country provided', { country: personalData.country });
    return res.status(400).send({
      message: associateText.errors.invalidCountry(personalData.country),
    });
  }

  // Additional validation for Mexico
  if (personalData?.country?.toLowerCase() === Country.mx) {
    const mexicoRequiredFields = ['nationalId', 'taxId', 'municipality', 'neighborhood'];
    const missingMexicoFields = mexicoRequiredFields.filter((field) => !personalData[field]);
    if (missingMexicoFields.length > 0) {
      logger.warn('[validateAssociateAssignment] Missing required Mexico-specific fields', {
        missingMexicoFields,
      });
      return res.status(400).send({
        message: `${associateText.errors.missingFields} '${missingMexicoFields?.join(', ')}'. ${
          associateText.errors.provideFields
        }`,
      });
    }
  }

  return next();
};
