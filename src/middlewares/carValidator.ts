import { body, validationResult } from 'express-validator';

// Validation rules for creating/updating a car
export const validateCar = [
  body('make').notEmpty().withMessage('Make is required'),
  body('models').isArray().withMessage('Models must be an array'),
  body('models.*.name').notEmpty().withMessage('Model name is required'),
  body('models.*.variations').isArray().withMessage('Variations must be an array'),
  body('models.*.variations.*.version').notEmpty().withMessage('Version is required'),
  body('models.*.variations.*.year').isNumeric().withMessage('Year must be a number'),
];

// Middleware to handle validation errors
export const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
