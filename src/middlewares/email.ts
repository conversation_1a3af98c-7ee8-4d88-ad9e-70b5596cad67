import { createTransport } from 'nodemailer';
import { emailSender, emailSenderPassword } from '../constants';
import cvTemplate from './emailTemplates/cvTemplate';
import forgotPassTemplate from './emailTemplates/forgotPass';
import invitationTemplate from './emailTemplates/invitationTemplate';
import gestorNotificationTemplate from './emailTemplates/gestorNotificationTemplate';

// momentaneamente use mi cuenta
const transporter = createTransport({
  host: 'smtp.gmail.com',
  port: 465,
  secure: true, // true for 465, false for other ports
  auth: {
    user: emailSender,
    pass: emailSenderPassword,
  },
});

type VerificationEmailProps = {
  email: string;
  user?: string;
  token: string;
  baseUrl: string;
};

type SendEmailFunction = ({ email, user, token, baseUrl }: VerificationEmailProps) => Promise<void>;

export const sendVerificationEmail: SendEmailFunction = async ({ email, token, baseUrl }) => {
  const emailHtml = forgotPassTemplate({ token, baseUrl });

  await transporter.sendMail({
    from: emailSender, // sender address
    to: email, // list of receivers
    subject: 'Recuperar contraseña Panel de Administrador OCN', // Subject line
    text: 'Recupera tu contraseña!', // plain text body
    html: emailHtml,
  });
};

type InvitationByEmail = VerificationEmailProps & {
  role?: string;
  name?: string;
  message?: string;
  title?: string;
  subject?: string;
  fullUrl?: string;
};

type SendInvitation = ({ email, token, baseUrl, name }: InvitationByEmail) => Promise<void>;

export const sendInvitationEmail: SendInvitation = async ({
  email,
  token,
  baseUrl,
  name,
  message,
  ...rest
}) => {
  const emailHtml = invitationTemplate({ token, baseUrl, name, message, fullUrl: rest.fullUrl });
  await transporter.sendMail({
    from: emailSender, // sender address
    to: email, // list of receivers
    subject: rest.subject || 'Has sido invitado al panel de administración', // Subject line
    text: rest.title || 'Invitación al panel de administrador', // plain text
    html: emailHtml,
  });
};

type SendCVProps = {
  name: string;
  email: string;
  birthday: string;
  cv: Express.Multer.File;
  country: string;
  state: string;
  city: string;
};

type SendCVFunc = ({ name, email, birthday, cv, country, state, city }: SendCVProps) => Promise<void>;

export const sendCV: SendCVFunc = async ({ name, email, birthday, cv, country, state, city }) => {
  const html = cvTemplate({ name, email, birthday, country, state, city, fileName: cv.originalname });

  await transporter.sendMail({
    from: emailSender, // sender address
    to: '<EMAIL>', // list of receivers
    subject: `Solicitud de ${name.toUpperCase()}`, // Subject line
    text: 'Solicitud de correo electronico', // plain text body
    html,
    attachments: [
      {
        filename: cv.originalname,
        path: cv.path,
        contentType: cv.mimetype,
      },
    ],
  });
};

type GestorNotificationProps = {
  gestorName: string;
  gestorEmail: string;
  tramiteName: string;
  vehicleInfo?: string;
  procedimientoId: string;
  cost?: number;
  duration?: number;
  status?: string;
};

type SendGestorNotificationFunc = (props: GestorNotificationProps) => Promise<void>;

export const sendGestorNotification: SendGestorNotificationFunc = async ({
  gestorName,
  gestorEmail,
  tramiteName,
  vehicleInfo,
  procedimientoId,
  cost,
  duration,
  status,
}) => {
  const html = gestorNotificationTemplate({
    gestorName,
    tramiteName,
    vehicleInfo,
    procedimientoId,
    cost,
    duration,
    status,
  });

  await transporter.sendMail({
    from: emailSender, // sender address
    to: gestorEmail, // list of receivers
    subject: `Nuevo Procedimiento Asignado: ${tramiteName}`, // Subject line
    text: `Nuevo procedimiento asignado: ${tramiteName}`, // plain text body
    html,
  });
};
