import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import UserVendorModel from '@/vendor-platform/modules/users/models/user.model';
import { invitationSecret } from '@/constants';

export class VendorAuthService {
  async findUserByEmail(email: string) {
    return UserVendorModel.findOne({ email }).select('+password');
  }

  async validatePassword(inputPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(inputPassword, hashedPassword);
  }

  generateAccessToken(
    payload: { email: string; userId: string; role: string; organizationId: string },
    secret: string = invitationSecret
  ) {
    const expiresIn = 10 * 60 * 60 * 1000; // 10 hours
    // const expiresIn = 1 * 60 * 1000; // 2 minutes
    const token = jwt.sign(payload, secret, { expiresIn: '10h' });
    const expires = new Date().getTime() + expiresIn;

    return { token, expires };
  }

  verifyToken(token: string): any {
    try {
      return jwt.verify(token, invitationSecret);
    } catch (error) {
      return null;
    }
  }

  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 10);
  }

  async updatePassword(email: string, newPassword: string): Promise<boolean> {
    const user = await UserVendorModel.findOne({ email });
    if (!user) return false;

    user.password = await this.hashPassword(newPassword);
    await user.save();
    return true;
  }
}
