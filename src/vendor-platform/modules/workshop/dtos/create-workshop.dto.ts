import z from 'zod';

export const createWorkshopDto = z.object({
  name: z.string().trim().min(3),
  organizationId: z.string().trim(),
  address: z
    .object({
      street: z.string().trim().optional(),
      city: z.string().trim().optional(),
      state: z.string().trim().optional(),
      zip: z.string().trim().optional(),
    })
    .optional(),
  phone: z.string().trim().optional(),
  country: z.string().trim().optional(),
  // date: z.string(),
  // duration: z.number(),
});

export type CreateWorkshopDtoType = z.infer<typeof createWorkshopDto>;
