export const brandssNotAllowed = ['CHIREY'];

interface MaintenanceRule {
  km: number;
  serviceType: 'preventivo menor' | 'preventivo mayor' | 'BYD';
  includesSparkPlugs: boolean;
  maintenanceNumber: number;
}

interface MaintenanceCase {
  firstMaintenanceKm?: number;
  firstYear: {
    maxKm: number;
    intervals: MaintenanceRule[];
  };
  secondYear: {
    minKm: number;
    intervals: MaintenanceRule[];
  };
}

export const MAINTENANCE_RULES: {
  [key: string]: MaintenanceCase;
} = {
  MG: {
    firstYear: {
      maxKm: 70000,
      // intervals: [
      //   { km: 10000, serviceType: 'preventivo menor', includesSparkPlugs: false },
      //   { km: 20000, serviceType: 'preventivo mayor', includesSparkPlugs: true },
      //   { km: 30000, serviceType: 'preventivo menor', includesSparkPlugs: false },
      //   { km: 40000, serviceType: 'preventivo mayor', includesSparkPlugs: true },
      //   { km: 50000, serviceType: 'preventivo menor', includesSparkPlugs: false },
      //   { km: 60000, serviceType: 'preventivo mayor', includesSparkPlugs: true },
      //   { km: 70000, serviceType: 'preventivo menor', includesSparkPlugs: false },
      //   // ... agregar todos los puntos del primer año
      // ],
      intervals: Array.from({ length: 7 }, (_, i) => ({
        km: (i + 1) * 10000, // 10000, 20000, 30000, 40000, 50000, 60000, 70000
        serviceType: i % 2 === 0 ? 'preventivo menor' : 'preventivo mayor',
        includesSparkPlugs: i % 2 === 1,
        maintenanceNumber: i + 1,
      })),
    },
    secondYear: {
      minKm: 85000,
      // intervals: [
      //   { km: 85000, serviceType: 'preventivo mayor', includesSparkPlugs: true },
      //   { km: 100000, serviceType: 'preventivo menor', includesSparkPlugs: false },
      //   { km: 115000, serviceType: 'preventivo mayor', includesSparkPlugs: true },
      //   // ... agregar todos los puntos del segundo año
      // ],
      intervals: Array.from({ length: 25 }, (_, i) => ({
        km: 85000 + i * 15000, // 85000, 100000, 115000, 130000, 145000, ...
        serviceType: i % 2 === 0 ? 'preventivo mayor' : 'preventivo menor', // mayor, menor, mayor, menor, ...
        includesSparkPlugs: i % 2 === 0,
        maintenanceNumber: 7 + i + 1, // 7 of previous year + 1 of current year
      })),
    },
  },
  BYD: {
    // first maintainance should start at 5,000 km and then every 20,000 km, so the intervals should be
    // 5,000km, 25,000km, 45,000km, 65,000km, 85,000km, 105,000km, 125,000km, 145,000km, 165,000km, 185,000km, 205,000km, 225,000km,
    firstMaintenanceKm: 5000,
    firstYear: {
      maxKm: 225000,
      intervals: Array.from({ length: 12 }, (_, i) => ({
        km: 5000 + i * 20000, // 5,000, 25,000, 45,000, 65,000, 85,000, 105,000, 125,000, 145,000, 165,000, 185,000, 205,000, 225,000
        serviceType: 'BYD', // all are 'preventivo mayor'
        // includesSparkPlugs: true, // all include spark plugs
        includesSparkPlugs: i % 2 === 0, // true, false, true, false, true, false, true, false, true, false, true, false, ...
        maintenanceNumber: i + 1,
      })),
    },
    secondYear: {
      minKm: 225000,
      intervals: Array.from({ length: 12 }, (_, i) => ({
        km: 225000 + i * 20000, // 225,000, 245,000, 265,000, 285,000, 305,000, 325,000, 345,000, 365,000, 385,000, 405,000, 425,000
        serviceType: 'BYD', // all are 'preventivo mayor'
        // includesSparkPlugs: true, // all include spark plugs
        includesSparkPlugs: i % 2 === 0, // true, false, true, false, true, false, true, false, true, false, true, false, ...
        maintenanceNumber: 12 + i + 1, // 12 of previous year + 1 of current year
      })),
    },
  },
  OTHERS: {
    // Estructura similar para otras marcas
    firstYear: {
      maxKm: 135000,
      // intervals: [
      //   { km: 15000, serviceType: 'preventivo menor', includesSparkPlugs: false },
      //   { km: 30000, serviceType: 'preventivo mayor', includesSparkPlugs: true },
      //   // ... agregar todos los puntos del primer año
      // ],
      intervals: Array.from({ length: 9 }, (_, i) => ({
        km: (i + 1) * 15000, // 15000, 30000, 45000, 60000, 75000, 90000, 105000, 120000, 135000
        // serviceType should be like this: 1.- preventivo menor, 2.- preventivo mayor, 3.- preventivo menor, 4.- preventivo menor, 5.- preventivo mayor, 6.- preventivo menor, 7.- preventivo menor, 8.- preventivo mayor, 9.- preventivo menor, 10.- preventivo menor
        // as you can see, the serviceType is not alternating, it is a pattern of 2 preventivo menor and 1 preventivo mayor, but the first one is preventivo menor and the second one is preventivo mayor
        serviceType: i % 3 === 0 ? 'preventivo menor' : 'preventivo mayor', // menor, mayor, menor, menor, mayor, menor, menor, mayor, menor, menor, ...
        includesSparkPlugs: i % 3 === 1, // false, true, false, false, true, false, false, true, false, false, ...
        maintenanceNumber: i + 1,
      })),
    },
    secondYear: {
      minKm: 150000,
      // intervals: [
      //   { km: 150000, serviceType: 'preventivo mayor', includesSparkPlugs: true },
      //   { km: 165000, serviceType: 'preventivo mayor', includesSparkPlugs: true },
      //   // ... agregar todos los puntos del segundo año
      // ],
      intervals: Array.from({ length: 10 }, (_, i) => ({
        km: 150000 + i * 15000, // 150000, 165000, 180000, 195000, ...
        // serviceType: 'preventivo mayor',
        // includesSparkPlugs: true,
        // the same as above, the serviceType is not alternating, it is a pattern of 2 preventivo mayor
        serviceType: i % 3 === 0 ? 'preventivo menor' : 'preventivo mayor', // menor, mayor, menor, menor, mayor, menor, menor, mayor, menor, menor, ...
        includesSparkPlugs: i % 3 === 1, // false, true, false, false, true, false, false, true, false, false, ...
        maintenanceNumber: 9 + i + 1, // 9 of previous year + 1 of current year
      })),
    },
  },
};
