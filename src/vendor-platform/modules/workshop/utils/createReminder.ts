import { schedulerClient } from '@/aws/scheduler-client';
import { CreateScheduleCommand } from '@aws-sdk/client-scheduler';
import { DateTime } from 'luxon';

export async function scheduleAppointmentReminder(
  data: {
    name: string;
    phone: string;
    mapsLink: string;
    eventDate: string;
    workshopName: string;
  },
  timezone: string
) {
  const { name, phone, mapsLink, eventDate, workshopName } = data;

  // Convertir la fecha a UTC y restar 1 hora
  const reminderDateTime = DateTime.fromISO(eventDate).setZone(timezone).minus({ hours: 1 });

  if (!reminderDateTime.isValid) {
    throw new Error('Error al convertir la fecha.');
  }
  // const reminderDateTimeUTC = reminderDateTime.toUTC();
  // Crear el nombre del schedule (único)
  const scheduleName = `reminder-${Date.now()}-${name.replace(/\s/g, '-')}`;

  try {
    const ScheduleExpression = `at(${reminderDateTime.toFormat("yyyy-MM-dd'T'HH:mm:ss")})`;
    console.log('ScheduleExpression', ScheduleExpression);
    // Crear el schedule
    const createScheduleCommand = new CreateScheduleCommand({
      Name: scheduleName,
      ScheduleExpression, // Fecha y hora en UTC
      // ScheduleExpression: `at(${eventDate})`,
      ScheduleExpressionTimezone: timezone, // Zona horaria del evento
      Target: {
        Arn: 'arn:aws:lambda:us-east-1:564033404206:function:vendors-appointment-reminder', // ARN de la Lambda
        RoleArn: 'arn:aws:iam::564033404206:role/schedule-appointment-reminders',
        Input: JSON.stringify({
          name,
          phone,
          mapsLink,
          date: DateTime.fromISO(eventDate).setZone(timezone).toFormat("dd 'de' LLLL 'de' yyyy"),
          time: DateTime.fromISO(eventDate).setZone(timezone).toFormat('hh:mm a'),
          workshopName,
        }),
      },
      FlexibleTimeWindow: {
        Mode: 'OFF', // Sin ventana flexible (ejecución exacta)
      },
      ActionAfterCompletion: 'DELETE', // Eliminar el schedule después de la ejecución
    });

    await schedulerClient.send(createScheduleCommand);

    console.log(`Recordatorio programado para ${eventDate} (${timezone})`);
  } catch (error) {
    console.error('Error al programar el recordatorio:', error);
  }
}

// Ejemplo de uso
// scheduleReminder(
//   {
//     name: 'Testing 3',
//     phone: '3333333333',
//     mapsLink:
//       'https://www.google.com/maps/place/Plaza+Central/@19.3767131,-99.1172331,15z/data=!3m1!5s0x85d1fdd0adc1d6bf:0x2b7f348b5fe5d9a7!4m15!1m8!3m7!1s0x85ce0026db097507:0x54061076265ee841!2sCiudad+de+M%C3%A9xico,+CDMX!3b1!8m2!3d19.4326077!4d-99.133208!16zL20vMDRzcWo!3m5!1s0x85d1fd72f7bcc815:0x34fb1ca1f5a40097!8m2!3d19.3767038!4d-99.0956561!16s%2Fg%2F11gnr_nx61?entry=ttu&g_ep=EgoyMDI1MDIwNS4xIKXMDSoASAFQAw%3D%3D',
//     eventDate: '2025-02-12T10:30:00',
//     workshopName: 'Taller de programación',
//   },
//   'America/Mexico_City'
// ).catch(console.error);
