/**
 * This timezones are for the regions in Mexico
 * Is used to send the timezone to contract generation API call and set the correct timezone for the contract,
 * to then get the correct date and time for the contract
 */
export const TIMEZONES_FOR_REGIONS_MX: { [key: string]: string } = {
  cdmx: 'America/Mexico_City',
  gdl: 'America/Mexico_City',
  mty: 'America/Monterrey',
  qro: 'America/Mexico_City',
  tij: 'America/Tijuana',
  moka: 'America/Mexico_City',
  pbe: 'America/Mexico_City',
  tol: 'America/Mexico_City',
  ptv: 'America/Mexico_City',
  tep: 'America/Mazatlan',
  col: 'America/Mexico_City',
  sal: 'America/Monterrey',
  torr: 'America/Monterrey',
  dur: 'America/Monterrey',
  mxli: 'America/Tijuana',
  her: 'America/Hermosillo',
  chi: 'America/Chihuahua',
  leo: 'America/Mexico_City',
  ags: 'America/Mexico_City',
  slp: 'America/Mexico_City',
  mer: 'America/Merida',
};

export function getTimezoneOnCity(city: string): string {
  return TIMEZONES_FOR_REGIONS_MX[city] || 'America/Mexico_City';
}
