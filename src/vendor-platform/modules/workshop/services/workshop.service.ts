import { CreateWorkshopDtoType } from '../dtos/create-workshop.dto';
import { Workshop } from '../models/workshops.model';

export class WorkshopService {
  async createWorkshop({ organizationId, name, address, phone, country = 'mx' }: CreateWorkshopDtoType) {
    const newWorkshop = new Workshop({
      organizationId,
      name,
      address,
      phone,
      country,
    });

    return newWorkshop.save();
  }

  async getWorkshopById(workshopId: string) {
    return Workshop.findById(workshopId);
  }

  async updateWorkshopById(workshopId: string, data: CreateWorkshopDtoType) {
    return Workshop.findByIdAndUpdate(workshopId, data, { new: true });
  }
}

export const workshopService = new WorkshopService();
