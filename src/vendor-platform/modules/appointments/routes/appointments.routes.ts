import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import {
  cancelAppointment,
  getLastScheduledppointmentByAssociateId,
  rescheduleAppointment,
  notAttendedAppointment,
  getAppointmentById,
} from '../controllers/appointments.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';

const appointmentRouter = Router();

const appointmentURL = '/appointments';

/**
 * Public routes
 */

appointmentRouter.put(
  '/public' + appointmentURL + '/:appointmentId/reschedule',
  verifyTokenVendorPlatform,
  errorHandlerV2(rescheduleAppointment)
);

/**
 * Private routes
 */

appointmentRouter.get(
  `${appointmentURL}/last/associate/:associateId`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getLastScheduledppointmentByAssociateId)
);

// get appointment by id
appointmentRouter.get(appointmentURL + '/:appointmentId', errorHandlerV2(getAppointmentById));

appointmentRouter.put(
  appointmentURL + '/:appointmentId/reschedule',
  verifyTokenVendorPlatform,
  errorHandlerV2(rescheduleAppointment)
);
appointmentRouter.delete(
  appointmentURL + '/:appointmentId/cancel',
  verifyTokenVendorPlatform,
  errorHandlerV2(cancelAppointment)
);

// confirm appointment

appointmentRouter.patch(
  appointmentURL + '/:appointmentId/confirm',
  verifyTokenVendorPlatform,
  errorHandlerV2(rescheduleAppointment)
);

appointmentRouter.patch(
  appointmentURL + '/:appointmentId/not-attended',
  verifyTokenVendorPlatform,
  errorHandlerV2(notAttendedAppointment)
);

export default appointmentRouter;
