import { AsyncController } from '@/types&interfaces/types';
import { CityService } from '../services/city.service';
import { createCityDto } from '../../company/dtos/create-city.dto';

export const createCity: AsyncController = async (req, res) => {
  const data = createCityDto.parse(req.body);
  const city = await CityService.createCity(data);

  return res.status(201).send({
    message: 'City created successfully',
    data: city,
  });
};

export const getCities: AsyncController = async (req, res) => {
  const { companyId } = req.query;
  const userId = req.userVendor.userId;

  const cities = await CityService.getCities(companyId as string, userId);

  return res.status(200).send({
    message: 'Cities retrieved successfully',
    data: cities,
  });
};

export const getCityById: AsyncController = async (req, res) => {
  const { cityId } = req.params;

  const city = await CityService.getCityById(cityId);

  if (!city) {
    return res.status(404).send({
      message: 'City not found',
    });
  }

  return res.status(200).send({
    message: 'City retrieved successfully',
    data: city,
  });
};

export const updateCity: AsyncController = async (req, res) => {
  const { cityId } = req.params;
  const data = createCityDto.partial().parse(req.body);

  const city = await CityService.updateCity(cityId, data);

  if (!city) {
    return res.status(404).send({
      message: 'City not found',
    });
  }

  return res.status(200).send({
    message: 'City updated successfully',
    data: city,
  });
};

export const deleteCity: AsyncController = async (req, res) => {
  const { cityId } = req.params;

  const city = await CityService.deleteCity(cityId);

  if (!city) {
    return res.status(404).send({
      message: 'City not found',
    });
  }

  return res.status(200).send({
    message: 'City deleted successfully',
    data: city,
  });
};
