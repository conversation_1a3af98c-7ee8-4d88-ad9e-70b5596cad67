import { AsyncController } from '@/types&interfaces/types';
import { serviceClassInstance } from '../services/service.service';

export const createService: AsyncController = async (req, res) => {
  try {
    const workshopId = req.body.workshopId;

    if (!workshopId) {
      return res.status(400).send({ message: 'Workshop ID is required' });
    }

    const organizationId = req.userVendor.organizationId;
    const serviceData = {
      ...req.body,
      workshopId,
      organizationId,
    };

    const result = await serviceClassInstance.createService(serviceData, req.files);

    return res.status(201).send({ message: 'Service registered successfully', data: result });
  } catch (error) {
    return res.status(500).send({ message: 'Error registering service', error });
  }
};

export const getServicesByStockId: AsyncController = async (req, res) => {
  const { stockId, associateId } = req.params;
  try {
    const organizationId = req.userVendor.organizationId;

    const services = await serviceClassInstance.findServicesByStockId({
      stockId,
      associateId,
      organizationId,
      options: { includeWorkshop: true },
    });

    return res.status(200).send({ message: 'Services found', data: services });
  } catch (error: any) {
    const message = error.message || 'Error fetching services';
    return res.status(500).send({ message, error });
  }
};

export const completeService: AsyncController = async (req, res) => {
  const { serviceId } = req.params;
  try {
    const data = req.body;
    const files = req.files as Express.Multer.File[];
    data.organizationId = req.userVendor.organizationId;

    const service = await serviceClassInstance.completeService(serviceId, data, files);

    return res.status(200).send({ message: 'Service completed', data: service });
  } catch (error: any) {
    const message = error.message || 'Error completing service';

    return res.status(500).send({ message, error });
  }
};

export const getServicesByAssociateId: AsyncController = async (req, res) => {
  const { associateId } = req.params;
  try {
    const organizationId = req.userVendor.organizationId;
    const services = await serviceClassInstance.findServicesByAssociateId(associateId, organizationId);
    return res.status(200).send({ message: 'Services found', data: services });
  } catch (error: any) {
    const message = error.message || 'Error fetching services';
    return res.status(500).send({ message, error });
  }
};
