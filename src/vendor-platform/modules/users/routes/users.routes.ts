import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  getUserById,
  getAllUsers,
  getUsersByOrganization,
  getUsersByCompany,
} from '../controllers/users.controller';

const usersRouter = Router();

const usersUrl = '/users';

// Get all users
usersRouter.get(usersUrl, verifyTokenVendorPlatform, errorHandlerV2(getAllUsers));

// Get user by ID
usersRouter.get(`${usersUrl}/:userId`, verifyTokenVendorPlatform, errorHandlerV2(getUserById));

// Get users by organization
usersRouter.get(
  '/organizations/:organizationId/users',
  verifyTokenVendorPlatform,
  errorHandlerV2(getUsersByOrganization)
);

// Get users by company
usersRouter.get('/companies/:companyId/users', verifyTokenVendorPlatform, errorHandlerV2(getUsersByCompany));

export default usersRouter;
