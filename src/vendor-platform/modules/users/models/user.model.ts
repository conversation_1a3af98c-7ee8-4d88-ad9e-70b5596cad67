import vendorDB from '@/vendor-platform/db';
import OrganizationModel from '@/vendor-platform/modules/organizations/models/organization.model';
import mongoose from 'mongoose';

export const userVendorTypes = ['workshop', 'company', 'company-gestor', 'all', 'superAdmin'] as const;
export type UserType = (typeof userVendorTypes)[number];
export interface IUserVendor {
  name: string;
  email: string;
  password: string;
  vendorId: string;
  role: string;
  status: 'active' | 'invited' | 'deleted';
  country: string;
  // userType: 'workshop' | 'company';
  userType: (typeof userVendorTypes)[number];
  language: string;
  created: Date;
  updated: Date;
}

const userVendorSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      // required: true,
    },

    email: {
      type: String,
      unique: true,
      required: true,
    },

    image: {
      type: String,
      default: '',
    },

    isVerified: {
      type: Boolean,
      default: false,
    },

    password: {
      type: String,
      select: false,
      required: false, // if oauth is implemented in the future, password will not be required
    },

    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: OrganizationModel.modelName,
    },

    roles: {
      type: [String],
      default: ['agent'],
    },

    userType: {
      type: String,
      enum: userVendorTypes,
      default: 'workshop',
    },

    status: {
      type: String,
      enum: ['active', 'invited', 'deleted'],
      default: 'invited',
    },

    country: {
      type: String,
      default: 'MX',
    },

    language: {
      type: String,
      default: 'es',
    },

    created: { type: Date, default: Date.now },
    updated: { type: Date, default: Date.now },
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

const UserVendorModel = vendorDB.model('Users', userVendorSchema);

export default UserVendorModel;
