import UserVendorModel from '../models/user.model';
import CompanyUserPermissions from '../../company/models/company-user-permissions.model';
import { Types } from 'mongoose';
import { AsyncController } from '@/types&interfaces/types';

export const getUserById: AsyncController = async (req, res) => {
  const { userId } = req.params;

  if (!Types.ObjectId.isValid(userId)) {
    return res.status(400).json({ message: 'Invalid user ID format' });
  }

  const user = await UserVendorModel.findById(userId);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // If user is company type, get permissions
  let permissions = null;
  if (user.userType === 'company' || user.userType === 'company-gestor') {
    permissions = await CompanyUserPermissions.findOne({ userId: user._id })
      .populate('companyId')
      .populate('allowedCities')
      .populate('allowedCrews');
  }

  const userWithPermissions = {
    ...user.toJSON(),
    permissions,
  };

  return res.status(200).send({ message: 'User found', data: userWithPermissions });
};

export const getAllUsers: AsyncController = async (req, res) => {
  const users = await UserVendorModel.find();
  return res.status(200).send({ message: 'Users found', data: users });
};

export const getUsersByOrganization: AsyncController = async (req, res) => {
  const { organizationId } = req.params;

  if (!Types.ObjectId.isValid(organizationId)) {
    return res.status(400).json({ message: 'Invalid organization ID format' });
  }

  const users = await UserVendorModel.find({ organizationId });
  // return res.json({ users });
  return res.status(200).send({ message: 'Users found', data: users });
};

export const getUsersByCompany: AsyncController = async (req, res) => {
  const { companyId } = req.params;

  if (!Types.ObjectId.isValid(companyId)) {
    return res.status(400).json({ message: 'Invalid company ID format' });
  }

  // Find all user permissions for this company
  // should be notListInUsers: false or the field should not exist
  const permissions = await CompanyUserPermissions.find({
    companyId,
    $or: [{ notListInUsers: false }, { notListInUsers: { $exists: false } }],
  })
    .populate('user')
    .populate('cities')
    .populate('crews')
    .lean();

  // Map the results to include both user and permission data
  const users = permissions.map((permission) => {
    return {
      ...permission.user,
      permissions: permission,
    };
  });

  return res.status(200).send({ message: 'Users found', data: users });
};
