import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  deleteServiceType,
  getServiceTypeById,
  updateServiceType,
} from '../controllers/serviceType.controller';

const serviceTypesRouter = Router();

const serviceTypesUrl = '/service-types';

serviceTypesRouter.get(
  `${serviceTypesUrl}/:id`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getServiceTypeById)
);
serviceTypesRouter.put(
  `${serviceTypesUrl}/:id`,
  verifyTokenVendorPlatform,
  errorHandlerV2(updateServiceType)
);
serviceTypesRouter.delete(
  `${serviceTypesUrl}/:id`,
  verifyTokenVendorPlatform,
  errorHandlerV2(deleteServiceType)
);

export default serviceTypesRouter;
