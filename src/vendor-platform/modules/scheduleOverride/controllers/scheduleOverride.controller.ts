import { AsyncController } from '@/types&interfaces/types';
import { ScheduleOverride } from '../models/scheduleOverride.model';
import { DateTime } from 'luxon';

export const createOrganizationOverride: AsyncController = async (req, res) => {
  const override = await ScheduleOverride.create({
    ...req.body,
    organization: req.params.organizationId,
    // createdBy: req.user._id // Asumiendo middleware de auth
  });
  return res.status(200).send(override);
};

export const createWorkshopOverride: AsyncController = async (req, res) => {
  if (!req.body.startDate || !req.body.endDate) {
    return res.status(400).send({ error: 'startDate and endDate are required' });
  }

  const startDate = DateTime.fromISO(req.body.startDate);
  const endDate = DateTime.fromISO(req.body.endDate);

  const override = await ScheduleOverride.create({
    ...req.body,
    startDate,
    endDate,
    workshop: req.params.workshopId,
  });

  return res.status(201).send({ message: 'Override created', data: override });
};

export const getOrganizationOverrides: AsyncController = async (req, res) => {
  const overrides = await ScheduleOverride.find({
    organization: req.params.organizationId,
    active: true,
  }).sort({ startDate: 1 });
  return res.status(200).send({ message: 'Overrides found', data: overrides });
};

export const getWorkshopOverrides: AsyncController = async (req, res) => {
  const overrides = await ScheduleOverride.find({
    workshop: req.params.workshopId,
    active: true,
  }).sort({ startDate: 1 });
  return res.status(200).send({ message: 'Overrides found', data: overrides });
};

export const updateOverride: AsyncController = async (req, res) => {
  const override = await ScheduleOverride.findByIdAndUpdate(req.params.id, req.body, { new: true });
  if (!override) {
    return res.status(404).send({ error: 'Override not found' });
  }
  return res.status(200).send({ message: 'Override updated', data: override });
};

export const deleteOverride: AsyncController = async (req, res) => {
  const override = await ScheduleOverride.findByIdAndUpdate(req.params.id, { active: false }, { new: true });
  if (!override) {
    return res.status(404).send({ error: 'Override not found' });
  }

  return res.status(200).send({ message: 'Override deleted', data: override });
};
