import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { updateOverride, deleteOverride } from '../controllers/scheduleOverride.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';

const scheduleOverrideRouter = Router();

const scheduleOverrideURL = '/overrides';
// router.put('/overrides/:id', ScheduleOverrideController.updateOverride);
// router.delete('/overrides/:id', ScheduleOverrideController.deleteOverride);
scheduleOverrideRouter.put(
  `${scheduleOverrideURL}/:id`,
  verifyTokenVendorPlatform,
  errorHandlerV2(updateOverride)
);
scheduleOverrideRouter.delete(
  `${scheduleOverrideURL}/:id`,
  verifyTokenVendorPlatform,
  errorHandlerV2(deleteOverride)
);

export default scheduleOverrideRouter;
