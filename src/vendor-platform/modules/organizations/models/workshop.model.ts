import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';

export interface IWorkshop extends Document {
  organizationId: mongoose.Types.ObjectId[]; // Referencias a los usuarios de la organización
  name: string;
  address?: string;
  country: string;
  phone?: string;
  created: Date;
  updated: Date;
}

const WorkshopSchema = new Schema<IWorkshop>({
  name: { type: String, required: true },
  organizationId: [{ type: Schema.Types.ObjectId, ref: 'Workshop' }],
  country: { type: String, required: true },
  address: { type: String, required: false },
  phone: { type: String, required: false },
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now },
});

const WorkshopModel = vendorDB.model<IWorkshop>('Workshop', WorkshopSchema);

export default WorkshopModel;
