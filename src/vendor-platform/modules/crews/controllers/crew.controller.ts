import { AsyncController } from '@/types&interfaces/types';
import { CrewService } from '../services/crew.service';
import { createCrewDto } from '../dtos/create-crew.dto';
import { updateCrewDto } from '../dtos/update-crew.dto';

export const createCrew: AsyncController = async (req, res) => {
  const data = createCrewDto.parse(req.body);
  const crew = await CrewService.createCrew(data);

  return res.status(201).send({
    message: 'Crew created',
    data: crew,
  });
};

export const getCrews: AsyncController = async (req, res) => {
  const crews = await CrewService.getCrews();

  return res.status(200).send({
    message: 'Crews found',
    data: crews,
  });
};

export const getCrewById: AsyncController = async (req, res) => {
  const { crewId } = req.params;
  const crew = await CrewService.getCrewById(crewId);

  if (!crew) {
    return res.status(404).send({
      message: 'Crew not found',
    });
  }

  return res.status(200).send({
    message: 'Crew found',
    data: crew,
  });
};

export const getCrewsByCity: AsyncController = async (req, res) => {
  const { cityId } = req.params;
  const userId = req.userVendor.userId;
  const crews = await CrewService.getCrewsByCity(cityId, userId);

  return res.status(200).send({
    message: 'Crews found',
    data: crews,
  });
};

export const updateCrew: AsyncController = async (req, res) => {
  const { crewId } = req.params;
  const data = updateCrewDto.parse(req.body);
  const updatedCrew = await CrewService.updateCrew(crewId, data);

  if (!updatedCrew) {
    return res.status(404).send({
      message: 'Crew not found',
    });
  }

  return res.status(200).send({
    message: 'Crew updated',
    data: updatedCrew,
  });
};

export const deleteCrew: AsyncController = async (req, res) => {
  const { crewId } = req.params;
  const deletedCrew = await CrewService.deleteCrew(crewId);

  if (!deletedCrew) {
    return res.status(404).send({
      message: 'Crew not found',
    });
  }

  return res.status(200).send({
    message: 'Crew deleted',
    data: deletedCrew,
  });
};
