import { CrewVendorModel } from '../models/crew.model';
import { CreateCrewDto } from '../dtos/create-crew.dto';
import { UpdateCrewDto } from '../dtos/update-crew.dto';
import CompanyUserPermissions from '../../company/models/company-user-permissions.model';

export class CrewService {
  static async createCrew(data: CreateCrewDto) {
    const crew = new CrewVendorModel(data);
    await crew.save();
    return crew;
  }

  static async getCrews() {
    return CrewVendorModel.find();
  }

  static async getCrewById(id: string) {
    return CrewVendorModel.findById(id);
  }

  static async getCrewsByCity(cityId: string, userId: string) {
    // return CrewVendorModel.find({ cityId });

    if (userId) {
      const userPermissions = await CompanyUserPermissions.findOne({ userId });
      if (userPermissions && !['owner', 'admin'].includes(userPermissions.role)) {
        const totalCrews = userPermissions.allowedCrews.length;
        // If there is not allowed crews, don't filter by crew, we assume that the user has access to all crews
        if (totalCrews > 0) {
          return CrewVendorModel.find({ cityId, _id: { $in: userPermissions.allowedCrews } });
        } else {
          return CrewVendorModel.find({ cityId });
        }
      }
    }
    return CrewVendorModel.find({ cityId });
  }

  static async updateCrew(id: string, data: UpdateCrewDto) {
    return CrewVendorModel.findByIdAndUpdate(id, data, { new: true });
  }

  static async deleteCrew(id: string) {
    return CrewVendorModel.findByIdAndDelete(id);
  }
}
