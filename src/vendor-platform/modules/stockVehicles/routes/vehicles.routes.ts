import { errorHandlerV2 } from '../../../../clean/errors/errorHandler';
import { getServicesByStockId } from '@/vendor-platform/modules/services/controllers/services.controller';
import { getVehicleDataById, getVehiclesVendor } from '../controllers/vehicles.controller';
import { Router } from 'express';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';

const vehiclesRouter = Router();

const vehiclesUrl = '/vehicles';

vehiclesRouter.get(vehiclesUrl, verifyTokenVendorPlatform, errorHandlerV2(getVehiclesVendor));
vehiclesRouter.get(`${vehiclesUrl}/:stockId`, verifyTokenVendorPlatform, errorHandlerV2(getVehicleDataById));
vehiclesRouter.get(
  `${vehiclesUrl}/:stockId/services`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getServicesByStockId)
);

export default vehiclesRouter;
