import { Async<PERSON>ontroller } from '@/types&interfaces/types';
import { Gestores } from '../models/gestores.model';
import { Tramites } from '../models/tramites.model';
import { createGestorDto } from '../dtos/create-gestor.dto';
import { Procedimiento } from '../models/procedimientos.model';
import { getUrlSingleFile, uploadFile } from '@/aws/s3';
import { replaceDocWithUrl } from '@/services/getPropertyWithUrls';
import StockVehicle from '@/models/StockVehicleSchema';
import Associate from '@/models/associateSchema';
import Document from '@/models/documentSchema';
import { Types } from 'mongoose';
import { removeEmptySpacesNameFile } from '@/services/removeEmptySpaces';

export const createGestor: AsyncController = async (req, res) => {
  const data = createGestorDto.parse(req.body);

  const gestor = await Gestores.create(data);

  return res.status(200).send({ message: 'Gestor creado', data: gestor });
};

export const getAllGestores: AsyncController = async (_req, res) => {
  const gestores = await Gestores.find().sort({ name: 1 });

  return res.status(200).send({ message: 'Gestores encontrados', data: gestores });
};

export const getGestorById: AsyncController = async (req, res) => {
  const gestor = await Gestores.findById(req.params.id);

  return res.status(200).send({ message: 'Gestor encontrado', data: gestor });
};

export const updateGestorById: AsyncController = async (req, res) => {
  const gestor = await Gestores.findByIdAndUpdate(req.params.id, req.body, { new: true });

  return res.status(200).send({ message: 'Gestor actualizado', data: gestor });
};

export const createTramite: AsyncController = async (req, res) => {
  const tramite = await Tramites.create(req.body);
  await Gestores.findByIdAndUpdate(req.body.gestorId, { $push: { tramites: tramite._id } }, { new: true });

  return res.status(200).send({ message: 'Trámite creado', data: tramite });
};

export const updateTramiteById: AsyncController = async (req, res) => {
  const tramite = await Tramites.findByIdAndUpdate(req.params.id, req.body, { new: true });

  return res.status(200).send({ message: 'Trámite actualizado', data: tramite });
};

export const deleteTramiteById: AsyncController = async (req, res) => {
  await Tramites.findByIdAndDelete(req.params.id);

  return res.status(200).send({ message: 'Trámite eliminado' });
};

export const getAllTramites: AsyncController = async (_req, res) => {
  const tramites = await Tramites.find().sort({ name: 1 });
  return res.status(200).send({ message: 'Trámites encontrados', data: tramites });
};

export const getTramiteById: AsyncController = async (req, res) => {
  const tramite = await Tramites.findById(req.params.id);

  return res.status(200).send({ message: 'Trámite encontrado', data: tramite });
};

export const createProcedimiento: AsyncController = async (req, res) => {
  try {
    // Get the tramite information to determine cost and duration
    const tramiteId = req.body.tramiteId;
    const gestorId = req.body.gestorId;
    const vehicleId = req.body.vehicleId;

    if (!tramiteId) {
      return res.status(400).send({
        message: 'El ID del trámite es requerido para crear un procedimiento',
      });
    }

    if (!gestorId) {
      return res.status(400).send({
        message: 'El ID del gestor es requerido para crear un procedimiento',
      });
    }

    // Get the tramite details
    const tramite = await Tramites.findById(tramiteId);
    if (!tramite) {
      return res.status(404).send({
        message: 'No se encontró el trámite especificado',
      });
    }

    // Get the gestor details
    const gestor = await Gestores.findById(gestorId);
    if (!gestor) {
      return res.status(404).send({
        message: 'No se encontró el gestor especificado',
      });
    }

    // Add cost and duration from the tramite to the procedure
    const procedimientoData = {
      ...req.body,
      cost: tramite.cost,
      duration: tramite.duration,
      status: 'Pendiente', // Default status
    };

    // Create the procedure
    const procedimiento = await Procedimiento.create(procedimientoData);

    // Get vehicle information if available
    let vehicleInfo = '';
    if (vehicleId) {
      try {
        const vehicle = await StockVehicle.findById(vehicleId);
        if (vehicle) {
          vehicleInfo = `${vehicle.brand} ${vehicle.model} ${vehicle.year} (${vehicle.vin})`;
          if (vehicle.carPlates && vehicle.carPlates.plates) {
            vehicleInfo += ` - Placas: ${vehicle.carPlates.plates}`;
          }
        }
      } catch (error) {
        console.error('Error al obtener información del vehículo:', error);
        // Continue even if vehicle info can't be retrieved
      }
    }

    // Send email notification to the gestor
    try {
      const { sendGestorNotification } = require('@/middlewares/email');
      console.log('Enviando notificación al gestor:', gestor.name, gestor.email);
      await sendGestorNotification({
        gestorName: gestor.name,
        gestorEmail: gestor.email,
        tramiteName: tramite.name,
        vehicleInfo: vehicleInfo,
        procedimientoId: procedimiento._id.toString(),
        cost: tramite.cost,
        duration: tramite.duration,
        status: 'Pendiente',
      });
      console.log(`Notificación enviada al gestor ${gestor.name} (${gestor.email})`);
    } catch (emailError) {
      console.error('Error al enviar notificación por email:', emailError);
      // Continue even if email fails
    }

    return res.status(200).send({ message: 'Procedimiento creado', data: procedimiento });
  } catch (error) {
    console.error('Error en createProcedimiento:', error);
    return res.status(500).send({
      message: 'Error al crear el procedimiento',
      error: error instanceof Error ? error.message : 'Error desconocido',
    });
  }
};

export const getProcedimientoById: AsyncController = async (req, res) => {
  try {
    const procedimiento = await Procedimiento.findById(req.params.id)
      .populate('gestorId', 'name location phone email')
      .populate('tramiteId', 'name description state documents city cost duration')
      .populate('vehicleId', 'vin brand model year');

    if (!procedimiento) {
      return res.status(404).send({ message: 'Procedimiento no encontrado' });
    }

    return res.status(200).send({
      message: 'Procedimiento encontrado',
      data: procedimiento,
    });
  } catch (error) {
    console.error('Error al obtener procedimiento:', error);
    return res.status(400).send({
      message: 'Error al obtener el procedimiento',
      error: error instanceof Error ? error.message : 'Error desconocido',
    });
  }
};

export const getProcedimientoByToken: AsyncController = async (req, res) => {
  try {
    const token = req.params.token;

    if (!token) {
      return res.status(400).send({ message: 'El token es requerido' });
    }

    const procedimiento = await Procedimiento.findOne({ token })
      .populate('gestorId', 'name location phone email')
      .populate('tramiteId', 'name description state documents city cost duration')
      .populate('vehicleId', 'vin brand model year');

    if (!procedimiento) {
      return res.status(404).send({ message: 'Procedimiento no encontrado con el token proporcionado' });
    }

    return res.status(200).send({
      message: 'Procedimiento encontrado',
      data: procedimiento,
    });
  } catch (error) {
    console.error('Error al obtener procedimiento por token:', error);
    return res.status(400).send({
      message: 'Error al obtener el procedimiento',
      error: error instanceof Error ? error.message : 'Error desconocido',
    });
  }
};

export const getProcedimientosByGestorId: AsyncController = async (req, res) => {
  const procedimientos = await Procedimiento.find({ gestorId: req.params.id })
    .populate('tramiteId', 'name description state documents city cost duration')
    .populate('vehicleId', 'vin brand model year')
    .sort({ createdAt: -1 });

  return res.status(200).send({ message: 'Procedimientos encontrados', data: procedimientos });
};
export const getProcedimientosByTramiteId: AsyncController = async (req, res) => {
  const procedimientos = await Procedimiento.find({ tramiteId: req.params.id })
    .populate('gestorId', 'name location phone email')
    .populate('vehicleId', 'vin brand model year')
    .sort({ createdAt: -1 });

  return res.status(200).send({ message: 'Procedimientos encontrados', data: procedimientos });
};
export const getProcedimientosByVehicleId: AsyncController = async (req, res) => {
  const vehicleId = req.params.id;

  if (!vehicleId) {
    return res.status(400).send({ message: 'El ID del vehículo es requerido en la URL.' });
  }

  console.log('Buscando procedimientos para vehicleId:', vehicleId);

  const procedimientos = await Procedimiento.find({ vehicleId: vehicleId })
    .populate('gestorId', 'name location phone email')
    .populate('tramiteId', 'name description state documents city cost duration')
    .sort({ createdAt: -1 });

  if (!procedimientos || procedimientos.length === 0) {
    return res.status(200).send({
      message: `No se encontraron procedimientos para el vehículo con ID: ${vehicleId}`,
      data: [],
    });
  }

  return res.status(200).send({ message: 'Procedimientos encontrados', data: procedimientos });
};
export const updateProcedimientoById: AsyncController = async (req, res) => {
  console.log('Actualizando procedimiento:', req.params.id, req.body);
  const procedimiento = await Procedimiento.findByIdAndUpdate(req.params.id, req.body, { new: true });

  console.log('Procedimiento actualizado:', procedimiento);

  return res.status(200).send({ message: 'Procedimiento actualizado', data: procedimiento });
};

export const getAllProcedimientos: AsyncController = async (_req, res) => {
  const procedimientos = await Procedimiento.find()
    .populate('gestorId', 'name location phone email')
    .populate('tramiteId', 'name description state documents city cost duration')
    .sort({ createdAt: -1 });

  return res.status(200).send({ message: 'Procedimientos encontrados', data: procedimientos });
};

// Interfaces para documentos del asociado
interface AssociateDocuments {
  curp?: any;
  ineFront?: any;
  ineBack?: any;
  taxStatus?: any;
  addressVerification?: any;
  driverLicenseFront?: any;
  driverLicenseBack?: any;
  garage?: any;
  [key: string]: any;
}

interface DocumentUrls {
  billUrl: string | null;
  platesUrl: string | null;
  circulationCardUrl: string | null;
  ineFrontUrl: string | null;
  ineBackUrl: string | null;
  curpUrl: string | null;
  taxStatusUrl: string | null;
  addressVerificationUrl: string | null;
  driverLicenseFrontUrl: string | null;
  driverLicenseBackUrl: string | null;
  lastPolicyUrl: string | null;
  garageUrl: string | null;
}

interface ProcessedDocument {
  format: string;
  name: string;
  path: string;
  isAdminUpload?: boolean;
  documentId?: string;
  url?: string | null;
}

// Función para crear objeto vacío de URLs
function createEmptyDocumentUrls(): DocumentUrls {
  return {
    billUrl: null,
    platesUrl: null,
    circulationCardUrl: null,
    ineFrontUrl: null,
    ineBackUrl: null,
    curpUrl: null,
    taxStatusUrl: null,
    addressVerificationUrl: null,
    driverLicenseFrontUrl: null,
    driverLicenseBackUrl: null,
    lastPolicyUrl: null,
    garageUrl: null,
  };
}

// Función auxiliar para obtener URL de un documento
async function getDocumentUrl(documentId: any): Promise<string | null> {
  if (!documentId) return null;
  try {
    const docWithUrl = await replaceDocWithUrl(documentId.toString());
    return docWithUrl?.url || null;
  } catch (error) {
    console.error('Error obteniendo URL del documento:', error);
    return null;
  }
}

// Función para obtener URL de documento admin
async function getAdminDocumentUrl(documentId: string): Promise<string | null> {
  const adminDoc = await Document.findById(documentId);
  if (adminDoc?.path) {
    return getUrlSingleFile(adminDoc.path);
  }
  return null;
}

// Función mejorada para mapear documentos especiales
function getSpecialDocumentUrl(docNameLower: string, urls: DocumentUrls): string | null {
  const mappings = [
    { keywords: ['factura'], exclude: ['refactura'], url: urls.billUrl },
    { keywords: ['placa'], url: urls.platesUrl },
    { keywords: ['circulación'], url: urls.circulationCardUrl },
    { keywords: ['oficial'], url: urls.ineFrontUrl || urls.ineBackUrl },
    { keywords: ['curp'], url: urls.curpUrl },
    { keywords: ['comprobante de domicilio'], url: urls.addressVerificationUrl },
    { keywords: ['licencia de conducir'], url: urls.driverLicenseFrontUrl || urls.driverLicenseBackUrl },
    { keywords: ['garage'], url: urls.garageUrl },
    { keywords: ['fiscal'], url: urls.taxStatusUrl },
    { keywords: ['poliza', 'póliza'], url: urls.lastPolicyUrl },
  ];

  for (const mapping of mappings) {
    const hasKeyword = mapping.keywords.some((keyword) => docNameLower.includes(keyword));
    const hasExclusion = mapping.exclude?.some((exclude) => docNameLower.includes(exclude));
    if (hasKeyword && !hasExclusion && mapping.url) {
      return mapping.url;
    }
  }
  return null;
}

// Función para procesar documentos del trámite
async function processTramiteDocuments(
  documents: any[],
  predefinedUrls: DocumentUrls,
  uploadedDocuments: any[] = []
): Promise<ProcessedDocument[]> {
  const uploadedDocsMap = new Map();
  uploadedDocuments.forEach((doc) => {
    if (doc.name) {
      uploadedDocsMap.set(doc.name, doc);
    }
  });

  return Promise.all(
    documents.map(async (doc) => {
      if (doc.format !== 'digital') {
        return doc;
      }

      let url = null;

      try {
        if (doc.isAdminUpload) {
          const uploadedDoc = uploadedDocsMap.get(doc.name);
          if (uploadedDoc?.documentId) {
            const docWithUrl = await replaceDocWithUrl(uploadedDoc.documentId.toString());
            url = docWithUrl?.url || null;
          }

          if (!url && doc.documentId) {
            url = await getAdminDocumentUrl(doc.documentId);
          }
        } else {
          url = getSpecialDocumentUrl(doc.name.toLowerCase(), predefinedUrls);

          if (!url && doc.path) {
            url = await getUrlSingleFile(doc.path);
          }
        }
      } catch (error) {
        console.error(`Error procesando documento '${doc.name}':`, error);
      }

      return {
        ...doc,
        url,
      };
    })
  );
}

// Función para procesar documentos subidos
async function processUploadedDocuments(uploadedDocuments: any[]) {
  if (!uploadedDocuments?.length) return [];

  return Promise.all(
    uploadedDocuments.map(async (docUpload) => {
      try {
        let url = null;

        if (docUpload.documentId) {
          const docWithUrl = await replaceDocWithUrl(docUpload.documentId.toString());
          url = docWithUrl?.url || null;
        }

        return {
          ...docUpload,
          url,
        };
      } catch (error) {
        console.error('Error procesando documento subido:', error);
        return {
          ...docUpload,
          url: null,
        };
      }
    })
  );
}

// Función para obtener URLs de documentos del vehículo
async function getVehicleDocumentUrls(vehicleId: string): Promise<DocumentUrls> {
  const urls = createEmptyDocumentUrls();

  try {
    const vehicleDetails = await StockVehicle.findById(vehicleId).lean();
    if (!vehicleDetails) return urls;

    const associateDetails = vehicleDetails.drivers?.length
      ? await Associate.findById(vehicleDetails.drivers[vehicleDetails.drivers.length - 1]).lean()
      : null;

    urls.billUrl = await getDocumentUrl(vehicleDetails.bill);
    urls.platesUrl = await getDocumentUrl(vehicleDetails.carPlates?.platesDocument);
    urls.circulationCardUrl = await getDocumentUrl(vehicleDetails.circulationCard?.frontImg);

    if (vehicleDetails.policiesArray?.length) {
      const lastPolicy = vehicleDetails.policiesArray[vehicleDetails.policiesArray.length - 1];
      urls.lastPolicyUrl = await getDocumentUrl(lastPolicy.policyDocument);
    }

    if (associateDetails?.documents) {
      const docFields: (keyof DocumentUrls)[] = [
        'ineFrontUrl',
        'ineBackUrl',
        'curpUrl',
        'taxStatusUrl',
        'addressVerificationUrl',
        'driverLicenseFrontUrl',
        'driverLicenseBackUrl',
        'garageUrl',
      ];

      const docMappings: Record<keyof DocumentUrls, string> = {
        billUrl: 'bill',
        platesUrl: 'plates',
        circulationCardUrl: 'circulation',
        ineFrontUrl: 'ineFront',
        ineBackUrl: 'ineBack',
        curpUrl: 'curp',
        taxStatusUrl: 'taxStatus',
        addressVerificationUrl: 'addressVerification',
        driverLicenseFrontUrl: 'driverLicenseFront',
        driverLicenseBackUrl: 'driverLicenseBack',
        garageUrl: 'garage',
        lastPolicyUrl: 'lastPolicy',
      };

      for (const urlField of docFields) {
        const docField = docMappings[urlField];
        const documents = associateDetails.documents as AssociateDocuments;
        if (documents[docField]) {
          urls[urlField] = await getDocumentUrl(documents[docField]);
        }
      }
    }
  } catch (error) {
    console.error('Error obteniendo URLs de documentos del vehículo:', error);
  }

  return urls;
}

// Función separada para procesar cada procedimiento
async function processProcedimiento(procedimiento: any) {
  const proc = procedimiento.toObject ? procedimiento.toObject() : { ...procedimiento };

  proc.uploadedDocuments = await processUploadedDocuments(proc.uploadedDocuments || []);
  const documentUrls = proc.vehicleId
    ? await getVehicleDocumentUrls(proc.vehicleId)
    : createEmptyDocumentUrls();

  if (proc.tramiteId?.documents?.length) {
    proc.tramiteId.documents = await processTramiteDocuments(
      proc.tramiteId.documents,
      documentUrls,
      proc.uploadedDocuments
    );
  }

  return proc;
}

export const getProcedimientosByEmail: AsyncController = async (req, res) => {
  try {
    const { email } = req.params;
    if (!email) {
      return res.status(400).send({ message: 'Email es requerido' });
    }

    const procedimientos = await Procedimiento.find({ email })
      .populate('gestorId', 'name location phone email')
      .populate('tramiteId', 'name description state documents city cost duration')
      .sort({ createdAt: -1 })
      .lean();

    if (!procedimientos.length) {
      return res.status(200).send({
        message: 'No se encontraron procedimientos',
        data: [],
      });
    }

    const procedimientosWithUrls = await Promise.all(
      procedimientos.map((procedimiento) => processProcedimiento(procedimiento))
    );

    return res.status(200).send({
      message: 'Procedimientos encontrados',
      data: procedimientosWithUrls,
    });
  } catch (error) {
    console.error('Error en getProcedimientosByEmail:', error);
    return res.status(500).send({
      message: 'Error interno del servidor',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
    });
  }
};

export const tokenValidator: AsyncController = async (req, res) => {
  const { token } = req.body;
  if (!token) return res.status(400).send({ message: 'Token no encontrado' });

  const procedimiento = await Procedimiento.findOne({ token });
  if (!procedimiento) return res.status(404).send({ message: 'Procedimiento no encontrado' });

  return res.status(200).send({ message: 'Token válido', data: procedimiento });
};

export const deleteDocumentFromProcedimiento: AsyncController = async (req, res) => {
  try {
    const { procedimientoId, documentId } = req.params;

    if (!procedimientoId) {
      return res.status(400).send({ message: 'El ID del procedimiento es requerido' });
    }

    if (!documentId) {
      return res.status(400).send({ message: 'El ID del documento es requerido' });
    }

    const procedimiento = await Procedimiento.findById(procedimientoId);
    if (!procedimiento) {
      return res.status(404).send({ message: 'Procedimiento no encontrado' });
    }

    // Check if document exists in the procedure
    if (!procedimiento.uploadedDocuments || procedimiento.uploadedDocuments.length === 0) {
      return res.status(404).send({ message: 'El procedimiento no tiene documentos' });
    }

    // Find the document in the uploadedDocuments array
    const documentIndex = procedimiento.uploadedDocuments.findIndex(
      (doc) => doc.documentId.toString() === documentId
    );

    if (documentIndex === -1) {
      return res.status(404).send({ message: 'Documento no encontrado en el procedimiento' });
    }

    // Get the document from the database
    const document = await Document.findById(documentId);
    if (!document) {
      return res.status(404).send({ message: 'Documento no encontrado en la base de datos' });
    }

    // Delete the document from the database
    await Document.findByIdAndDelete(documentId);

    // Remove the document from the procedure
    procedimiento.uploadedDocuments.splice(documentIndex, 1);
    await procedimiento.save();

    return res.status(200).send({
      message: 'Documento eliminado correctamente',
      data: { documentId },
    });
  } catch (error) {
    console.error('Error al eliminar documento:', error);
    return res.status(500).send({
      message: 'Error al eliminar el documento',
      error: error instanceof Error ? error.message : 'Error desconocido',
    });
  }
};

export const createAndUpdateCarPlates: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  try {
    const { historyData, isEditing, plates } = req.body;
    const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };

    const frontImgFiles = 'frontImg' in files ? files.frontImg[0] : undefined;
    const backImgFiles = 'backImg' in files ? files.backImg[0] : undefined;
    const platesDocumentFile = 'platesDocument' in files ? files.platesDocument[0] : undefined;

    const stockVehicle = await StockVehicle.findById(vehicleId);
    if (!stockVehicle) return res.status(404).send({ message: 'Vehículo no encontrado' });

    const carPlatesObj: any = {};
    const isEdit = isEditing === 'true' || isEditing === true;

    const removeSpacesFrontImg = removeEmptySpacesNameFile(frontImgFiles);
    const removeSpacesBackImg = removeEmptySpacesNameFile(backImgFiles);
    const removeSpacesPlatesDoc = removeEmptySpacesNameFile(platesDocumentFile);

    const carNumber = stockVehicle.carNumber;
    if (!isEdit) {
      /* DENTRO DE ESTE IF QUIERE DECIR QUE NO SE ESTA EDITANDO, SE ESTA CREANDO */

      if (!plates || !historyData) return res.status(404).send({ message: 'Faltan datos requeridos' });

      if (plates) carPlatesObj.plates = plates;

      if (frontImgFiles) {
        const frontImgDoc = new Document({
          originalName: removeSpacesFrontImg,
          path: `stock/${carNumber}/` + removeSpacesFrontImg,
        });

        await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`);
        await frontImgDoc.save();
        carPlatesObj.frontImg = frontImgDoc._id;
      }

      if (backImgFiles) {
        const backImgDoc = new Document({
          originalName: removeSpacesBackImg,
          path: `stock/${carNumber}/` + removeSpacesBackImg,
        });
        await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`);
        await backImgDoc.save();
        carPlatesObj.backImg = backImgDoc._id;
      }

      if (platesDocumentFile) {
        const policyDoc = new Document({
          originalName: removeSpacesPlatesDoc,
          path: `stock/${carNumber}/` + removeSpacesPlatesDoc,
        });
        await uploadFile(platesDocumentFile, removeSpacesPlatesDoc, `stock/${carNumber}/`);
        await policyDoc.save();
        carPlatesObj.platesDocument = policyDoc._id;
      }

      stockVehicle.carPlates = carPlatesObj;

      // Add history data
      const historyUpdate = {
        ...historyData,
        description: 'Placas agregadas',
        group: 'vehicle-info',
        userId: new Types.ObjectId(historyData.userId),
      };
      stockVehicle.updateHistory.push(historyUpdate);

      await stockVehicle.save();

      return res.status(200).send({ message: 'Vehículo actualizado correctamente' });
    } else {
      if (stockVehicle.carPlates) {
        const oldFrontImgDoc = await Document.findById(stockVehicle.carPlates?.frontImg);
        const oldBackImgDoc = await Document.findById(stockVehicle.carPlates?.backImg);
        const oldPolicyDoc = await Document.findById(stockVehicle.carPlates?.platesDocument);

        if (plates) stockVehicle.carPlates.plates = plates.trim();

        if (frontImgFiles && oldFrontImgDoc) {
          stockVehicle.oldDocuments.push(oldFrontImgDoc);
          oldFrontImgDoc.originalName = removeSpacesFrontImg;
          oldFrontImgDoc.path = `stock/${carNumber}/${removeSpacesFrontImg}`;

          await oldFrontImgDoc.save();
          await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`);
        } else if (frontImgFiles && !oldFrontImgDoc) {
          const frontImgDoc = new Document({
            originalName: removeSpacesFrontImg,
            path: `stock/${carNumber}/` + removeSpacesFrontImg,
          });

          await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`);
          await frontImgDoc.save();
          stockVehicle.carPlates.frontImg = frontImgDoc._id;
        }

        if (backImgFiles && oldBackImgDoc) {
          stockVehicle.oldDocuments.push(oldBackImgDoc);
          oldBackImgDoc.originalName = removeSpacesBackImg;
          oldBackImgDoc.path = `stock/${carNumber}/${removeSpacesBackImg}`;

          await oldBackImgDoc.save();
          await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`);
        } else if (backImgFiles && !oldBackImgDoc) {
          const backImgDoc = new Document({
            originalName: removeSpacesBackImg,
            path: `stock/${carNumber}/` + removeSpacesBackImg,
          });

          await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`);
          await backImgDoc.save();
          stockVehicle.carPlates.backImg = backImgDoc._id;
        }

        if (platesDocumentFile && oldPolicyDoc) {
          stockVehicle.oldDocuments.push(oldPolicyDoc);
          oldPolicyDoc.originalName = removeSpacesPlatesDoc;
          oldPolicyDoc.path = `stock/${carNumber}/${removeSpacesPlatesDoc}`;

          await oldPolicyDoc.save();
          await uploadFile(platesDocumentFile, removeSpacesPlatesDoc, `stock/${carNumber}/`);
        } else if (platesDocumentFile && !oldPolicyDoc) {
          const platesDoc = new Document({
            originalName: removeSpacesPlatesDoc,
            path: `stock/${carNumber}/` + removeSpacesPlatesDoc,
          });

          await uploadFile(platesDocumentFile, removeSpacesPlatesDoc, `stock/${carNumber}/`);
          await platesDoc.save();
          stockVehicle.carPlates.platesDocument = platesDoc._id;
        }
      }

      // Add history data
      const historyUpdate = {
        ...historyData,
        description: 'Placas actualizadas',
        group: 'vehicle-info',
        userId: new Types.ObjectId(historyData.userId),
      };
      stockVehicle.updateHistory.push(historyUpdate);

      await stockVehicle.save();
      return res.status(200).send({ message: 'Vehículo actualizado correctamente' });
    }
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: 'Error al actualizar las placas del vehículo', error });
  }
};

export const uploadDocumentToProcedimiento: AsyncController = async (req, res) => {
  try {
    const { procedimientoId } = req.params;
    const { documentType } = req.body;

    if (!procedimientoId) {
      return res.status(400).send({ message: 'El ID del procedimiento es requerido' });
    }

    if (!documentType) {
      return res.status(400).send({ message: 'El tipo de documento es requerido' });
    }

    const procedimiento = await Procedimiento.findById(procedimientoId);
    if (!procedimiento) {
      return res.status(404).send({ message: 'Procedimiento no encontrado' });
    }

    // Check if file was uploaded
    const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };
    if (!files || !('document' in files) || !files.document[0]) {
      return res.status(400).send({ message: 'No se ha subido ningún archivo' });
    }

    const documentFile = files.document[0];
    const fileName = removeEmptySpacesNameFile(documentFile);

    // Get vehicle info for folder structure
    const vehicleId = procedimiento.vehicleId;
    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).send({ message: 'Vehículo no encontrado' });
    }

    const carNumber = vehicle.carNumber || vehicleId.toString();
    const folderPath = `procedimientos/${carNumber}/${procedimientoId}/`;

    // Create document in database
    const newDocument = new Document({
      originalName: fileName,
      path: folderPath + fileName,
      vehicleId: vehicleId,
    });

    // Upload file to S3
    await uploadFile(documentFile, fileName, folderPath);
    await newDocument.save();

    // Add document to procedimiento
    const documentUpload = {
      documentId: newDocument._id,
      name: documentType,
      uploadDate: new Date(),
      documentType: documentType,
    };

    // Initialize uploadedDocuments array if it doesn't exist
    if (!procedimiento.uploadedDocuments) {
      procedimiento.uploadedDocuments = [];
    }

    procedimiento.uploadedDocuments.push(documentUpload);
    await procedimiento.save();

    // Get URL for the uploaded document
    const documentWithUrl = await replaceDocWithUrl(newDocument._id.toString());

    return res.status(200).send({
      message: 'Documento subido correctamente',
      data: {
        ...documentUpload,
        url: documentWithUrl ? documentWithUrl.url : null,
      },
    });
  } catch (error) {
    console.error('Error al subir documento:', error);
    return res.status(500).send({
      message: 'Error al subir el documento',
      error: error instanceof Error ? error.message : 'Error desconocido',
    });
  }
};

export const createAndUpdateCirculationCard: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { historyData, isEditing, validity, number } = req.body;

  const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };

  const frontImgFiles = 'frontImg' in files ? files.frontImg[0] : undefined;
  const backImgFiles = 'backImg' in files ? files.backImg[0] : undefined;

  const removeSpacesFrontImg = removeEmptySpacesNameFile(frontImgFiles);
  const removeSpacesBackImg = removeEmptySpacesNameFile(backImgFiles);

  const stockVehicle = await StockVehicle.findById(vehicleId);

  if (!stockVehicle) return res.status(404).send({ message: 'Vehículo no encontrado' });

  if (!historyData || !historyData.userId || !historyData.step || !historyData.description)
    return res.status(404).send({ message: 'Datos de historial incompletos' });

  const carNumber = stockVehicle.carNumber;

  try {
    const isEdit = isEditing === 'true' || isEditing === true;

    if (!isEdit) {
      // Código para crear nueva tarjeta de circulación
      if (!validity || !historyData || !number || !frontImgFiles || !backImgFiles)
        return res.status(404).send({ message: 'Faltan datos requeridos' });

      const frontImgDoc = new Document({
        originalName: removeSpacesFrontImg,
        path: `stock/${carNumber}/` + removeSpacesFrontImg,
      });
      await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`);
      await frontImgDoc.save();

      const backImgDoc = new Document({
        originalName: removeSpacesBackImg,
        path: `stock/${carNumber}/` + removeSpacesBackImg,
      });
      await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`);
      await backImgDoc.save();

      stockVehicle.circulationCard = {
        number: number,
        validity: validity,
        frontImg: frontImgDoc._id,
        backImg: backImgDoc._id,
      };

      // Add history data
      const historyUpdate = {
        ...historyData,
        description: 'Tarjeta de circulación agregada',
        group: 'vehicle-info',
        userId: new Types.ObjectId(historyData.userId),
      };
      stockVehicle.updateHistory.push(historyUpdate);

      await stockVehicle.save();
      return res.status(200).send({ message: 'Vehículo actualizado correctamente' });
    } else {
      // Código para editar tarjeta de circulación existente
      if (stockVehicle.circulationCard) {
        if (validity) stockVehicle.circulationCard.validity = validity;
        if (number) stockVehicle.circulationCard.number = number;

        const oldFrontImgDoc = await Document.findById(stockVehicle.circulationCard.frontImg);
        const oldBackImgDoc = await Document.findById(stockVehicle.circulationCard.backImg);

        if (frontImgFiles && oldFrontImgDoc) {
          stockVehicle.oldDocuments.push(oldFrontImgDoc);
          oldFrontImgDoc.originalName = removeSpacesFrontImg;
          oldFrontImgDoc.path = `stock/${carNumber}/${removeSpacesFrontImg}`;

          await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`);
          await oldFrontImgDoc.save();
        } else if (frontImgFiles && !oldFrontImgDoc) {
          const frontImgDoc = new Document({
            originalName: removeSpacesFrontImg,
            path: `stock/${carNumber}/` + removeSpacesFrontImg,
          });

          await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`);
          await frontImgDoc.save();
          stockVehicle.circulationCard.frontImg = frontImgDoc._id;
        }

        if (backImgFiles && oldBackImgDoc) {
          stockVehicle.oldDocuments.push(oldBackImgDoc);
          oldBackImgDoc.originalName = removeSpacesBackImg;
          oldBackImgDoc.path = `stock/${carNumber}/${removeSpacesBackImg}`;

          await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`);
          await oldBackImgDoc.save();
        } else if (backImgFiles && !oldBackImgDoc) {
          const backImgDoc = new Document({
            originalName: removeSpacesBackImg,
            path: `stock/${carNumber}/` + removeSpacesBackImg,
          });

          await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`);
          await backImgDoc.save();
          stockVehicle.circulationCard.backImg = backImgDoc._id;
        }
      }

      // Add history data
      const historyUpdate = {
        ...historyData,
        description: 'Tarjeta de circulación actualizada',
        group: 'vehicle-info',
        userId: new Types.ObjectId(historyData.userId),
      };
      stockVehicle.updateHistory.push(historyUpdate);

      await stockVehicle.save();
      return res.status(200).send({ message: 'Vehículo actualizado correctamente' });
    }
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: 'Error al actualizar la tarjeta de circulación ', error });
  }
};
