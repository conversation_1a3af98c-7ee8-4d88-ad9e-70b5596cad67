import { Schema, Document } from 'mongoose';
import vendorDB from '@/vendor-platform/db';

interface IDocument {
  name: string;
  format: 'digital' | 'físico';
  isAdminUpload?: boolean;
}

export interface ITramite extends Document {
  name: string;
  state: string;
  documents: IDocument[];
  cost: number;
  duration: number;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    isCarAtYard?: boolean;
  };
}

const TramitesSchema = new Schema<ITramite>(
  {
    name: {
      type: String,
      required: [true, 'El nombre del trámite es obligatorio.'],
      trim: true,
    },
    state: {
      type: String,
      required: [true, 'El estado donde aplica el trámite es obligatorio.'],
      trim: true,
      index: true,
    },
    documents: [
      {
        _id: false,
        name: {
          type: String,
          required: [true, 'El nombre del documento es obligatorio.'],
          trim: true,
        },
        format: {
          type: String,
          required: [true, 'Es necesario especificar el formato del documento.'],
          enum: ['digital', 'físico'],
          trim: true,
        },
        isAdminUpload: {
          type: Boolean,
          default: false,
        },
      },
    ],
    cost: {
      type: Number,
      required: [true, 'El costo del trámite es obligatorio.'],
    },
    duration: {
      type: Number,
      required: [true, 'La duración del trámite es obligatoria.'],
    },
    description: {
      type: String,
      required: [true, 'La descripción del trámite es obligatoria.'],
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

TramitesSchema.index({ name: 1, state: 1 }, { unique: true });
TramitesSchema.set('toObject', { virtuals: true });
TramitesSchema.set('toJSON', { virtuals: true });

export const Tramites = vendorDB.model<ITramite>('Tramites', TramitesSchema);
