import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@/vendor-platform/db';

export interface IGestores extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  phone: string;
  email: string;
  tramites: mongoose.Types.ObjectId[] | any[];
}

const GestoresSchema = new Schema(
  {
    name: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    tramites: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Tramites',
      },
    ],
  },
  { timestamps: true }
);

export const Gestores = vendorDB.model<IGestores>('Gestores', GestoresSchema);
