import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import {
  createGestor,
  getAllGestores,
  getGestorById,
  updateGestorById,
  createAndUpdateCarPlates,
} from '../controllers/gestores.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import { upload } from '@/multer/multer';
import { checkSchema } from 'express-validator';
import stockPatchSchema from '@/express-validator/associates/stockPatchValidation';
import { stockPatchValidate } from '@/middlewares/stockValidators';

const gestoresRouter = Router();

const gestoresURL = '/gestores';

// Definición de campos para carPlates
const carPlates = [
  { name: 'frontImg', maxCount: 1 },
  { name: 'backImg', maxCount: 1 },
  { name: 'platesDocument', maxCount: 1 },
];

// --- <PERSON><PERSON><PERSON> de Gestores ---
gestoresRouter.get(gestoresURL, verifyTokenVendorPlatform, errorHandlerV2(getAllGestores));
gestoresRouter.get(`${gestoresURL}/:id`, verifyTokenVendorPlatform, errorHandlerV2(getGestorById));
gestoresRouter.post(gestoresURL, verifyTokenVendorPlatform, errorHandlerV2(createGestor));
gestoresRouter.patch(`${gestoresURL}/:id`, verifyTokenVendorPlatform, errorHandlerV2(updateGestorById));

// Ruta para actualizar placas de vehículos
gestoresRouter.patch(
  `${gestoresURL}/update/carPlates/:vehicleId`,
  verifyTokenVendorPlatform,
  upload.fields(carPlates),
  checkSchema(stockPatchSchema),
  stockPatchValidate,
  errorHandlerV2(createAndUpdateCarPlates)
);

export default gestoresRouter;
