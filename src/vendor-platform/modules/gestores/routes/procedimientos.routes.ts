import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import {
  createProcedimiento,
  getAllProcedimientos,
  getProcedimientoById,
  getProcedimientoByToken,
  updateProcedimientoById,
  getProcedimientosByVehicleId,
  getProcedimientosByGestorId,
  getProcedimientosByEmail,
  tokenValidator,
  createAndUpdateCarPlates,
  createAndUpdateCirculationCard,
  uploadDocumentToProcedimiento,
  deleteDocumentFromProcedimiento,
} from '../controllers/gestores.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import { upload } from '@/multer/multer';
import { checkSchema } from 'express-validator';
import stockPatchSchema from '@/express-validator/associates/stockPatchValidation';
import { stockPatchValidate } from '@/middlewares/stockValidators';

const procedimientosRouter = Router();

const gestoresURL = '/gestores';
const procedimientosSubURL = '/procedimientos';

// Definición de campos para carPlates
const carPlates = [
  { name: 'frontImg', maxCount: 1 },
  { name: 'backImg', maxCount: 1 },
  { name: 'platesDocument', maxCount: 1 },
];

// Definición de campos para circulationCard
const circulationCard = [
  { name: 'frontImg', maxCount: 1 },
  { name: 'backImg', maxCount: 1 },
];

// Definición de campos para documentos de procedimientos
const procedimientoDocument = [{ name: 'document', maxCount: 1 }];

// Rutas de Procedimientos (Instancias)
procedimientosRouter.get(
  `${gestoresURL}${procedimientosSubURL}`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getAllProcedimientos)
);

procedimientosRouter.post(
  `${gestoresURL}${procedimientosSubURL}`,
  verifyTokenVendorPlatform,
  errorHandlerV2(createProcedimiento)
);

procedimientosRouter.get(
  `${gestoresURL}${procedimientosSubURL}/:procedimientoId`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getProcedimientoById)
);

procedimientosRouter.patch(
  `${gestoresURL}${procedimientosSubURL}/:id`,
  verifyTokenVendorPlatform,
  errorHandlerV2(updateProcedimientoById)
);

procedimientosRouter.get(
  `${gestoresURL}${procedimientosSubURL}/stock/:id`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getProcedimientosByVehicleId)
);

procedimientosRouter.get(
  `${gestoresURL}${procedimientosSubURL}/gestor/:id`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getProcedimientosByGestorId)
);

procedimientosRouter.get(
  `${gestoresURL}${procedimientosSubURL}/email/:email`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getProcedimientosByEmail)
);

// Ruta para buscar un procedimiento por su token
procedimientosRouter.get(
  `${gestoresURL}${procedimientosSubURL}/token/:token`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getProcedimientoByToken)
);

procedimientosRouter.post(
  `${gestoresURL}${procedimientosSubURL}/token/validate`,
  verifyTokenVendorPlatform,
  errorHandlerV2(tokenValidator)
);

procedimientosRouter.patch(
  `${gestoresURL}${procedimientosSubURL}/update/carPlates/:vehicleId`,
  verifyTokenVendorPlatform,
  upload.fields(carPlates),
  checkSchema(stockPatchSchema),
  stockPatchValidate,
  errorHandlerV2(createAndUpdateCarPlates)
);

procedimientosRouter.patch(
  `${gestoresURL}${procedimientosSubURL}/update/circulationCard/:vehicleId`,
  verifyTokenVendorPlatform,
  upload.fields(circulationCard),
  checkSchema(stockPatchSchema),
  stockPatchValidate,
  errorHandlerV2(createAndUpdateCirculationCard)
);

// Rutas para gestionar documentos de procedimientos
procedimientosRouter.post(
  `${gestoresURL}${procedimientosSubURL}/:procedimientoId/documents`,
  verifyTokenVendorPlatform,
  upload.fields(procedimientoDocument),
  errorHandlerV2(uploadDocumentToProcedimiento)
);

procedimientosRouter.delete(
  `${gestoresURL}${procedimientosSubURL}/:procedimientoId/documents/:documentId`,
  verifyTokenVendorPlatform,
  errorHandlerV2(deleteDocumentFromProcedimiento)
);

export default procedimientosRouter;
