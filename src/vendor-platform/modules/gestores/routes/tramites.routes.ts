import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { createTramite, updateTramiteById, getAllTramites } from '../controllers/gestores.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';

const tramitesRouter = Router();

const gestoresURL = '/gestores';
const tramitesSubURL = '/tramites';

// --- <PERSON><PERSON><PERSON> de <PERSON>ámite<PERSON> (Tipos) ---
tramitesRouter.get(
  `${gestoresURL}${tramitesSubURL}/all`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getAllTramites)
);

tramitesRouter.post(
  `${gestoresURL}${tramitesSubURL}`,
  verifyTokenVendorPlatform,
  errorHandlerV2(createTramite)
);

tramitesRouter.patch(
  `${gestoresURL}${tramitesSubURL}/:tramiteId`,
  verifyTokenVendorPlatform,
  errorHandlerV2(updateTramiteById)
);

export default tramitesRouter;
