import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  createNeighborhood,
  getNeighborhoods,
  getNeighborhoodById,
  updateNeighborhood,
  deleteNeighborhood,
  // getNeighborhoodsByCity,
} from '../controllers/neighborhood.controller';

const neighborhoodRouter = Router();

neighborhoodRouter.get('/public/neighborhoods', verifyTokenVendorPlatform, errorHandlerV2(getNeighborhoods));

neighborhoodRouter.get('/neighborhoods', verifyTokenVendorPlatform, errorHandlerV2(getNeighborhoods));
neighborhoodRouter.post('/neighborhoods', verifyTokenVendorPlatform, errorHandlerV2(createNeighborhood));
neighborhoodRouter.get(
  '/neighborhoods/:neighborhoodId',
  verifyTokenVendorPlatform,
  errorHandlerV2(getNeighborhoodById)
);

neighborhoodRouter.put(
  '/neighborhoods/:neighborhoodId',
  verifyTokenVendorPlatform,
  errorHandlerV2(updateNeighborhood)
);
neighborhoodRouter.delete(
  '/neighborhoods/:neighborhoodId',
  verifyTokenVendorPlatform,
  errorHandlerV2(deleteNeighborhood)
);

export default neighborhoodRouter;
