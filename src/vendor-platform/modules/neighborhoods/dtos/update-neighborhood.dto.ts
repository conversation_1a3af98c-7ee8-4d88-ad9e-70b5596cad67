import { z } from 'zod';
import { timeRangeSchema } from '../../company/dtos/create-company.dto';

const weeklyScheduleSchema = z.object({
  monday: timeRangeSchema.optional(),
  tuesday: timeRangeSchema.optional(),
  wednesday: timeRangeSchema.optional(),
  thursday: timeRangeSchema.optional(),
  friday: timeRangeSchema.optional(),
  saturday: timeRangeSchema.optional(),
  sunday: timeRangeSchema.optional(),
});

export const updateNeighborhoodDto = z.object({
  name: z.string().min(3).optional(),
  scheduleConfig: z
    .object({
      weeklySchedule: weeklyScheduleSchema,
      installationDuration: z.number().min(30),
      timezone: z.string().optional().default('America/Mexico_City'),
      breakTime: z
        .object({
          start: z
            .string()
            .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
            .optional(),
          end: z
            .string()
            .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
            .optional(),
        })
        // .refine((data) => data.start && data.end, {
        //   message: 'Break time must have both start and end',
        //   path: ['breakTime'],
        // })
        // refine, checking if only one is present, if one is present, the other must be present too
        // otherwise, if both are missing, it's ok
        .refine((data) => (data.start && data.end) || (!data.start && !data.end), {
          message: 'Break time must have both start and end',
          path: ['breakTime'],
        }),
      maxSimultaneousInstallations: z.number().min(1),
    })
    .optional(),
  address: z
    .object({
      street: z.string().min(3).optional(),
      number: z.string().min(1).optional(),
      interior: z.string().optional(),
      colony: z.string().min(3).optional(),
      city: z.string().min(3).optional(),
      postalCode: z.string().min(5).optional(),
    })
    .optional(),
  mapsLink: z.string().optional(),
  active: z.boolean().optional(),
});

export type UpdateNeighborhoodDto = z.infer<typeof updateNeighborhoodDto>;
