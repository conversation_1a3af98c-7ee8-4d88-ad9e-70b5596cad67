import { AsyncController } from '@/types&interfaces/types';
import { CompanyService } from '../services/company.service';
import { createCompanyDto } from '../dtos/create-company.dto';

export const createCompany: AsyncController = async (req, res) => {
  const data = createCompanyDto.parse(req.body);

  const company = await CompanyService.createCompany(data);

  return res.status(201).send({
    message: 'Company created successfully',
    data: company,
  });
};

export const getCompanies: AsyncController = async (req, res) => {
  const companies = await CompanyService.getCompanies();

  return res.status(200).send({
    message: 'Companies retrieved successfully',
    data: companies,
  });
};

export const getCompanyById: AsyncController = async (req, res) => {
  const { companyId } = req.params;

  const company = await CompanyService.getCompanyById(companyId);

  if (!company) {
    return res.status(404).send({
      message: 'Company not found',
    });
  }

  return res.status(200).send({
    message: 'Company retrieved successfully',
    data: company,
  });
};

export const updateCompany: AsyncController = async (req, res) => {
  const { companyId } = req.params;
  const data = createCompanyDto.partial().parse(req.body);

  const company = await CompanyService.updateCompany(companyId, data);

  if (!company) {
    return res.status(404).send({
      message: 'Company not found',
    });
  }

  return res.status(200).send({
    message: 'Company updated successfully',
    data: company,
  });
};

export const deleteCompany: AsyncController = async (req, res) => {
  const { companyId } = req.params;

  const company = await CompanyService.deleteCompany(companyId);

  if (!company) {
    return res.status(404).send({
      message: 'Company not found',
    });
  }

  return res.status(200).send({
    message: 'Company deleted successfully',
    data: company,
  });
};
