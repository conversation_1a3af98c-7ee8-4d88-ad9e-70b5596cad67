import vendorDB from '@/vendor-platform/db';
import { IUserVendor } from '../../users/models/user.model';
import { ICompany } from './company.model';
import { ICity } from '../../cities/models/city.model';
import { ICrew } from '../../crews/models/crew.model';
import { Types, Schema } from 'mongoose';

export enum CompanyUserRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MANAGER = 'manager',
  SUPERVISOR = 'supervisor',
  OPERATOR = 'operator',
}

export interface ICompanyUserPermissions {
  userId: Types.ObjectId;
  companyId: Types.ObjectId;
  user: IUserVendor;
  company: ICompany;
  role: CompanyUserRole;
  cities: ICity[];
  crews: ICrew[];
  allowedCities: Types.ObjectId[];
  allowedCrews: Types.ObjectId[];
  notListInUsers: boolean;
  status: 'active' | 'invited' | 'suspended';
  created: Date;
  updated: Date;
}

const companyUserPermissionsSchema = new Schema<ICompanyUserPermissions>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'Users',
      required: true,
    },
    companyId: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: true,
    },
    notListInUsers: {
      type: Boolean,
      default: false,
    },
    role: {
      type: String,
      enum: Object.values(CompanyUserRole),
      required: true,
    },
    allowedCities: [
      {
        type: Schema.Types.ObjectId,
        ref: 'City',
      },
    ],
    allowedCrews: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Crew',
      },
    ],
    status: {
      type: String,
      enum: ['active', 'invited', 'suspended'],
      default: 'invited',
    },
    created: { type: Date, default: Date.now },
    updated: { type: Date, default: Date.now },
  },
  {
    timestamps: true,
  }
);

// Índice compuesto para búsquedas eficientes y unicidad
companyUserPermissionsSchema.index({ userId: 1, companyId: 1 }, { unique: true });

// create virtual fields for population
companyUserPermissionsSchema.virtual('user', {
  ref: 'Users',
  localField: 'userId',
  foreignField: '_id',
  justOne: true,
});

companyUserPermissionsSchema.virtual('company', {
  ref: 'Company',
  localField: 'companyId',
  foreignField: '_id',
  justOne: true,
});

companyUserPermissionsSchema.virtual('cities', {
  ref: 'City',
  localField: 'allowedCities',
  foreignField: '_id',
});

companyUserPermissionsSchema.virtual('crews', {
  ref: 'Crew',
  localField: 'allowedCrews',
  foreignField: '_id',
});

companyUserPermissionsSchema.set('toJSON', { virtuals: true });
companyUserPermissionsSchema.set('toObject', { virtuals: true });

const CompanyUserPermissions = vendorDB.model<ICompanyUserPermissions>(
  'CompanyUserPermissions',
  companyUserPermissionsSchema
);

export default CompanyUserPermissions;
