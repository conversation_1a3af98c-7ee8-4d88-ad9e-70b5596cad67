import { z } from 'zod';

export const timeRangeSchema = z.object({
  start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
});

export const createCityDto = z.object({
  companyId: z.string(),
  name: z.string().min(3),
  state: z.string().min(3),
  country: z.string().optional().default('MX'),
  postalCode: z.string().optional(),
  timezone: z.string().optional(),
});

export type CreateCityDto = z.infer<typeof createCityDto>;
