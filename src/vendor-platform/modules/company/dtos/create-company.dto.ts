import { z } from 'zod';

export const timeRangeSchema = z.object({
  start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
});

export const createCompanyDto = z.object({
  name: z.string().min(3),
  address: z.string().min(5),
  phone: z.string().min(10),
  email: z.string().email(),
  // defaultInstallationDuration: z.number().min(30).default(180),
  // defaultBreakTime: timeRangeSchema.default({
  //   start: '13:00',
  //   end: '14:00',
  // }),
});

export type CreateCompanyDto = z.infer<typeof createCompanyDto>;
