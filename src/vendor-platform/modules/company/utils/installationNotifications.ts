import axios from 'axios';
import { HILOS_API_KEY, HILOS_URL_SEND_TEMPLATE } from '@/constants';

const TEMPLATES = {
  NEW_INSTALLATION_REQUEST: '067f7f3c-e559-73d9-8000-ffbac2d86537',
};

interface SendNewInstallationNotificationParams {
  associateName: string;
  phone: string;
}

export async function sendNewInstallationNotification({
  associateName,
  phone,
}: SendNewInstallationNotificationParams) {
  try {
    const url = `${HILOS_URL_SEND_TEMPLATE}/${TEMPLATES.NEW_INSTALLATION_REQUEST}/send`;
    console.log('url', url);
    phone = phone.toString().replace('+52', '').replace('52', '');
    console.log('phone', phone);
    console.log('associateName', associateName);
    console.log('HILOS_API_KEY', HILOS_API_KEY);
    const response = await axios.post(
      url,
      {
        variables: [associateName],
        phone,
      },
      {
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('sendNewInstallationNotification response: ', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error sending new installation notification:', error.response?.data, error.message);
    return error;
  }
}
