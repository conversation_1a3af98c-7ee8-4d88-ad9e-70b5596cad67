import {
  CreateScheduleCommand,
  DeleteScheduleCommand,
  CreateScheduleGroupCommand,
  // SchedulerClient,
} from '@aws-sdk/client-scheduler';
import { schedulerClient } from '@/aws/scheduler-client';
import { DateTime } from 'luxon';
import { isDev } from '@/constants';

interface ScheduleEventParams {
  appointmentId: string;
  scheduledTime: DateTime;
  timezone: string;
  reminderType: 'night-before' | 'one-hour-before';
  endpoint: string;
}

interface DeleteScheduleEventParams {
  eventId: string;
  reminderType: 'night-before' | 'one-hour-before';
}

const SCHEDULE_GROUPS = {
  'night-before': 'installation-reminders-night-before',
  'one-hour-before': 'installation-reminders-hour-before',
} as const;

async function ensureScheduleGroupsExist() {
  for (const groupName of Object.values(SCHEDULE_GROUPS)) {
    try {
      await schedulerClient.send(
        new CreateScheduleGroupCommand({
          Name: groupName,
        })
      );
    } catch (error: any) {
      // Si el grupo ya existe, ignoramos el error
      if (error.name !== 'ConflictException') {
        throw error;
      }
    }
  }
}

export async function createScheduleEvent({
  appointmentId,
  scheduledTime,
  timezone,
  reminderType,
  endpoint,
}: ScheduleEventParams) {
  const scheduleGroup = SCHEDULE_GROUPS[reminderType];

  // Asegurarnos que los grupos existen antes de crear el evento
  await ensureScheduleGroupsExist();

  const scheduleCommand = new CreateScheduleCommand({
    Name: `${appointmentId}-${Date.now()}`,
    GroupName: scheduleGroup,
    ScheduleExpression: `at(${scheduledTime.toFormat("yyyy-MM-dd'T'HH:mm:ss")})`,
    ScheduleExpressionTimezone: timezone,
    Target: {
      Arn: 'arn:aws:lambda:us-east-1:564033404206:function:charger-installation-reminder-handler',
      RoleArn: 'arn:aws:iam::564033404206:role/schedule-appointment-reminders',
      Input: JSON.stringify({
        appointmentId,
        endpoint,
        reminderType,
        isDev,
      }),
    },
    FlexibleTimeWindow: { Mode: 'OFF' },
    ActionAfterCompletion: 'DELETE',
  });

  return schedulerClient.send(scheduleCommand);
}

export async function deleteScheduleEvent({ eventId, reminderType }: DeleteScheduleEventParams) {
  const scheduleGroup = SCHEDULE_GROUPS[reminderType];

  try {
    const deleteCommand = new DeleteScheduleCommand({
      Name: eventId,
      GroupName: scheduleGroup,
    });

    return await schedulerClient.send(deleteCommand);
  } catch (error: any) {
    // Si el grupo o el evento no existe, ignoramos el error
    if (error.name !== 'ResourceNotFoundException') {
      throw error;
    }
    return null;
  }
}

export const InstallationScheduleEvents = {
  createScheduleEvent,
  deleteScheduleEvent,
};
