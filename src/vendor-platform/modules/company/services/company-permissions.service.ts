import { Types } from 'mongoose';
import CompanyUserPermissions, {
  CompanyUserRole,
  ICompanyUserPermissions,
} from '../models/company-user-permissions.model';
import UserVendorModel from '@/vendor-platform/modules/users/models/user.model';
import { sendInvitationEmail } from '@/middlewares/email';
import { organizationsService } from '../../organizations/services/organizations.service';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
// import { sendInvitationEmail } from '@/vendor-platform/services/email.service';

export class CompanyPermissionsService {
  static async inviteUserToCompany({
    email,
    companyId,
    name,
    role,
    allowedCities = [],
    allowedCrews = [],
    originUrl,
  }: {
    email: string;
    companyId: string;
    name: string;
    role: CompanyUserRole;
    allowedCities?: string[];
    allowedCrews?: string[];
    originUrl: string;
  }) {
    // Verificar si el usuario existe o crear uno nuevo
    let user = await UserVendorModel.findOne({ email });

    if (!user) {
      user = new UserVendorModel({
        email,
        name,
        status: 'invited',
        userType: 'company',
      });
      await user.save();
    }

    // Crear o actualizar los permisos
    const permissions = await CompanyUserPermissions.findOneAndUpdate(
      { userId: user._id, companyId },
      {
        role,
        allowedCities: allowedCities.map((id) => new Types.ObjectId(id)),
        allowedCrews: allowedCrews.map((id) => new Types.ObjectId(id)),
        status: 'invited',
      },
      { upsert: true, new: true }
    );

    // Generar y enviar invitación
    // const invitationToken = this.generateInvitationToken({ userId: user._id.toString(), companyId });
    const invitationToken = organizationsService.generateInvitationToken(email);

    const fullUrl = `${originUrl}/${user.language}/accept-invitation?code=${invitationToken}`;

    const text = organizationsService.getTextsOfInvitation(user.language, user.name!, 'company');

    await sendInvitationEmail({
      title: text.title,
      subject: text.subject,
      message: text.message,
      email,
      token: invitationToken,
      baseUrl: originUrl + '/' + user.language,
      fullUrl,
    });

    // return permissions;
    // Return user and permissions
    return { user, permissions };
  }

  static async getUserCompanyPermissions({ userId, companyId }: { userId: string; companyId: string }) {
    const permissions = await CompanyUserPermissions.findOne({
      $or: [{ userId, companyId }, { userId }, { companyId }],
    })
      .populate('allowedCities')
      .populate('allowedCrews');
    if (!permissions) throw HttpException.NotFound('Permissions not found');
    return permissions;
  }

  async validateUserAccess({
    userId,
    companyId,
    cityId,
    crewId,
  }: {
    userId: string;
    companyId: string;
    cityId?: string;
    crewId?: string;
  }): Promise<boolean> {
    const permissions = await CompanyUserPermissions.findOne({ userId, companyId });
    if (!permissions || permissions.status !== 'active') {
      return false;
    }

    // OWNER y ADMIN tienen acceso completo
    if ([CompanyUserRole.OWNER, CompanyUserRole.ADMIN].includes(permissions.role)) {
      return true;
    }

    // Validar acceso específico
    if (cityId && !permissions.allowedCities.map(String).includes(cityId)) {
      return false;
    }

    if (crewId && !permissions.allowedCrews.includes(new Types.ObjectId(crewId))) {
      return false;
    }

    return true;
  }

  static async validateOwnerAndAdminAccess({ userId, companyId }: { userId: string; companyId: string }) {
    // const permissions = await CompanyUserPermissions.findOne({ userId, companyId });
    const permissions = await this.getUserCompanyPermissions({ userId, companyId });
    // console.log('permi', permissions.status);
    if (permissions.status !== 'active') {
      return {
        validation: false,
        message: 'User has no permissions',
        permissions,
      };
    }

    // OWNER y ADMIN tienen acceso completo
    if ([CompanyUserRole.OWNER, CompanyUserRole.ADMIN].includes(permissions.role)) {
      // return true;
      return {
        validation: true,
        message: 'User has owner or admin permissions',
        permissions,
      };
    }

    // return false;
    return {
      validation: false,
      message: 'User has no owner or admin permissions',
      permissions,
    };
  }

  static async updateUserPermissionStatus(
    userId: string,
    status: 'active' | 'invited' | 'suspended'
  ): Promise<ICompanyUserPermissions | null> {
    console.table({ userId, status });
    return CompanyUserPermissions.findOneAndUpdate(
      { userId },
      { status, updated: new Date() },
      { new: true }
    );
  }

  static async updateUserPermissions(
    userId: string,
    companyId: string,
    updates: {
      role?: CompanyUserRole;
      allowedCities?: string[];
      allowedCrews?: string[];
    }
  ): Promise<ICompanyUserPermissions | null> {
    return CompanyUserPermissions.findOneAndUpdate(
      { userId, companyId },
      {
        ...updates,
        allowedCities: updates.allowedCities?.map((id) => new Types.ObjectId(id)),
        allowedCrews: updates.allowedCrews?.map((id) => new Types.ObjectId(id)),
        updated: new Date(),
      },
      { new: true }
    )
      .populate('allowedCities')
      .populate('allowedCrews');
  }

  static async removeUserPermissions(userId: string, companyId: string): Promise<void> {
    await CompanyUserPermissions.findOneAndDelete({ userId, companyId });
  }
}
