import { CreateCityDto } from '../dtos/create-city.dto';
import { City } from '../../cities/models/city.model';

export class CityService {
  static async createCity(data: CreateCityDto) {
    const city = new City(data);
    await city.save();
    return city;
  }

  static async getCities(companyId: string) {
    return City.find({ companyId });
  }

  static async getCityById(id: string) {
    return City.findById(id);
  }

  static async updateCity(id: string, data: Partial<CreateCityDto>) {
    return City.findByIdAndUpdate(id, data, { new: true });
  }

  static async deleteCity(id: string) {
    return City.findByIdAndDelete(id);
  }
}
