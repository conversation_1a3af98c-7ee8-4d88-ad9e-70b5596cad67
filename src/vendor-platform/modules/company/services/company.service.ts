import { CreateCompanyDto } from '../dtos/create-company.dto';
import { CompanyVendorModel } from '../models/company.model';

export class CompanyService {
  static async createCompany(data: CreateCompanyDto) {
    const company = new CompanyVendorModel(data);
    await company.save();
    return company;
  }

  static async getCompanies() {
    return CompanyVendorModel.find();
  }

  static async getCompanyById(id: string) {
    return CompanyVendorModel.findById(id);
  }

  static async updateCompany(id: string, data: Partial<CreateCompanyDto>) {
    return CompanyVendorModel.findByIdAndUpdate(id, data, { new: true });
  }

  static async deleteCompany(id: string) {
    return CompanyVendorModel.findByIdAndDelete(id);
  }
}
