import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import { upload } from '@/multer/multer';
import {
  getAvailableInstallationSlots,
  createInstallationAppointment,
  getInstallationAppointments,
  updateInstallationAppointmentProof,
  rescheduleInstallationAppointment,
  sendOneHourReminder,
  sendOneNightReminder,
  updateInstallationArrival,
  filterInstallationAppointments,
  updateNotAttendedReason,
  searchInstallationAppointments,
} from '../controllers/installation-appointment.controller';

const installationRouter = Router();

/* ================= Public routes ================= */
installationRouter.post(
  '/public/installation-appointments',
  // verifyTokenVendorPlatform,
  errorHandlerV2(createInstallationAppointment)
);

installationRouter.patch(
  '/public/installation-appointments/:appointmentId/proof',
  upload.array('proofImages', 10), // Permite hasta 10 imágenes
  errorHandlerV2(updateInstallationAppointmentProof)
);

installationRouter.patch(
  '/public/installation-appointments/:appointmentId/reschedule',
  errorHandlerV2(rescheduleInstallationAppointment)
);

// Endpoints para las lambdas
installationRouter.post(
  '/public/installation-appointments/:appointmentId/send-one-hour-reminder',
  errorHandlerV2(sendOneHourReminder)
);

installationRouter.post(
  '/public/installation-appointments/:appointmentId/send-one-night-reminder',
  errorHandlerV2(sendOneNightReminder)
);

/* ================= Private routes ================= */
installationRouter.get(
  '/installation-slots/:neighborhoodId/:date',
  errorHandlerV2(getAvailableInstallationSlots)
);

installationRouter.get(
  '/installation-appointments',
  verifyTokenVendorPlatform,
  errorHandlerV2(getInstallationAppointments)
);
installationRouter.get(
  '/installation-appointments/filter',
  verifyTokenVendorPlatform,
  errorHandlerV2(filterInstallationAppointments)
);
installationRouter.post(
  '/installation-appointments',
  verifyTokenVendorPlatform,
  errorHandlerV2(createInstallationAppointment)
);

installationRouter.patch(
  '/installation-appointments/:appointmentId/arrived',
  errorHandlerV2(updateInstallationArrival)
);

installationRouter.patch(
  '/installation-appointments/:appointmentId/reschedule',
  verifyTokenVendorPlatform,
  errorHandlerV2(rescheduleInstallationAppointment)
);

installationRouter.patch(
  '/installation-appointments/:appointmentId/proof',
  verifyTokenVendorPlatform,
  upload.array('proofImages', 10),
  errorHandlerV2(updateInstallationAppointmentProof)
);

installationRouter.patch(
  '/installation-appointments/:id/not-attended-reason',
  verifyTokenVendorPlatform,
  errorHandlerV2(updateNotAttendedReason)
);

installationRouter.get(
  '/installation-appointments/search',
  verifyTokenVendorPlatform,
  errorHandlerV2(searchInstallationAppointments)
);

export default installationRouter;
