import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
// import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  createCompany,
  deleteCompany,
  getCompanies,
  getCompanyById,
  updateCompany,
} from '../controllers/company.controller';
import companyPermissionsRouter from './company-permissions.routes';

const companyRouter = Router();

companyRouter.post('/companies', /* verifyTokenVendorPlatform, */ errorHandlerV2(createCompany));
companyRouter.get('/companies', /* verifyTokenVendorPlatform, */ errorHandlerV2(getCompanies));
companyRouter.get('/companies/:companyId', /* verifyTokenVendorPlatform, */ errorHandlerV2(getCompanyById));
companyRouter.put('/companies/:companyId', /* verifyTokenVendorPlatform, */ errorHandlerV2(updateCompany));
companyRouter.delete('/companies/:companyId', /* verifyTokenVendorPlatform, */ errorHandlerV2(deleteCompany));
companyRouter.use(companyPermissionsRouter);

export default companyRouter;
