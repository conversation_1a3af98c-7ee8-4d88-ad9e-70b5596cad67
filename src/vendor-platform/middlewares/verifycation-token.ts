import jwt from 'jsonwebtoken';
import { blacklist } from '@/conf/blackList';
import { accessTokenSecret, genericMessages } from '@/constants';
import { MiddlewareController } from '@/types&interfaces/types';
import { vendorPlatformAccessTokenSecret } from '../constants';
import { isAdminPlatformRequest } from '../utils/is-admin-platform';
import User from '@/models/userSchema';

export const verifyTokenVendorPlatform: MiddlewareController = async (req, res, next) => {
  const authorization = req.headers.authorization;
  const isAdminPlatform = isAdminPlatformRequest(req.headers);

  if (!authorization)
    return res.status(401).send({ message: genericMessages.errors.unauthorized, data: 'v' });
  const token = authorization.split(' ')[1];
  if (!token) return res.status(401).send({ message: genericMessages.errors.unauthorized, data: 'v' });
  if (blacklist[token])
    return res.status(403).send({ message: genericMessages.errors.tokens.invalidToken, data: 'v' });

  try {
    let secret = vendorPlatformAccessTokenSecret;

    if (isAdminPlatform) {
      secret = accessTokenSecret;
    }

    const result = jwt.verify(token, secret) as {
      userId: string;
      role: string;
      organizationId: string;
      email: string;
      exp: number;
      iat: number;
    };
    // req.userId = result;
    req.userVendor = {
      ...result,
      isAdminPlatform,
    };

    if (isAdminPlatform) {
      // console.log('result', result);

      const user = await User.findById(result.userId).select('email').lean();
      let userEmail = 'API'; // If user is not found, we set the email to API because maybe it's a generated token
      if (user) {
        userEmail = user.email;
      }

      req.userReq = {
        ...result,
        email: userEmail,
        isAdminPlatform,
      };
    }

    return next();
  } catch (error: any) {
    console.log('error message', error.message);
    return res.status(400).send({ message: error.message });
  }
};
