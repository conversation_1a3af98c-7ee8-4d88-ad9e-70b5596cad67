import { Router } from 'express';
import { validatePlatesAndReturnNeighborHoodsScheduleConfig } from './general-controllers';

const generalRoutes = Router();

//  Validate plates, get vehicle data and associate data
// then check the state of associate and find all neighborhoods that
// belong to the same city state of the associate
generalRoutes.post('/validate-plates/neighborhoods', validatePlatesAndReturnNeighborHoodsScheduleConfig);

export default generalRoutes;
