import Associate from '@/models/associateSchema';
import StockVehicle from '@/models/StockVehicleSchema';
import { AsyncController } from '@/types&interfaces/types';
import { NeighborhoodVendorModel } from './modules/neighborhoods/models/neighborhood.model';
import { City } from './modules/cities/models/city.model';
import { InstallationAppointment } from './modules/company/models/installation-appointment.model';
import { statesByCity } from '@/constants/cities-values';

export const validatePlatesAndReturnNeighborHoodsScheduleConfig: AsyncController = async (req, res) => {
  try {
    const { plates } = req.body;

    if (!plates) {
      return res.status(400).send({ error: 'Plates are required' });
    }
    const stockVehicle = await StockVehicle.findOne({
      // 'carPlates.plates': plates.toUpperCase().trim(),
      'carPlates.plates': new RegExp(`^${plates.trim()}$`, 'i'),
    }).select('vehicleState drivers carPlates.plates model brand vin');

    if (!stockVehicle) {
      return res.status(404).send({ error: 'Stock vehicle not found' });
    }

    const driverId = stockVehicle.drivers[stockVehicle.drivers.length - 1]._id;

    const associate = await Associate.findById(driverId).select('state firstName lastName email phone');

    if (!associate) {
      return res.status(404).send({ error: 'Associate not found' });
    }

    const appointment = await InstallationAppointment.findOne({
      associateId: associate._id,
    }).populate('neighborhood', 'name scheduleConfig');

    const associateState = associate.state;
    const vehicleStateCode = stockVehicle.vehicleState;

    // get vehicle state by object
    const vehicleState = statesByCity[vehicleStateCode]?.state || associateState;

    // if the appointment is already present, then fetch the neighborhood from the appointment
    // if not, then fetch the neighborhood from the city state based on the associate state

    let neighborhoods = [];
    if (appointment) {
      neighborhoods = [appointment.neighborhood];
    } else {
      const citiesWithSameState = await City.find({
        $or: [{ state: associateState }, { state: vehicleState }],
      }).select('name state');

      neighborhoods = await NeighborhoodVendorModel.find({
        cityId: { $in: citiesWithSameState.map((city) => city._id) },
      }).select('name scheduleConfig');
    }

    (stockVehicle.drivers as any) = undefined;
    const responseData = {
      vehicleInfo: {
        ...stockVehicle.toObject(),
        driver: {
          ...associate.toObject(),
          name: `${associate.firstName} ${associate.lastName}`,
        },
        neighborhoods,
        appointment,
      },
    };

    return res.status(200).send({ message: 'Associate and stock vehicle found', data: responseData });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};
