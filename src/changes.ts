/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable prettier/prettier */
import axios from 'axios';
import { steps } from './constants';
import StockVehicle from './models/StockVehicleSchema';
import AssociatePayments from './models/associatePayments';
import Associate from './models/associateSchema';
import MainContractSchema from './models/mainContractSchema';
import User from './models/userSchema';
import { getGigstackConfig } from './services/tokenAssignGigstack';
import moment from 'moment';
import Document from './models/documentSchema';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from './constants/payments-api';
import xlsx from 'xlsx';
import fs from 'fs';
import path from 'path';
import UserVehicleRestrictions from './models/userVehicleRestriction';
import TempSuscriptionPayments from './modules/TempSuscriptionPayments/model/tempSuscriptionPayment.model';
import { generateDateNotUTC } from './modules/TempSuscriptionPayments/controllers/cronjobHandler.controller';
import { subDays } from 'date-fns';
import ServiceVendorModel from './vendor-platform/modules/services/models/service.model';
import { AppointmentVendor, AppointmentVendorStatus } from './vendor-platform/modules/workshop/models/appointment.model';
require('moment/locale/es');

// Configurar moment para usar español
moment.locale('es');

export async function addStepObjectProd() {
  const vehicles = await StockVehicle.find();
  for (const vehicle of vehicles) {
    vehicle.step = {
      stepName: steps.stock.name,
      stepNumber: steps.stock.number,
    };

    // vehicle.save();
  }
  console.log('hecho');
}

// const states: { [key: string]: string } = {
//   1: 'cdmx',
//   2: 'gdl',
//   3: 'mty',
//   4: 'qro',
//   5: 'tij,',
//   6: 'moka',
// };

export async function changeCorrectVehicleState() {
  const vehicles = await StockVehicle.find();
  // let counter = 0;
  // let counter2 = 0;
  for (const vehicle of vehicles) {
    const contractNumber = vehicle.carNumber;
    // const state = states[contractNumber[0]] as 'cdmx' | 'gdl' | 'mty' | 'qro' | 'tij' | 'moka';
    console.log('contract number: ' + contractNumber[0], contractNumber);
    if (contractNumber[0] === '1') {
      vehicle.vehicleState = 'cdmx';
    } else if (contractNumber[0] === '2') {
      vehicle.vehicleState = 'gdl';
    } else if (contractNumber[0] === '3') {
      vehicle.vehicleState = 'mty';
    } else if (contractNumber[0] === '4') {
      vehicle.vehicleState = 'qro';
    } else if (contractNumber[0] === '5') {
      vehicle.vehicleState = 'tij';
    } else if (contractNumber[0] === '6') {
      vehicle.vehicleState = 'moka';
    } else {
      vehicle.vehicleState = 'cdmx';
    }
    // await vehicle.save();
  }
  // console.log(counter, counter2, vehicles.length);
  console.log(vehicles.length);
}

export async function updateSteps() {
  const vehicles = await StockVehicle.find();
  for (const vehicle of vehicles) {
    // const { status } = vehicle;
    // console.log(vehicle.contract);

    const hasAllDocs =
      vehicle.carPlates?.plates &&
      vehicle.circulationCard?.validity &&
      vehicle.gpsNumber &&
      vehicle.gpsSerie &&
      vehicle.tenancy.length > 0 &&
      vehicle.policiesArray.length > 0;

    // if (status === 'stock' || status === 'Stock') {
    //   vehicle.step.stepName = steps.stock.name;
    //   vehicle.step.stepNumber = steps.stock.number;
    // }
    // if (status === 'Vehiculo Listo' || status === 'Vehiculo listo') {
    //   vehicle.step.stepName = steps.vehicleReady.name;
    //   vehicle.step.stepNumber = steps.vehicleReady.number;
    // }
    // if (status.toLocaleLowerCase() === 'activo') {
    //   vehicle.step.stepName = steps.driverAssigned.name;
    //   vehicle.step.stepNumber = steps.driverAssigned.number;
    // }
    // if (status.toLocaleLowerCase() === 'activo' && !hasAllDocs) {
    //   vehicle.step.stepName = steps.stock.name;
    //   vehicle.step.stepNumber = steps.stock.number;
    // }

    // if (status === 'Contrato generado' || vehicle.contract) {
    //   vehicle.step.stepName = steps.contractCreated.name;
    //   vehicle.step.stepNumber = steps.contractCreated.number;
    // }
    // if (status === 'Entregado') {
    //   vehicle.step.stepName = steps.delivered.name;
    //   vehicle.step.stepNumber = steps.delivered.number;
    // }
    /*     if (vehicle.carNumber === '2004') {
      console.log('plates', vehicle.carPlates);
      console.log('circulation card', vehicle.circulationCard);
      console.log('gps', vehicle.gpsNumber);
      console.log('gps serie', vehicle.gpsSerie);
      console.log('tenancy', vehicle.tenancy);
      console.log('policy', vehicle.policiesArray);
      console.log(hasAllDocs);
    } */
    if (!hasAllDocs) {
      vehicle.step.stepName = steps.stock.name;
      vehicle.step.stepNumber = steps.stock.number;
    }
    if (hasAllDocs) {
      vehicle.step.stepName = steps.vehicleReady.name;
      vehicle.step.stepNumber = steps.vehicleReady.number;
    }
    if (vehicle.drivers.length > 0) {
      vehicle.step.stepName = steps.driverAssigned.name;
      vehicle.step.stepNumber = steps.driverAssigned.number;
    }
    if (vehicle.contract) {
      vehicle.step.stepName = steps.contractCreated.name;
      vehicle.step.stepNumber = steps.contractCreated.number;
    }
    console.log(vehicle.carNumber, vehicle.step);
    // await vehicle.save();
  }
  console.log('cambios guardados');
}

export async function addSettingsToUsers() {
  const users = await User.find();
  for (const user of users) {
    user.settings = {
      allowedRegions: [user.city],
    };
    // await user.save();
  }
  console.log('hecho');
}

export async function moveContractRelation() {
  const vehicles = await StockVehicle.find();

  for (const vehicle of vehicles) {
    if (vehicle.contract) {
      console.log(vehicle.carNumber);
      const associate = await Associate.findById(vehicle.drivers[vehicle.drivers.length - 1]?._id);
      if (associate) {
        const unSginedContract = vehicle.contract._id;
        associate.unSignedContractDoc = unSginedContract;
        console.log(associate._id);
        console.log(associate.firstName);
        console.log(associate.unSignedContractDoc);
        delete vehicle.contract;
        // associate.save();
        // break;
      }
      // await vehicle.save();
    }
  }
}

export async function addGPSINFO() {
  const vehicles = await StockVehicle.find();

  for (const vehicle of vehicles) {
    const carNumber = vehicle.carNumber;
    if (vehicle.status.toLowerCase() === 'vehiculo listo') {
      vehicle.status = 'stock';
    }
    if (vehicle.status.toLowerCase() === 'bloqueado') {
      vehicle.status = 'bloqueo';
    }
    vehicle.gpsNumber = carNumber;
    vehicle.gpsSerie = carNumber;
    console.log(`Vehicle ${vehicle.carNumber} updated`);
    // await vehicle.save();
  }
  console.log('GPS UPDATE DONE');
}

export async function updateAssociatePayments() {
  const mainContracts = await MainContractSchema.find();
  const associatePayments = await AssociatePayments.find();
  console.log(mainContracts.length);
  let counter = 0;
  let counterChanged = 0;
  for (const main of mainContracts) {
    const findAssociatePayment = associatePayments.find(
      (assoc) => assoc.vehiclesId.toString() === main.stockId?.toString()
    );
    if (findAssociatePayment) {
      // console.log('TIENE MAIN CONTRACT PERO NO EXISTE EL ASSOCIATE PAYMENTS: ', main.contractNumber);
      // console.log('IDS: ', findAssociatePayment.vehiclesId.toString(), main.stockId?.toString());
    } else {
      const associate = await Associate.findById(main.associatedId);
      console.log('associate', associate?.email);
      console.log('NO TIENE ASSOCIATE PAYMENTS: ', main.contractNumber);
      const array = [...Array(156)];
      const newPaymentsArray = array.map((e) => ({
        ...e,
        payed: false,
        weeklyCost: main.weeklyRent,
        block: false,
      }));

      const vehicle = await StockVehicle.findById(main.stockId);

      const newAssociatePay = new AssociatePayments({
        vehiclesId: main.stockId,
        associateId: associate?._id,
        contractId: main._id,
        model: vehicle?.model,
        paymentsArray: newPaymentsArray,
        associateEmail: associate?.email,
        monexClabe: '',
        stpClabe: '',
      });
      console.log('new associate pay', newAssociatePay);
      // await newAssociatePay.save();
      // if (counterChanged < 1) {
      // console.log(newAssociatePay);
      // break;
      // }
      counterChanged = counterChanged + 1;
    }
    counter = counter + 1;
    // console.log(counter);
  }
  console.log('FINALIZADO');
  return;
}

export async function addSuscriptionIdToAssociatePayments(regionCode: string) {
  const associatePayments = await AssociatePayments.find({ region: regionCode });
  console.log('TOTAL associate payments', associatePayments.length);
  const gigConfig = getGigstackConfig(regionCode as any);

  const baseURL = 'http://localhost:3000/ocnpayments/gig/recurring/list';

  const url = new URL(baseURL);

  url.searchParams.append('regionCode', regionCode); // set the region code to update suscription
  // url.searchParams.append('limit', '100'); // uncoment if you want to limit the response
  url.searchParams.append('all', 'true'); // get all suscriptions recurresively from local request

  try {
    const { data: resRecurrent } = await axios.get(url.toString(), gigConfig);
    console.log('total response', resRecurrent.total);

    const { data } = resRecurrent; // gigstack suscriptions array
    const suscriptions = data;
    console.log('suscriptions.length', suscriptions.length, resRecurrent.startAfter);
    // console.log('get suscriptions', suscriptions.length);
    let counter = 0;

    for (const associatePayment of associatePayments) {
      // if (counter > 0) break;
      const associate = await Associate.findById(associatePayment.associateId);
      if (associate && associate.active && associatePayment.gigId && !associatePayment.suscriptionId) {
        const suscription = suscriptions.find((sus: any) => {
          console.log('sus.client.id', sus.client.id, associatePayment.gigId);
          return sus.client.id === associatePayment.gigId;
        });
        console.log('antes de verificar');
        if (suscription) {
          associatePayment.suscriptionId = suscription.id;
          associatePayment.version = 2;
          await associatePayment.save();
          const log = {
            gigId: associatePayment.gigId,
            suscriptionId: suscription.id,
            id: associatePayment._id,
          };
          console.log('change log', log);
          counter++;
        }
      }
    }

    console.log('TOTAL CHANGED', counter);
    console.log('FINISH');
  } catch (error) {
    console.log('ERROR', error);
  }
}

export function siguienteLunes(date: moment.Moment) {
  let fechaMoment = moment(date, 'YYYY-MM-DD');
  if (fechaMoment.day() === 1) return fechaMoment;

  if (fechaMoment.day() === 2 || fechaMoment.day() === 3) {
    return fechaMoment.clone().subtract(fechaMoment.day() === 2 ? 1 : 2, 'days');
  }

  let diasHastaLunes;

  if (fechaMoment.day() === 0) diasHastaLunes = 1;
  else diasHastaLunes = 1 + (7 - fechaMoment.day());

  let siguienteLunesv = fechaMoment.clone().add(diasHastaLunes, 'days');
  return siguienteLunesv;
}

export const generateArrayPayments = (date: moment.Moment, totalPayments: number) => {
  let lunes = siguienteLunes(date);

  let pagos = [{ day: lunes.format('DD-MM-YYYY'), number: 1 }];

  const total = totalPayments ? totalPayments : 155;
  for (let i = 1; i <= total; i++) {
    pagos.push({ day: lunes.add(1, 'week').format('DD-MM-YYYY'), number: i + 1 });
  }

  return pagos;
};

export const createMissedRecords = async () => {
  let today = moment('2023-10-02');

  const allPayments = generateArrayPayments(today, 128);

  const mainContract = new MainContractSchema({
    contractNumber: '1081-3',
    // associateId: '60c7f7b8d6c0f4001f3c3b4a',
    associatedId: '659717867226b3ee81291eb9',
    stockId: '64e4e7b60cdfc2c68435fe74',
    documentId: '659717e67226b3ee81291f85',
    allPayments,
    downPayment: 0,
    weeklyRent: 3348,
    totalPrice: 522288,
    isActive: true,
    finalPrice: 100000,
    deliveredDate: new Date('2023-10-02'),
  });

  // await mainContract.save();

  console.log('main contract created');

  const paymentsArray = mainContract.allPayments.map(() => ({
    weeklyCost: mainContract.weeklyRent,
    block: false,
  }));

  const associatePayment = new AssociatePayments({
    vehiclesId: '64e4e7b60cdfc2c68435fe74',
    associateId: '659717867226b3ee81291eb9',
    contractId: mainContract._id,
    model: 'MG5',
    paymentsArray,
    associateEmail: '<EMAIL>',
    monexEmail: '<EMAIL>',
    monexClabe: '112180066335306564',
    gigId: 'client_Hax396Mx1l',
  });

  console.log('associate payment created', associatePayment._id);
  // await associatePayment.save();

  console.log('associate payment created');
};

export const removeDuplicateVinRandomVin = async () => {
  const vehicles = await StockVehicle.find({ vin: 'safasfas' });
  console.log('total vehicles', vehicles.length);
  // generate random vin for each vehicle
  console.log('generating random vin');
  for (const vehicle of vehicles) {
    vehicle.vin = Math.random().toString(36).substring(2, 17).toUpperCase();
    console.log(vehicle.vin);
    // await vehicle.save();
  }

  console.log('removed duplicated');
};

export const removeSignContract = async () => {
  const datos = [
    // '2004',
    // '2005',
    '2020',
    '2066',
    '2076',
    '2077',
    '2078',
    '2079',
    '2080',
    '2081',
    '2082',
    '2083',
    '2084',
    '2085',
    '2086',
    '2087',
    '2088',
    '2089',
    '2091',
    '2092',
    '2093',
    '2094',
    '2095',
    '2096',
    '2097',
    '2098',
    '2099',
    '2100',
    '2101',
    '2102',
    '2103',
    '2104',
  ];

  const vehicles = await StockVehicle.find({ carNumber: { $in: datos } });

  for (const vehicle of vehicles) {
    const associate = await Associate.findById(vehicle.drivers[vehicle.drivers.length - 1]?._id);

    if (associate) {
      const unSginedContract = associate.signDocs?.contract;

      if (!unSginedContract) continue;

      const doc = await Document.findById(unSginedContract);

      if (doc) await doc.remove();

      if (associate.signDocs) associate.signDocs.contract = undefined;

      console.log('Vehicle: ', vehicle.carNumber);
      console.log(`associate ${associate.email} updated`);
      // await associate.save();

      // break;
    }
    console.log('Finalizado!');
  }
};
export const getLastAssociateOfActiveVehicles = async () => {
  // $or: [
  //   { status: 'active' },
  //   { status: 'legal-process' },
  //   { status: 'awaiting-insurance' },
  //   { status: 'in-service' },
  // ],
  const vehicles = await StockVehicle.find({
    status: { $in: ['active', 'legal-process', 'awaiting-insurance', 'in-service'] },
    'step.stepNumber': { $gte: 4 },
  }).populate('associates');

  console.log('total vehicles', vehicles.length);
  console.log('---------------------------------------------------------------------------------------');

  const associates: any[] = [];

  // for (const vehicle of vehicles) {
  //   // console.log('last driver', vehicle.drivers);

  //   const lastAssociate = vehicle.associates[vehicle.associates.length - 1];

  //   const associatePayments = await AssociatePayments.findOne({ associateId: lastAssociate?._id });
  //   // console.log('monex clabe', associatePayments?.monexClabe, vehicle.carNumber);

  //   const mainContract = await MainContractSchema.findOne({
  //     stockId: vehicle._id,
  //     associatedId: lastAssociate?._id,
  //   });

  //   const contractNumber =
  //     vehicle.carNumber + (vehicle.extensionCarNumber ? `-${vehicle.extensionCarNumber}` : '');

  //   const data = {
  //     _id: lastAssociate?._id,
  //     email: lastAssociate?.email,
  //     phone: lastAssociate?.phone,
  //     name: lastAssociate?.firstName,
  //     lastName: lastAssociate?.lastName,
  //     legal_name: `${lastAssociate?.firstName} ${lastAssociate?.lastName}`.toUpperCase(),
  //     rfc: lastAssociate?.rfc,
  //     zip: lastAssociate?.postalCode,
  //     monexClabe: associatePayments?.monexClabe,
  //     region: vehicle.vehicleState.toUpperCase(),
  //     associateId: lastAssociate?._id,
  //     gigId: associatePayments?.gigId,
  //     vehicleStatus: vehicle.status,
  //     vehicleNumber: vehicle.carNumber,
  //     contractNumber,
  //     weeklyRent: associatePayments?.paymentsArray[0]?.weeklyCost,
  //     totalPayments: associatePayments?.paymentsArray.length,
  //     model: vehicle.model,
  //     brand: vehicle.brand,
  //     downPayment: mainContract?.downPayment,
  //     lastPaymentDate: mainContract?.allPayments[mainContract?.allPayments.length - 1]?.day,
  //   };
  //   // console.log('data', data);

  //   if (lastAssociate) associates.push(data);

  //   // const associate = await Associate.findById(vehicle.drivers[vehicle.drivers.length - 1]?._id);
  //   // if (associate) {
  //   //   console.log('Vehicle: ', vehicle.carNumber);
  //   //   console.log(`associate ${associate.email}`);
  //   // }
  // }

  await Promise.allSettled(
    vehicles.map(async (vehicle) => {
      const lastAssociate = vehicle.associates[vehicle.associates.length - 1];
      const associatePayments = await AssociatePayments.findOne({ associateId: lastAssociate?._id });
      const mainContract = await MainContractSchema.findOne({
        stockId: vehicle._id,
        associatedId: lastAssociate?._id,
      });

      const contractNumber =
        vehicle.carNumber + (vehicle.extensionCarNumber ? `-${vehicle.extensionCarNumber}` : '');

      const data = {
        _id: lastAssociate?._id,
        email: lastAssociate?.email,
        phone: lastAssociate?.phone,
        name: lastAssociate?.firstName,
        lastName: lastAssociate?.lastName,
        legal_name: `${lastAssociate?.firstName} ${lastAssociate?.lastName}`.toUpperCase(),
        rfc: lastAssociate?.rfc,
        zip: lastAssociate?.postalCode,
        monexClabe: associatePayments?.monexClabe,
        region: vehicle.vehicleState.toUpperCase(),
        associateId: lastAssociate?._id,
        gigId: associatePayments?.gigId,
        vehicleStatus: vehicle.status,
        vehicleNumber: vehicle.carNumber,
        contractNumber,
        weeklyRent: associatePayments?.paymentsArray[0]?.weeklyCost,
        totalPayments: associatePayments?.paymentsArray.length,
        model: vehicle.model,
        brand: vehicle.brand,
        downPayment: mainContract?.downPayment,
        lastPaymentDate: mainContract?.allPayments[mainContract?.allPayments.length - 1]?.day,
      };

      if (lastAssociate) associates.push(data);
    })
  );

  return associates;

};

export const populateClientIdToAssociate = async () => {
  try {
    const result = await axios.get(`${PAYMENTS_API_URL}/clients`, {
      headers: {
        Authorization: `Bearer ${process.env.PAYMENTS_API_KEY}`,
      },
    });

    const clients = result.data.data as any[];
    console.log('total clients', clients.length);

    // if (clients.length > 5) {
    //   clients.slice(0, 100).map((e: any) => console.log(e.contractNumber, e.associateId));
    //   return console.log('stop execution');
    // }

    const totalUpdated: any[] = [];
    const alreadyExist: any[] = [];
    for (const client of clients) {
      const associate = await Associate.findOne({
        _id: client.associateId /* , clientId: { $exists: false } */,
      });

      if (associate && associate.clientId) {
        alreadyExist.push(associate.email);
        console.log('already exist', associate.email);
        continue;
      }

      if (associate && !associate.clientId) {
        associate.clientId = client.id;
        // await associate.save();
        console.log('associate updated', associate.email);
        totalUpdated.push(associate.email);
      }
    }
    console.log(
      'total updated',
      totalUpdated.length,
      totalUpdated.map((e) => e.email)
    );
    console.log('already exist', alreadyExist.length, alreadyExist);
  } catch (error: any) {
    console.log('error', error);
  }
};

// let xlsx: any;

// function excelDateToJSDate(serial: number) {
//   const utcDays = Math.floor(serial - 25569);
//   const utcValue = utcDays * 86400;
//   const dateInfo = new Date(utcValue * 1000);

//   const fractionalDay = serial - Math.floor(serial) + 0.0000001;

//   let totalSeconds = Math.floor(86400 * fractionalDay);

//   const seconds = fractionalDay % 60;

//   totalSeconds -= seconds;

//   const hours = Math.floor(totalSeconds / (60 * 60));
//   const minutes = Math.floor(totalSeconds / 60) % 60;

//   return new Date(dateInfo.getFullYear(), dateInfo.getMonth(), dateInfo.getDate(), hours, minutes, seconds);
// }

function excelDateToJSDate(serial: number) {
  const utcDays = Math.floor(serial - 25569);
  const dateInfo = new Date(utcDays * 86400 * 1000);

  const fractionalDay = serial - Math.floor(serial) + 0.0000001;
  let totalSeconds = Math.floor(86400 * fractionalDay);
  const seconds = totalSeconds % 60;
  totalSeconds -= seconds;
  const hours = Math.floor(totalSeconds / (60 * 60));
  const minutes = Math.floor(totalSeconds / 60) % 60;

  dateInfo.setUTCHours(hours);
  dateInfo.setUTCMinutes(minutes);
  dateInfo.setUTCSeconds(seconds);

  return new Date(dateInfo.getUTCFullYear(), dateInfo.getUTCMonth(), dateInfo.getUTCDate());
}

export const transformData = async () => {
  const filePath = path.join(__dirname, '../data.xlsx');
  console.log('filePath', filePath);
  const workbook = xlsx.readFile(filePath);
  // console.log('file', file);

  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];

  // Convertir la hoja de trabajo a JSON
  const data = xlsx.utils.sheet_to_json(worksheet);

  const relevantData = data.map((item: any) => ({
    vin: item.Vin.trim().toUpperCase(),
    billDate: excelDateToJSDate(item['Fecha de factura ']),
  }));

  // console.log('EXCEL DATA ', data);
  // console.log('relevant data', relevantData);
  const transformedData = data.map((item: any) => item.Vin.trim().toUpperCase());
  console.log('EXCEL DATA LENGTH', transformedData.length);


  // apply regex and in operator to find stock vehicles
  const orQuery = transformedData.map(vin => ({ vin: { $regex: `^\\s*${vin}\\s*$`, $options: 'i' } }));
  const stockVehicles = await StockVehicle.find({
    $or: orQuery,
  });

  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    const vin = vehicle.vin;
    const vinFixed = vin.trim().toUpperCase();

    if (vin !== vinFixed) {
      // console.log('VIN BEFORE', vin, vin.length);
      vehicle.vin = vinFixed;
      // console.log('VIN AFTER', vinFixed, vinFixed.length);
      // console.log('--------------------------------')
      // await vehicle.save();
    }

    const found = relevantData.find((e) => e.vin === vinFixed);

    if (found) {
      // vehicle.billDate = new Date(billDate);
      // console.log('bill date', found, vinFixed);

      const billDate = found.billDate.toISOString().split('T')[0];
      // console.log('bill date', billDate);
      vehicle.billDate = billDate;

    }

    // await vehicle.save();

  }));


  console.log('stock vehicles LENGTH', stockVehicles.length);


  // const transformedData = data.map((item: any) => ({
  //   carNumber: item['Contrato / Contract'].toString().split('.')[0],
  // }));

  // const outputFilePath = path.join(__dirname, '../contractNumbers.json');
  // // fs.writeFileSync(outputFilePath, JSON.stringify(jsonData, null, 2));
  // fs.writeFileSync(outputFilePath, JSON.stringify(transformedData, null, 2));

  // console.log('Archivo XLSX convertido a JSON exitosamente.');
};


export const getRecordsWithoutBillDate = async () => {
  const vehicles = await StockVehicle.find({ billDate: { $exists: false } });

  console.log('vehicles without bill date', vehicles.length);
  vehicles.forEach((vehicle) => {
    console.log('vehicle', vehicle.carNumber, vehicle.vin);
  });

  // transform this data to excel

  const data = vehicles.map((vehicle) => ({
    carNumber: vehicle.carNumber,
    vin: vehicle.vin,
  }));

  const ws = xlsx.utils.json_to_sheet(data);
  const wb = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(wb, ws, 'Sheet1');
  xlsx.writeFile(wb, path.join(__dirname, '../vehiclesWithoutBillDate.xlsx'));

}

export const transformDataParam = async (
  relativeFilePath: string, fileOutputPath: string, params: any = {}) => {
  console.log('params', params, fileOutputPath);
  const filePath = path.join(__dirname, relativeFilePath);
  console.log('filePath', filePath);
  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];

  const data = xlsx.utils.sheet_to_json(worksheet);



  // const transformArray = data.map((item: any) => {
  //   return {
  //     carNumber: item.__EMPTY.toString(),
  //   }
  // })

  const transformArray2 = await Promise.allSettled(data.map(async (item: any) => {
    const carNumber = item.__EMPTY.toString();
    const vehicle = await StockVehicle.findOne({ carNumber }).lean();
    // console.log('return data', {
    //   carNumber,
    //   _id: vehicle?._id,
    // })
    return {
      carNumber,
      _id: vehicle?._id?.toString(),
    };
  }));
  // console.log('transformArray', transformArray);
  const transformArray = transformArray2.map((e: any) => e.value);
  console.log('transformArray', transformArray);

  const outputFilePath = path.join(__dirname, fileOutputPath);
  fs.writeFileSync(outputFilePath, JSON.stringify(transformArray, null, 2));
  console.log('Archivo XLSX convertido a JSON exitosamente.');
}

// const notFound = contractNumbers.filter((e) => !vehicles.map((v) => v.carNumber).includes(e));


// await Promise.allSettled(vehicles.map(async (vehicle) => {

//   console.log('vehicle', vehicle.transferredTo)

//   /* if (vehicle.transferredTo){
//     jsonData.push({
//       carNumber: vehicle.carNumber,
//       transferredTo: vehicle.transferredTo,
//     });
//   } */

//   vehicle.transferredTo = 'En Revisión Documental'
//   await vehicle.save();
// }));


// const outputFilePath2 = path.join(__dirname, '../endoso.data.json');
// fs.writeFileSync(outputFilePath2, JSON.stringify(jsonData, null, 2));

// console.log('FINISH ---------------------------------------------------------')

// const result = await Promise.allSettled(vehicles.map(async (v) => {
//   const associate = await Associate.findById(v.drivers[v.drivers.length - 1]?._id);
//   const mainContract = await MainContractSchema.findOne({ stockId: v._id, associatedId: associate?._id });
//   if (associate) {
//     // create complete address with addressStreet, interior, exterior, colony, city postalCode, etc

//     let addressStreet = associate.addressStreet;
//     const exterior = associate.exterior.toString();
//     const interior = associate.interior ? associate.interior.toString() : '';

//     let domicilio;
//     if (addressStreet.includes(exterior) && addressStreet.includes(interior)) {
//       // Si addressStreet ya contiene exterior e interior, no los añadimos
//       domicilio = `${addressStreet} ${associate.colony} ${cities[associate.city].label} ${associate.postalCode}`;
//     } else {
//       // Si no, los añadimos
//       domicilio = `${addressStreet} ${exterior} ${interior} ${associate.colony} ${cities[associate.city].label} ${associate.postalCode}`;
//     }

//     let fecha = mainContract?.deliveredDate ? mainContract?.deliveredDate.toISOString().split('T')[0] : v.deliveredDate[v.deliveredDate.length - 1];

//     // parse fecha to make like 16 de septiembre 2024

//     // fecha = moment(fecha).format('DD [de] MMMM [de] YYYY');
//     const contractNumber = v.carNumber + (v.extensionCarNumber ? `-${v.extensionCarNumber}` : '');
//     console.log('contractNumber', contractNumber);

//     const obj = {
//       // nombre: (associate.firstName + ' ' + associate.lastName).replace(/\t/g, ' '),
//       nombre: capitalizeWords(`${associate.firstName} ${associate.lastName}`),
//       fecha,
//       domicilio: capitalizeWords(domicilio),
//       estado: cities[associate.city].label,
//       zip: associate.postalCode,
//       contractNumber,
//       totalPrice: mainContract?.totalPrice,
//       email: associate.email,
//       status: v.status,
//       step: v.step,
//     };

//     relevantData.push(obj);
//     // console.log('associate', associate.email, obj);
//   }
// }));

// transformData();
// let jsonData: any[] = [];

const cities: { [key: string]: { label: string; value: string } } = {
  cdmx: { label: 'Ciudad De México', value: 'cdmx' },
  edomx: { label: 'Estado De México', value: 'edomx' },
  gdl: { label: 'Guadalajara', value: 'gdl' },
  mty: { label: 'Monterrey', value: 'mty' },
  pbc: { label: 'Puebla', value: 'puebla' },
  qro: { label: 'Querétaro', value: 'qro' },
  tij: { label: 'Tijuana', value: 'tij' },
  moka: { label: 'Moka', value: 'moka' },
  pbe: { label: 'Puebla', value: 'pbe' },
  tol: { label: 'Toluca', value: 'tol' },
  ptv: { label: 'Puerto Vallarta', value: 'ptv' },
  tep: { label: 'Tepic', value: 'tep' },
  col: { label: 'Colima', value: 'col' },
  sal: { label: 'Saltillo', value: 'sal' },
  torr: { label: 'Torreon', value: 'torr' },
  dur: { label: 'Durango', value: 'dur' },
  mxli: { label: 'Mexicali', value: 'mxli' },
  her: { label: 'Hermosillo', value: 'her' },
  chi: { label: 'Chihuaha', value: 'chi' },
  leo: { label: 'León', value: 'leo' },
  ags: { label: 'Aguas Calientes', value: 'ags' },
  slp: { label: 'San Luis Potosí', value: 'slp' },
  mer: { label: 'Mérida', value: 'mer' },
};

function getAddress(associate: any) {
  let addressStreet = associate.addressStreet;
  const exterior = associate.exterior.toString();
  const interior = associate.interior ? associate.interior.toString() : '';

  let domicilio;
  if (addressStreet.includes(exterior) && addressStreet.includes(interior)) {
    // Si addressStreet ya contiene exterior e interior, no los añadimos
    domicilio = `${addressStreet} ${associate.colony} ${cities[associate.city].label} ${associate.postalCode}`;
  } else {
    // Si no, los añadimos
    domicilio = `${addressStreet} ${exterior} ${interior} ${associate.colony} ${cities[associate.city].label} ${associate.postalCode}`;
  }

  return capitalizeWords(domicilio);
}


export async function shortGetData() {

  const dataxlsx = path.join(__dirname, '../short-data.xlsx');

  const workbook = xlsx.readFile(dataxlsx);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const data = xlsx.utils.sheet_to_json(worksheet, { raw: false });

  // get the data from the json file that it is in the same directory of this file
  console.log('data', data.length)

  // const root = process.cwd();
  // const data = require(path.join(root, 'contracts.json'));
  // console.log('data length', data.length);

  const carNumbers = data.map((item: any) => item.Contrato.replace(',', '').replace('.', '-').split('-')[0]);
  // console.log('carNumbers', carNumbers)
  console.log('carNumbers length', carNumbers.length /* carNumbers */);

  const stockVehicles = await StockVehicle.find({ carNumber: { $in: carNumbers } });
  console.log('stock vehicles', stockVehicles.length);
  const jsonData: any[] = [];

  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    const associate = await Associate.findById(vehicle.drivers[vehicle.drivers.length - 1]?._id);
    if (!associate) {
      console.log('no associate', vehicle.carNumber);
      return
    };

    // const extensionCarNumberIndex = vehicle.extensionCarNumber ? vehicle.extensionCarNumber - 1 : 0;

    const contractNumber = vehicle.extensionCarNumber ? `${vehicle.carNumber}-${vehicle.extensionCarNumber}` : vehicle.carNumber;

    const mainContract = await MainContractSchema.findOne(
      { stockId: vehicle._id, associatedId: associate._id, contractNumber });

    if (!mainContract) {
      console.log('no main contract', vehicle.carNumber);
      return;
    };

    const associatepayments = await AssociatePayments.findOne({
      contractId: mainContract._id,
      $or: [{ associateId: associate._id }, { associateEmail: associate.email }],
    });

    if (!associatepayments) {
      console.log('no associate payments', vehicle.carNumber);
      return;
    };

    const obj = {
      domicilio: getAddress(associate),
      nombre: `${associate.firstName} ${associate.lastName}`,
      fechaEntrega: mainContract.deliveredDate.toISOString().split('T')[0],
      contractNumber,
      clabe: associatepayments.monexClabe,
      cesionDate: '2024-10-31',
      email: associate.email,
    }

    jsonData.push(obj);

  }));

  console.log('jsonData', jsonData.length);

  const output = path.join(__dirname, '../short-data.json');
  fs.writeFileSync(output, JSON.stringify(jsonData, null, 2));
  console.log('finish');


}

export async function getData() {
  // const data = jsonData;
  // console.log('json data', data.length);
  // return data;

  const dataxlsx = path.join(__dirname, '../data.xlsx');

  const workbook = xlsx.readFile(dataxlsx);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  // // console.log('worksheet', worksheet);
  const data = xlsx.utils.sheet_to_json(worksheet, { raw: false });
  // const root = process.cwd();
  // const data: string[] = require(path.join(root, 'contracts.json'));
  // console.log('data length', data.length);
  // console.log('total data', data, data.length);


  const contractNumbers = data.map((item: any) => item.Contrato.replace(',', '').replace('.', '-').split('-')[0]);
  // contractNumbers.forEach((e) => {
  //   console.log('contract number', e);
  // });
  console.log('contractNumbers', /* contractNumbers, */ contractNumbers.length);
  const fields = data.map((item: any) => {
    return {
      ...item,
      // carNumber: item.LOAN?.replace(',', '').split('-')[0],
      // carNumber: item['Loan ID']?.replace(',', '').replace('.', '-').split('-')[0],
      // extensionCarNumber: item['Loan ID']?.replace(',', '').replace('.', '-').split('-')[1],
      carNumber: item.Contrato?.replace(',', '').replace('.', '-').split('-')[0],
      extensionCarNumber: item.Contrato?.replace(',', '').replace('.', '-').split('-')[1],
      date: item.__EMPTY,
    }
  });



  // const removeRepeat = contractNumbers.filter((e, i) => contractNumbers.indexOf(e) === i);
  // console.log('remove repeat', removeRepeat.length);
  // const removeRepeat = new Set(contractNumbers);

  // console.log('remove repeat', removeRepeat.size, removeRepeat);

  // contractNumbers.forEach((e) => {
  //   console.log('contract number', e);
  // });
  // return;

  const vehicles = await StockVehicle.find({ carNumber: { $in: contractNumbers } }
  ).select({
    updateHistory: 0,
    policiesArray: 0,
    tenancy: 0,
    oldDocuments: 0,
    readmissionReason: 0,
    adendumServiceCount: 0,
    readmissionDate: 0,
    deliveredDate: 0,
    paymentCount: 0,
    km: 0,
    mi: 0,
    gpsInstalled: 0,
    gpsNumber: 0,
    gpsSerie: 0,
    circulationCard: 0,
    carPlates: 0,
  })
    ;
  console.log('vehicles length', vehicles.length);
  // oder by contract number

  /*   vehicles.sort((a, b) => {
      // return contractNumbers.indexOf(a.carNumber) - contractNumbers.indexOf(b.carNumber);
      return +a.carNumber - +b.carNumber;
    }); */


  // // save vehicles info in a json file
  //   const toJson: any[] = [];

  //   await Promise.allSettled(vehicles.map(async (v) => {

  //     const foundField = fields.find((e) => e.carNumber === v.carNumber);

  //     if (!foundField) return;

  //     const extensionCarNumberIndex = foundField.extensionCarNumber ? foundField.extensionCarNumber - 1 : 0;
  //     // const extensionCarNumber = foundField.extensionCarNumber;

  //     const associate = await Associate.findById(v.drivers[extensionCarNumberIndex]?._id);
  //     if(!associate) return;
  //     // v.save
  //     const mainContract = await MainContractSchema.findOne({
  //       stockId: v._id,
  //       contractNumber: v.extensionCarNumber ? `${v.carNumber}-${v.extensionCarNumber}` : v.carNumber,
  //       $or: [{ associateId: associate._id }, { associateEmail: associate.email }] ,
  //     });
  //     if (!mainContract) return;

  //     const obj = {
  //       // ...v,
  //       carNumber: v.carNumber,
  //       contractNumber: v.extensionCarNumber ? `${v.carNumber}-${v.extensionCarNumber}` : v.carNumber,
  //       deliveredDate: mainContract.deliveredDate.toISOString().split('T')[0],
  //       totalPrice: mainContract.totalPrice,
  //       name: `${associate.firstName} ${associate.lastName}`,
  //       // mainContract,
  //     }

  //     toJson.push(obj);

  //   }));

  //   const outputbackup = path.join(__dirname, '../vehicles.backup.json');
  //   const toJsonSorted = toJson.sort((a, b) => +a.carNumber - +b.carNumber);
  //   fs.writeFileSync(outputbackup, JSON.stringify(toJsonSorted, null, 2));
  //  return;

  // UPDATE/FIX VEHICLES DATA -----------------------------------------------------------------------

  // let index = 0;
  // for(const v of vehicles){

  //   const fieldFound = fields.find((e) => e.carNumber === v.carNumber);

  //   if(fieldFound){
  //     // console.log('fieldFound', fieldFound);

  //     const excelDate = fieldFound?.date;
  //     // console.log('excelDate', excelDate);

  //     const [month, day, year]: string[] = excelDate.split('/');
  //     // console.log('month', year)
  //     // console.log('date', `20${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`);
  //     const date = new Date(`20${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`);
  //     // console.log('date', date.toISOString().split('T')[0], fieldFound.carNumber);

  //     // const extensionCarNumber = v.extensionCarNumber;

  //     const extensionCarNumberIndex = fieldFound.extensionCarNumber ? fieldFound.extensionCarNumber - 1 : 0;
  //     // const extensionCarNumber = fieldFound.extensionCarNumber;
  //     // console.log('extensionCarNumber', extensionCarNumberIndex, fieldFound.carNumber);

  //     const associate = await Associate.findById(v.drivers[extensionCarNumberIndex]?._id);

  //     if(!associate){
  //       // index++;
  //       continue;
  //     }      

  //     const contractNumber = v.extensionCarNumber ? `${v.carNumber}-${v.extensionCarNumber}` : v.carNumber;
  //     const mainContract = await MainContractSchema.findOne({
  //       stockId: v._id,
  //       // deliveredDate: date,
  //       contractNumber,
  //       or: [{ associateId: associate?._id }, { associateEmail: associate?.email }],
  //     });
  //     if(!mainContract){
  //       // index++;
  //       continue;
  //     }
  //     // if (mainContract){
  //       // console.log('deliveredDate', mainContract?.deliveredDate.toISOString(), mainContract?.totalPrice, v.carNumber);
  //     // }
  //     // const isSameDate = moment.utc(date) === moment.utc(v.


  //     // update part

  //     const objUpdate: any = {};
  //     console.log('mainContract', mainContract?.contractNumber, mainContract?.deliveredDate, v.deliveredDate[extensionCarNumberIndex] || v.deliveredDate[v.deliveredDate.length - 1]);
  //     const deliveredDate = mainContract?.deliveredDate.toISOString().split('T')[0];
  //     const foundDate = date.toISOString().split('T')[0];
  //     if (deliveredDate !== foundDate){
  //       console.log('The delivered date is different: -->', v.carNumber, deliveredDate, '|', foundDate);
  //       // objUpdate.deliveredDate = date;
  //       objUpdate.deliveredDate = date;
  //     }

  //     const mount = +fieldFound.Monto.replace('$', '').replace(',', '');
  //     if(mainContract.totalPrice !== mount){
  //       console.log('The total price is different: -->', v.carNumber, mainContract.totalPrice, '|', mount);
  //       objUpdate.totalPrice = mount;
  //     }


  //     if(Object.keys(objUpdate).length > 0){
  //       console.log('updating', v.carNumber, objUpdate);

  //       if('deliveredDate' in objUpdate){

  //         v.deliveredDate[extensionCarNumberIndex] = date.toISOString().split('T')[0];
  //         await v.save();

  //       }

  //       await MainContractSchema.updateOne({ _id: mainContract._id }, {
  //         $set: objUpdate,
  //       });
  //     }

  //     console.log('---------------------------------------');

  //     /* if(index > 1){
  //       return;
  //     } */
  //   }
  //   // index++;
  // }


  // find those contract numbers that are not in the database but they are in the excel file

  // const notFound = contractNumbers.filter((e) => !vehicles.map((v) => v.carNumber).includes(e));


  // find repeated contract numbers in the excel file

  const repeated = contractNumbers.filter((e, i) => contractNumbers.indexOf(e) !== i);
  console.log('repeated', repeated.length, repeated);


  const relevantData: any[] = [];
  const newDataIndex: any[] = [];
  await Promise.allSettled(vehicles.map(async (v, index) => {
    try {

    const associate = await Associate.findById(v.drivers[v.drivers.length - 1]._id).select('+digitalSignature');

    const mainContract = await MainContractSchema.findOne({ stockId: v._id, associatedId: associate?._id });

    if (!associate || !mainContract) {
      console.log('no associate or main contract', v.carNumber);
      console.log('associate', associate);
      console.log('mainContract', mainContract);
      console.log('---------------------------------------')
      throw new Error('no associate or main contract');
      return;
    };

    // console.log('db date', mainContract.deliveredDate, mainContract.deliveredDate.toISOString().split('T')[0]);

    const dateParsed = moment.utc(mainContract.deliveredDate).format('DD [de] MMMM [de] YYYY');
    // console.log('dateParsed', dateParsed);
    // console.log('deliveredDate', mainContract.deliveredDate, mainContract.deliveredDate.toISOString().split('T')[0]);

    const addressStreet = associate.addressStreet;
    const exterior = associate.exterior.toString();
    const interior = associate.interior ? associate.interior.toString() : '';


    let domicilio;

      // console.log('associate city, and label', v.carNumber, associate.city, associate.colony, cities[associate.city]?.label, associate.state, v.state);

      const city = cities[associate.city]?.label || associate.city;

    if (addressStreet.includes(exterior) && addressStreet.includes(interior)) {
      // Si addressStreet ya contiene exterior e interior, no los añadimos
      domicilio = `${addressStreet} ${associate.colony} ${city} ${associate.postalCode}`;
    } else {
      // Si no, los añadimos
      domicilio = `${addressStreet} ${exterior} ${interior} ${associate.colony} ${city} ${associate.postalCode}`;
    }

    const associatePayments = await AssociatePayments.findOne(
      // { associateId: associate._id });
      // make an or query to find associate payments by associateId or associateId2
      { $or: [{ associateId: associate._id }, { associateEmail: associate.email }] });

    if (!associatePayments) {
      console.log('associatePayments not found', v.carNumber, associate.firstName, associate.lastName);
      console.log('---------------------------------------')
      throw new Error('associatePayments not found');
      return;
    }


    const newData = {
      nombre: `${associate.firstName} ${associate.lastName}`,
      fecha: mainContract.deliveredDate.toISOString().split('T')[0],
      domicilio: capitalizeWords(domicilio),
      fechaEntrega: mainContract.deliveredDate.toISOString().split('T')[0],
      fechaTexto: dateParsed,
      contractNumber: v.extensionCarNumber ? `${v.carNumber}-${v.extensionCarNumber}` : v.carNumber,
      totalPrice: mainContract.totalPrice,
      email: associate.email,
      zip: associate.postalCode,
      cesionDate: fields.find((e: any) => e.carNumber === v.carNumber)?.date,
      clabe: associatePayments?.monexClabe || 'no definido',
      estado: cities[associate.city]?.label || associate.state,
    }
      // console.log('new data for element: ', index, newData);

      newDataIndex[index] = newData;

    relevantData.push(newData);
    } catch (error: any) {
      console.log('error', error);
    }

  }));

  // console.log('New data index', newDataIndex);
  newDataIndex.forEach((e, i) => {
    console.log('New data for index: ', e.contractNumber, i);
  });

  // save relevant data to xlsx file

  // const ws = xlsx.utils.json_to_sheet(relevantData);
  // const wb = xlsx.utils.book_new();
  // xlsx.utils.book_append_sheet(wb, ws, 'Sheet1');
  // xlsx.writeFile(wb, path.join(__dirname, '../pagares.xlsx'));

  // console.log('total relevant data', relevantData.length, 'Result length');
  // console.log('total relevant data', relevantData.length, 'Result length');

  // create json file with relevant data
  console.log('relevantData', relevantData.length, '-------------------');
  const outputFilePath = path.join(__dirname, '../endoso.data.json');
  fs.writeFileSync(outputFilePath, JSON.stringify(relevantData, null, 2));

  // console.log('Archivo JSON creado exitosamente.');

  // Filter missing data, relevantData sometimes does not have all the data from vehicles length, so we need to filter it and get those that are missing
  // const missingData = vehicles.
  //   filter((v) => !relevantData.map((e) => e.contractNumber).includes(v.carNumber));


  // MissingData array should bring the vehicle and associate data, only the required data used to create the endoso.data.json
  // make it using Promise all to fetch de associate to get associate data
  const missingData = [] as any[];
  await Promise.allSettled(vehicles.map(async (v) => {
    if (!relevantData.map((e) => e.contractNumber).includes(v.carNumber)) {
      const associate = await Associate.findById(v.drivers[v.drivers.length - 1]?._id).select({
        firstName: 1,
        lastName: 1,
        email: 1,
        phone: 1,
        postalCode: 1,
        city: 1,
        state: 1,
        addressStreet: 1,
        exterior: 1,
        interior: 1,
        colony: 1,
        delegation: 1,
      });
      if (!associate) return;
      missingData.push({
        carNumber: v.carNumber,
        extensionCarNumber: v.extensionCarNumber,
        associate: associate,
      });
    }
  }));



  console.log('missingData', missingData.length, '-------------------');




  // save it to a json file
  const root = process.cwd();
  const outputFilePath2 = path.join(root, 'missing.data.json');
  fs.writeFileSync(outputFilePath2, JSON.stringify(missingData, null, 2));


}

const capitalizeWords = (str: string) => {
  return str
    .replace(/\t/g, ' ')    // Reemplaza las tabulaciones con espacios
    .replace(/\s+/g, ' ')   // Reemplaza múltiples espacios con un solo espacio
    .trim()                 // Elimina espacios al inicio y al final
    .toLowerCase()       // Convierte todo a minúsculas
    // .replace(/\b\w/g, char => char.toUpperCase()); // Capitaliza la primera letra de cada palabra
    .replace(/(?:^|\s)\S/g, char => char.toUpperCase());
};

export async function updateUserRestrictions() {

  // const usersId = ['668833e6181c63e6935364d8', '65caa212df029bfd248e2fba', '6668b58a4a3f8794d7df3790', '6643e4478fb7a01c75353a62', '65caa19bdf029bfd248e2fb6', '65caa113df029bfd248e2fb2'];

  const xlsxPath = path.join(__dirname, '../data.xlsx');
  const workbook = xlsx.readFile(xlsxPath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];

  const data = xlsx.utils.sheet_to_json(worksheet);

  // const root = process.cwd();
  // const data = require(path.join(root, 'contracts.json'));
  // console.log('data length', data.length);

  const newRestrictions = data.map((item: any) => (item.Contrato || item).toString().replace('.', '-').split('-')[0]);

  // console.log('new restrictions', newRestrictions.length /* newRestrictions */);

  const auditorUsers = await User.find({ role: 'auditor', email: { $regex: '<EMAIL>' } });

  console.log('total auditor users', auditorUsers.length);
  // auditorUsers.forEach((user) => {
  //   console.log('user', user.email);
  // });
  const usersId = auditorUsers.map((user) => user._id.toString());


  const userRestrictions = await UserVehicleRestrictions.find({ userId: { $in: usersId } });

  console.log('total user restrictions', userRestrictions.length);


  // add new restrictions to users restrictions, without losing the previous restrictions

  console.log('new restrictions', newRestrictions.length);

  const stockVehicles = await StockVehicle.find({ carNumber: { $in: newRestrictions } })
    .select({
      carNumber: 1,

    })
  console.log('stock vehicles', stockVehicles.length);


  // merge new restrictions with previous restrictions of each user but without duplicates
  // save changes of each user

  const firstRestriction = userRestrictions[0];
  console.log('BEFORE: ', firstRestriction.stockRestrictions.length)

  const carNumber = firstRestriction.stockRestrictions.map((e) => e.carNumber);
  const news = newRestrictions.filter((e: any) => !carNumber.includes(e));

  const newStockVehicles1 = stockVehicles.filter((e) => news.includes(e.carNumber));
  console.log('NEW ADDED: ', newStockVehicles1.length);
  const merged = [...firstRestriction.stockRestrictions, ...newStockVehicles1];

  console.log('merged', merged.length);
  // return;
  await Promise.allSettled(userRestrictions.map(async (restriction) => {
    // console.log('restriction', restriction.userId);


    // const user = await User.findById(restriction.userId);
    // if (!user) return;

    // const carNumbers = restriction.stockRestrictions.map((e) => e.carNumber);
    // console.log('[stockRestrictions] 1 before', carNumbers.length, user.email);
    // const newCarNumbers = newRestrictions.filter((e: any) => !carNumbers.includes(e));

    // // console.log('newCarNumbers', newCarNumbers.length);

    // const newStockVehicles = stockVehicles.filter((e) => newCarNumbers.includes(e.carNumber));

    // // console.log('newStockVehicles', newStockVehicles);

    // const newMergedRestrictions = [...restriction.stockRestrictions, ...newStockVehicles];

    // console.log('[stockRestrictions] 2 after', newMergedRestrictions.length, user.email);

    restriction.stockRestrictions = merged;
    console.log('stockRestrictions', restriction.stockRestrictions.length);
    console.log('-------------------------------------------------------------------------')
    // await restriction.save();
  }));
  console.log('finish');



}

export const transformData2 = async () => {
  const filePath = path.join(__dirname, '../data.xlsx');
  console.log('filePath', filePath);
  const workbook = xlsx.readFile(filePath);
  // console.log('file', file);

  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];

  // Convertir la hoja de trabajo a JSON
  const data = xlsx.utils.sheet_to_json(worksheet);

  // console.log('data', data)

  const transformArray = data.map((item: any) => item.loan_id.toString().split('.')[0]);
  console.log('ttransformArray', transformArray.length);

  const stockVehicles = await StockVehicle.find({
    carNumber: { $in: transformArray },
    $or: [{ transferredTo: null }, { transferredTo: undefined }],
  });

  console.log('stock vehicles', stockVehicles.length);

  const withoutTransferredTo = stockVehicles.filter((vehicle) => !vehicle.transferredTo);
  await Promise.allSettled(withoutTransferredTo.map(async (vehicle) => {
    console.log('vehicle transferredTo', vehicle.transferredTo);
    vehicle.transferredTo = "i80-1"
    // await vehicle.save();
  }));

  console.log('saved vehicles');

};

export async function removei80TransferredToFromExcel() {
  const filePath = path.join(__dirname, '../removei80.xlsx');
  const workbook = xlsx.readFile(filePath);

  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];

  const data = xlsx.utils.sheet_to_json(worksheet);

  const transformArray = data.map((item: any) => item.Vin.trim());

  console.log('transformArray', transformArray.length);

  const stockVehicles = await StockVehicle.find({
    vin: { $in: transformArray },
    transferredTo: 'i80-1',
  });

  console.log('stock vehicles', stockVehicles.length);

  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    console.log('vehicle transferredTo', vehicle.transferredTo);
    vehicle.transferredTo = undefined;
    await vehicle.save();
  }));

}



export const fixTempSuscriptionPayments = async () => {

  try {
    const today = generateDateNotUTC({ currentTime: true })
    console.log('today', today);
    const tempItems = await TempSuscriptionPayments.find({ status: 'pending', stockId: '64e3e69e0cdfc2c684350bf6' });

    console.log('total temp items', tempItems.length);

    await Promise.allSettled(tempItems.map(async (item) => {
      // const associate = await Associate.findById(item.associateId);
      // console.log('item', item.associateId);
      const stockVehicle = await StockVehicle.findById(item.stockId);
      if (!stockVehicle) return;

      const lastAssociate = stockVehicle.drivers[stockVehicle.drivers.length - 1];
      // console.log('last associate', lastAssociate?._id.toString(), item.associateId.toString());
      // console.log('IS SAME ASSOCIATE', lastAssociate?._id.toString() === item.associateId.toString(), 'contract number', stockVehicle.carNumber + (stockVehicle.extensionCarNumber ? `-${stockVehicle.extensionCarNumber}` : ''));

      if (lastAssociate?._id.toString() !== item.associateId.toString()) { // if associate is different
        item.status = 'inactive';
        // await item.save();

      }

      const associatePayments = await AssociatePayments.findOne({ associateId: item.associateId });
      const mainContractSchema = await MainContractSchema.findOne({ associatedId: item.associateId });

      if (associatePayments && mainContractSchema) {

        const weeklyRent = mainContractSchema.weeklyRent;
        // console.log('length arrays', mainContractSchema.allPayments.length, associatePayments.paymentsArray.length);
        // console.log('weekly rent', weeklyRent);

        // const filter = associatePayments.paymentsArray.filter((e) => e.weeklyCost !== weeklyRent);
        // console.log('filter length', filter.length);

        const findIndex = associatePayments.paymentsArray.findIndex(
          (e) => e.weeklyCost !== weeklyRent && e.weeklyCost !== 0);

        // console.log('filter array', filter);
        // console.log('find index', findIndex, 'stockId', mainContractSchema.stockId);

        if (findIndex !== -1) {
          const date = mainContractSchema.allPayments[findIndex].day;
          // console.log('activation date', date);

          const [day, month, year] = date.split('-');
          const activationDate = new Date(`${year}-${month}-${day}`);
          // console.log('activation date', activationDate);

          // const
          const lastIndex = associatePayments.paymentsArray.findLastIndexCustom(
            (e) => e.weeklyCost !== 0 && e.weeklyCost !== weeklyRent);
          // console.log('last index', lastIndex);

          if (lastIndex !== -1) {
            const lastDate = mainContractSchema.allPayments[lastIndex].day;
            const [day2, month2, year2] = lastDate.split('-');
            const expirationDate = new Date(`${year2}-${month2}-${day2}`);
            // console.log('activationDate', activationDate, 'expirationDate', expirationDate);
            console.log('is before', expirationDate < today, 'contract number', mainContractSchema.contractNumber);
            if (expirationDate < today) {
              console.log('contract number', mainContractSchema.contractNumber);

              item.status = 'inactive';
              // await item.save();
            }

            // item.activationDate = activationDate;

            const thursdayActivationDay = subDays(activationDate, 4)
            console.log('thursdayActivationDay', thursdayActivationDay);
            // item.tempItems[0].expirationDate = expirationDate;

            // await item.save();

          }

        }

        console.log('------------------------------------------------')
      }

    }));

  } catch (error) {
    console.log('error', error);
  }

}

const contractNumbers: string[] = [];

export async function removeWeetrustDocs() {

  console.log('contractNumbers', contractNumbers.length);
  const stockVehicles = await StockVehicle.find({ carNumber: { $in: contractNumbers } });
  console.log('stock vehicles', stockVehicles.length);


  const totalDeleted = [];
  const totalNotDeleted = [];
  const notAssociate: any[] = [];
  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    // console.log('vehicle', vehicle.carNumber, vehicle.transferredTo);

    const associate = await Associate.findById(vehicle.drivers[vehicle.drivers.length - 1]?._id).select('+digitalSignature');

    if (!associate) {
      console.log('no associate', vehicle.carNumber);
      notAssociate.push(vehicle.carNumber);
      return;
    }

    if (vehicle.step.stepNumber === 5) {

      vehicle.step.stepNumber = steps.contractCreated.number
      vehicle.step.stepName = steps.contractCreated.name

      await vehicle.save();
      return;

    }

    const digitalSignature = associate.digitalSignature;

    if (digitalSignature?.documentID) {
      // console.log('documentID', digitalSignature.documentID, 'contract number', vehicle.carNumber);
      // digitalSignature.documentID = undefined;
      // console.log('documentID', digitalSignature.documentID, 'contract number', vehicle.carNumber);
      // const data = await deleteWeetrustDocument(undefined, digitalSignature.documentID);

      console.log('--------------------------------------------------')
      // console.log('Deleted', data, 'contract number: ', vehicle.carNumber);
      // delete digitalSignature.documentID;

      totalDeleted.push({
        carNumber: vehicle.carNumber,
        documentID: digitalSignature.documentID,
      });

    } else {
      // console.log('no documentID', vehicle.carNumber);
      totalNotDeleted.push(vehicle.carNumber);
    }


    digitalSignature.isSent = false;
    digitalSignature.signed = false;

    await associate.save();
  }

  ));

  console.log('------------------------------')
  console.log('not associate', notAssociate);
  console.log('total deleted', totalDeleted.length);
  console.log('total not deleted', totalNotDeleted.length);
  console.log('finish');

}

const contractNumbers2 = [
  "5335",
  "5336",
  "5337",
  "5338",
  "5339",
  "5340",
  "5341",
  "5342",
]

export async function changeArrayPayments() {

  const stockVehicles = await StockVehicle.find({ carNumber: { $in: contractNumbers2 } });

  console.log('stock vehicles', stockVehicles.length);


  await Promise.allSettled(stockVehicles.map(async (vehicle) => {

    const mainContract = await MainContractSchema.findOne({ stockId: vehicle._id }).sort({ createdAt: -1 });

    if (!mainContract) return;
    // console.log('contract number', mainContract.contractNumber);

    const contractNumber = mainContract.contractNumber;
    const allPayments = mainContract.allPayments.slice(1);

    console.log('all payments length', allPayments.length, contractNumber);
    // console.log('first element', allPayments[0]);
    // console.log('last element', allPayments[allPayments.length - 1]);

    allPayments[155] = {
      day: "27-09-2027",
      number: "156",
    }
    console.log('payments length', allPayments.length);

    const allPaymentsFixed = allPayments.map((e, i) => {
      e.number = (i + 1).toString();
      return e;
    });

    console.log('all payments fixed FIRST', allPaymentsFixed[0], contractNumber);
    console.log('all payments fixed LAST', allPaymentsFixed[155])

    mainContract.allPayments = allPaymentsFixed;


    // await mainContract.save();



    console.log('--------------------------------------------------------------------')

  }));


}

// updateAllPayments('1224')
export async function updateAllPayments(contractNumber: string) {

  const mainContract = await MainContractSchema.findOne({ contractNumber });

  if (!mainContract) return;

  // console.log('contract number', mainContract);

  const allPayments = mainContract.allPayments;

  console.log('all payments length', allPayments.length, 'contract number', contractNumber);

  allPayments.forEach((e, i) => {
    // console.log('number', e.number, 'day', e.day);

    // there is an error with date where the year has one 0 extra, for example 20023 instead of 2023, 20024 instead of 2024
    // we need to fix this error

    const [day, month, year] = e.day.split('-');
    if (year.length === 5) {
      // const newYear = year.slice(1);
      // e.day = `${day}-${month}-${newYear}`;
      // console.log('new date', e.day);

      const newYear = '20' + year.slice(3);
      e.day = `${day}-${month}-${newYear}`;
      console.log('new date', e.day);
    }

    allPayments[i] = e;

  });

  // console.log('payments fixed', allPayments)

  await mainContract.save();

}

const contractNumbers3 = [
  "1114",
  "1125",
  "1154",
  "1200",
  "1220",
  "1237",
  "1249",
  "1259",
  "1263",
  "1294",
  "1311",
  "1336",
  "1367",
  "1368",
  "1411",
  "1455.2",
  "1485",
  "1506.2",
  "1507",
  "1531",
  "1558",
  "1593",
  "1621",
  "1622",
  "1668",
  "1674",
  "1676",
  "1679",
  "1679",
  "1691",
  "1766",
  "1782",
  "1791",
  "1895",
  "1896",
  "2005",
  "2009",
  "2011",
  "2019",
  "2043",
  "2052",
  "2069",
  "2133",
  "2186",
  "2195",
  "2203",
  "3065",
  "3107",
  "3115",
  "3120",
  "3126",
  "3141",
  "3167",
  "3189",
  "3225",
  "3238",
  "3240",
  "3249",
  "3287",
  "3297",
  "3323",
  "3327",
  "3520",
  "3529",
  "3541",
  "3586",
  "3588",
  "3663",
  "4014",
  "4056",
  "4059",
  "4060",
  "5022",
  "5037",
  "5056",
  "5181.2",
  "5344",
  "5383",
  "5539",
  "5051-2",
  "7007.2",
  "7046",
  "7073",
  "7086",
  "12009",
  "12015",
  "3606",
]
// getMissingPayments();
export async function getMissingPayments() {

  const contractsParsed = contractNumbers3.map((e) => e.replace('.', ''));
  console.log('contractsParsed', contractsParsed.length);

  const groupRepeatedContracts = contractsParsed.filter((e, i) => contractsParsed.indexOf(e) !== i);
  console.log('groupRepeatedContracts', groupRepeatedContracts);

  const stockVehicles = await StockVehicle.find({ carNumber: { $in: contractsParsed } });

  console.log('stock vehicles', stockVehicles.length);

}

export async function getLegalInsuranceAndInServiceVehicles() {

  const stockVehicles = await StockVehicle.find({ status: { $in: ['legal-process', 'awaiting-insurance', 'in-service'] } })
    .select({
      carNumber: 1,
      extensionCarNumber: 1,
      status: 1,
      vehicleStatus: 1,
      drivers: 1,
    })
  // .populate({
  //   path: 'associates',
  //   select: 'firstName lastName phone',
  // })
  // console.log('stock vehicles', stockVehicles.length);

  // stockVehicles.forEach((vehicle) => {
  //   console.log('carNumber', vehicle.carNumber, vehicle.extensionCarNumber, 
  //     vehicle.status, vehicle.vehicleStatus,
  //     vehicle.associates
  //   );
  // });

  const data = [] as any[];

  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    console.log('getting associate for vehicle', vehicle.drivers[vehicle.drivers.length - 1]?._id);
    const associate = await Associate.findById(vehicle.drivers[vehicle.drivers.length - 1]?._id);

    console.log('associate name', associate?.firstName, associate?.lastName);
    if (!associate) {
      console.log('no associate', vehicle.carNumber);
      return;
    }

    const obj = {
      contrato: vehicle.carNumber + (vehicle.extensionCarNumber ? `-${vehicle.extensionCarNumber}` : ''),
      nombre: `${associate.firstName} ${associate.lastName}`,
      telefono: associate.phone,
      status: vehicle.status,
    }

    console.log('obj contrato', obj.contrato);

    data.push(obj);
  }));

  console.log('data', data.length);

  // save data in a excel file
  const ws = xlsx.utils.json_to_sheet(data);
  const wb = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(wb, ws, 'Sheet1');
  xlsx.writeFile(wb, path.join(__dirname, '../legal-insurance-in-service-vehicles.xlsx'));

  console.log('finish');

}

export async function getStockVehiclesInCollectionCategory() {

  const stockVehicles = await StockVehicle.find({ category: 'collection' });

  console.log('stock vehicles', stockVehicles.length);


  const arrayData = [] as any[];

  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    const associateId = vehicle.drivers[vehicle.drivers.length - 1]?._id?.toString();

    const associate = await Associate.findById(associateId).select('firstName lastName phone clientId');

    if (!associate) {
      console.log('no associate', vehicle.carNumber);
      return;
    }

    if (!associate.clientId) {
      console.log('no clientId', vehicle.carNumber, associate.email);
      return;
    }

    const { data } = await axios.get(`${PAYMENTS_API_URL}/payments?clientId=${associate.clientId}`, {
      headers: {
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    });

    const payments = data.data as {
      status: 'success' | 'pending' | 'canceled'
      isPaid: boolean
    }[];

    console.log('payments', payments.length);

    // const lastPayments = payments.filter((payment) => payment.isPaid === false);
    const filterPayments = [];
    for (const payment of payments) {
      if (payment.isPaid === true) {
        // console.log('payment', payment);
        // cut the loop if it finds a paid payment
        break;
      }
      if (payment.isPaid === false) {
        // console.log('payment', payment);
        filterPayments.push(payment);
      }
    }

    const newObj = {
      contrato: vehicle.carNumber + (vehicle.extensionCarNumber ? `-${vehicle.extensionCarNumber}` : ''),
      nombre: `${associate.firstName} ${associate.lastName}`,
      telefono: associate.phone,
      pagos: filterPayments,
    }

    arrayData.push(newObj);

    // here get all last payments of the associate that are not paid
    // finish filter if it find a paid payment

    // const lastPayments = payments.filter((payment) => payment.isPaid === false);

    // console.log('lastPayments', lastPayments);

  }));

  console.log('arrayData', arrayData.length);

  // save in json file

  const output = path.join(__dirname, '../collection-vehicles.json');
  fs.writeFileSync(output, JSON.stringify(arrayData, null, 2));
  console.log('finish');

}

export async function readCollectionJsonFileAndGetFromWhenIsNotPaying() {

  const filePath = path.join(__dirname, '../collection-vehicles.json');
  const data = require(filePath);

  // console.log('data length', data.length);

  const arrayData = [] as any[];

  data.forEach((e: any) => {

    const pagos = e.pagos;

    const getLastPago = pagos[pagos.length - 1];
    console.log('createdAt', getLastPago?.createdAt);
    const obj = {
      contrato: e.contrato,
      nombre: e.nombre,
      telefono: e.telefono.toString(),
      desdeCuandoNoPaga: getLastPago?.createdAt || 'sin pagos o pagos realizados',
    }
    arrayData.push(obj);
  });

  console.log('arrayData', arrayData.length);
  // save in excel file

  const ws = xlsx.utils.json_to_sheet(arrayData);
  const wb = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(wb, ws, 'Sheet1');
  xlsx.writeFile(wb, path.join(__dirname, '../collection-vehicles-not-paying.xlsx'));

}
const carNumbers = [
  "3354",
  "1555",
  "1130",
  "1454",
  "1472",
  "1476",
  "1477",
  "1480",
  "1485",
  "3349",
  "5259",
  "5269",
  "5284",
  "19011",
  "19017",
  "21004",
  "1363",
  "1396",
  "1408",
  "1469",
  "1487",
  "1500",
  "2136",
  "4029",
  "4031",
  "5195",
  "5196",
  "7024",
  "7028",
  "12007",
];

// Create function to get weetrust document id from a csv file list, getting from Stock carnumber
// where the weetrust document id is on last associate from drivers array in stockvehicle model
// getWeetrustDocumentIdFromCsv();
export async function getWeetrustDocumentIdFromCsv() {
  // name file: carnumber-list.xlsx

  // const root = process.cwd();
  // const filePath = path.join(root, 'carnumber-list.xlsx');


  // const workbook = xlsx.readFile(filePath);
  // const sheetName = workbook.SheetNames[0];
  // const worksheet = workbook.Sheets[sheetName];

  // const data = xlsx.utils.sheet_to_json(worksheet);

  // const carNumbers = data.map((item: any) => item.CarNumber);
  const stockVehicles = await StockVehicle.find({ carNumber: { $in: carNumbers } })/* .limit(3) */;

  console.log('stock vehicles', stockVehicles.length);

  const dataToSave = [] as any[];

  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    const associate = await Associate.findById(vehicle.drivers[vehicle.drivers.length - 1]?._id).select('+digitalSignature');

    if (!associate) {
      console.log('no associate', vehicle.carNumber);
      return;
    }

    const digitalSignature = associate.digitalSignature;

    if (!digitalSignature || !digitalSignature.documentID) {
      console.log('no digitalSignature', vehicle.carNumber);

      // push to dataToSave with carNumber and documentID: null
      dataToSave.push({
        carNumber: vehicle.carNumber,
        documentID: 'NO ENCONTRADO',
        digitalSignature,
      });

      return;
    }

    const participants: {
      email: string;
      name: string;
      urlSign: string;
      signed: boolean;
    }[] = digitalSignature.participants;
    // Map participants to create an object with name as key and signed as value
    // to return "Name" as column name and "name + signed" as value for true use "Si", for false use "No"
    // Example:  Name 1      Name 2       Name 3
    //           Pedro: Si   Martinez: No   Other name: No
    const formattedParticipants = participants.reduce((acc, participant, index) => {
      acc[`Name ${index + 1}`] = `${participant.name}: ${participant.signed ? 'Si' : 'No'}`;
      return acc;
    }, {} as Record<string, string>);

    console.log(formattedParticipants);



    const signDocs = associate.signDocs;
    console.log('signDocs', signDocs);
    const hasAllDocs = signDocs ? Object.values(signDocs).every((doc) => doc) : false;
    console.log('hasAllDocs', hasAllDocs);
    console.log("--------------------------------------------------------------");

    dataToSave.push({
      carNumber: vehicle.carNumber,
      documentID: digitalSignature.documentID,
      isSent: digitalSignature.isSent,
      signed: digitalSignature.signed,
      ...formattedParticipants,
      'Ya tiene todos los documentos': hasAllDocs ? 'Si' : 'No',
    });

  }));

  // save dataToSave in a json file and xlsx file
  const outputFilePath = path.join(__dirname, '../weetrust-document-id.json');
  fs.writeFileSync(outputFilePath, JSON.stringify(dataToSave, null, 2));

  const ws = xlsx.utils.json_to_sheet(dataToSave);
  const wb = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(wb, ws, 'Sheet1');
  xlsx.writeFile(wb, path.join(__dirname, '../weetrust-document-id.xlsx'));
  console.log('finish');

}



// Return all these car numbers list to Contrato generado step
export async function returnToContratoGeneradoStep() {
  console.log('carNumbers', carNumbers.length);
  const stockVehicles = await StockVehicle.find({ carNumber: { $in: carNumbers } }).select('carNumber step');

  console.log('stock vehicles', stockVehicles.length);
  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    console.log('Previous vehicle', vehicle.carNumber, vehicle.step);

    // validate the step is not lower than 4 
    if (vehicle.step.stepNumber <= 4) { // 4 is Contrato generado, there is no need to change it
      console.log('step is lower than 4', vehicle.carNumber, vehicle.step);
      console.log('-----------------------------------------------------------------')
      return;
    }

    vehicle.step.stepName = steps.contractCreated.name;
    vehicle.step.stepNumber = steps.contractCreated.number;
    console.log('Vehicle', vehicle.carNumber, vehicle.step);
    console.log('-----------------------------------------------------------------')
    // await vehicle.save();
  }));
}

// getWeetrustDocumentIdsFromStockVehicles();
export async function getWeetrustDocumentIdsFromStockVehicles() {
  console.log('totalCarNumbers', carNumbers.length);
  const stockVehicles = await StockVehicle.find({ carNumber: { $in: carNumbers } }).select('carNumber extensionCarNumber step drivers').populate('associates', '+digitalSignature');
  console.log('stock vehicles', stockVehicles.length);

  const dataToSave = [] as any[];

  await Promise.allSettled(stockVehicles.map(async (vehicle) => {
    const lastAssociate = vehicle.associates[vehicle.associates.length - 1];
    if (!lastAssociate) {
      console.log('no associate', vehicle.carNumber);
      return;
    }
    // console.log('last associate', lastAssociate.firstName, lastAssociate.lastName, lastAssociate.email);
    console.log('last associate digitalSignature', lastAssociate.digitalSignature);
    console.log('-----------------------------------------------------------------')
    const documentID = lastAssociate.digitalSignature.documentID;
    console.log('documentID', documentID);

    dataToSave.push({
      carNumber: vehicle.carNumber,
      contractNumber: vehicle.carNumber + (vehicle.extensionCarNumber ? `-${vehicle.extensionCarNumber}` : ''),
      documentID,
      signed: lastAssociate.digitalSignature.signed,
      isSent: lastAssociate.digitalSignature.isSent,
    });

  }));

  console.log('dataToSave', dataToSave.length);
  const outputFilePath = path.join(__dirname, '../weetrust-document-id.json');
  fs.writeFileSync(outputFilePath, JSON.stringify(dataToSave, null, 2));
  console.log('finish');
}


// From weetrust-document-id.json file, download all the documents from weetrust and save them in a folder
// download every file with next format: carNumber-associateName-documentID.pdf
// downloadWeetrustDocuments();
export async function downloadWeetrustDocuments() {
  const root = process.cwd();
  const filePath = path.join(root, 'weetrust-document-id.json');

  const data = require(filePath);

  console.log('data', data.length);

  const filteredData = data.filter((item: any) => item.documentID !== 'NO ENCONTRADO');

  console.log('filteredData', filteredData.length);

  const documentsId = filteredData.map((item: any) => item.documentID);

  // from associate model, get all the associates that have a documentID in the digitalSignature field

  const associates = await Associate.find({ 'digitalSignature.documentID': { $in: documentsId } })
    .select('firstName lastName email +digitalSignature')
  // .limit(1)
  console.log('associates', associates);
  console.log('associates', associates.length);

  const token = await getWeetrustToken();
  console.log('token', token);


  const folderPath = path.join(root, 'contratos-pdf');

  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath);
  }
  // throw new Error('token not found');

  await Promise.allSettled(associates.map(async (associate) => {
    const documentID = associate.digitalSignature.documentID;

    if (!documentID) return;
    const isSigned = associate.digitalSignature.signed;

    const documentUrl = await getWeetrustDocumentUrlByDocumentId(documentID, token);

    console.log('documentUrl', documentUrl);
    if (!documentUrl) return;

    // download the file from the url and save it in a folder with the name of the associate

    const associateName = associate.firstName.trim().replace(/\s/g, '_') + ' ' + associate.lastName.trim().replace(/\s/g, '_'); // Replace spaces with underscores

    const contractNumber = filteredData.find((item: any) => item.documentID === documentID)?.contractNumber;
    console.log('contractNumber', contractNumber);
    if (!contractNumber) return;

    const fileName = `${contractNumber}-${associateName}-${documentID}.pdf`;

    // const pdfFilePath = path.join(folderPath, fileName);
    // const newFile = await getFileFromUrl(documentUrl);
    // fs.writeFileSync(pdfFilePath, newFile);
    // console.log('File saved:', pdfFilePath);

    // Si isSigned es true, guardar en la carpeta // contratos-pdf/firmados
    // Si isSigned es false, guardar en la carpeta // contratos-pdf/no firmados

    const folder = isSigned ? 'firmados' : 'no firmados';
    const pdfFilePath = path.join(folderPath, folder, fileName);
    const newFile = await getFileFromUrl(documentUrl);
    fs.writeFileSync(pdfFilePath, newFile);
    console.log('File saved:', pdfFilePath);
  }));


}
const WEETRUST_URL = "https://api.weetrust.mx"

export async function getWeetrustToken() {
  // try {
  const response = await axios.post(
    `${WEETRUST_URL}/access/token`,
    {},
    {
      headers: {
        'user-id': process.env.WEETRUST_USER_ID,
        'api-key': process.env.WEETRUST_API_KEY,
      },
    }
  );
  return response.data.responseData.accessToken;
}

async function getWeetrustDocumentUrlByDocumentId(documentId: string, token: string) {
  const url = `${WEETRUST_URL}/documents/${documentId}`;
  const response = await axios.get(url, {
    headers: {
      'user-id': process.env.WEETRUST_USER_ID,
      token,
    },
  });
  return response.data.responseData.documentFileObj.url;
}


export const getFileFromUrl = async (url: string) => {
  const response = await axios.get(url, {
    responseType: 'arraybuffer',
  });

  return Buffer.from(response.data, 'binary');
};


export async function getAllSignedPagareFromStockVehicles() {

  return;
}

// Crea una función que marque todas las citas de mantenimiento como completadas si ya tienen un
// servicio registrado y son menores a la fecha actual,
//
// export async function g


// Función para agregar el associateId a todos los servicios de mantenimientos de vendors
// validar que correspondan al conductor correcto, asi que checar la fecha del servicio con
// la fecha de creación del associate para verificar si al vehiculo se ha cambiado de conductor
// addAssociateIdToServices();
export async function addAssociateIdToServices() {
  const services = await ServiceVendorModel.find({ associateId: { $exists: false } })
    .select('stockId associateId workshopId createdAt')
    .sort({ createdAt: 1 })

  console.log('services', services.length);
  const savedData: any[] = [];

  await Promise.allSettled(services.map(async (service) => {
    // Encontrar todos los appointments relacionados por stockId y hacer un sort de createdAt
    // const appointments = await AppointmentVendor.find({ stockId: service.stockId }).sort({ createdAt: 1 });

    // console.log('total appointments found of stockId: ', appointments.length, service.stockId);
    // if (!appointments.length) return;


    if (!service.workshopId) {
      console.log('no workshopId', service._id, service.stockId);
      // await service.remove();

      savedData.push({
        serviceId: service._id,
        stockId: service.stockId,
        reason: 'no workshopId',
      });

      return;
    }

    const stockVehicle = await StockVehicle.findById(service.stockId)
      .select('drivers carNumber')
      .populate('associates', 'createdAt')

    console.log('stockVehicle', stockVehicle?.carNumber, service.stockId);
    if (!stockVehicle) {
      console.log('no stock vehicle found for service: ', service._id, service.stockId);
      console.log('--------------------------------------------------------------');
      savedData.push({
        serviceId: service._id,
        stockId: service.stockId,
        reason: 'no stock vehicle',
      });
      return;
    }

    const totalAssociates = stockVehicle.associates?.length;
    console.log('total associates', totalAssociates, service.stockId);
    // console.log('associates: ', stockVehicle.associates);
    console.log('service before: ', service.stockId, service.associateId);
    if (!totalAssociates) {
      console.log('no associates found for service: ', service._id, service.stockId);
      console.log('--------------------------------------------------------------');
      // delete service;
      // await service.remove();
      savedData.push({
        serviceId: service._id,
        stockId: service.stockId,
        reason: 'no associates',
      });
      return;
    }

    if (totalAssociates === 1) {
      console.log('Saving associate id: ', stockVehicle.associates[0]._id.toString());
      service.associateId = stockVehicle.associates[0]._id.toString();
      console.log('service after: ', service.stockId, service.associateId);
      try {
        await service.save();
        console.log('service updated', service._id);
        console.log('--------------------------------------------------------------');

      } catch (error) {
        console.log('error saving service', error);
        console.log('--------------------------------------------------------------');
      }
      savedData.push({
        serviceId: service._id,
        stockId: service.stockId,
        associateId: service.associateId,
        reason: 'total associates === 1',
      });
      return;
    }

    console.log('Service: ', service);
    console.log('stockVehicle.associates', stockVehicle.associates);
    console.log('--------------------------------------------------------------');
    const serviceDate = service.createdAt;
    console.log('serviceDate', serviceDate);


    const almostLatestAssociate = stockVehicle.associates[totalAssociates - 2]; // Get second last associate
    console.log('almostLatestAssociate', almostLatestAssociate);

    const latestAssociate = stockVehicle.associates[totalAssociates - 1]; // Get last associate
    console.log('latestAssociate', latestAssociate);
    console.log('--------------------------------------------------------------');

    const serviceDateObj = new Date(serviceDate);
    const latestAssociateDateObj = new Date(latestAssociate.createdAt);


    // Si el servicio es posterior a la fecha de creación del último conductor, asignar al último
    if (serviceDateObj >= latestAssociateDateObj) {
      service.associateId = latestAssociate._id.toString();
      console.log('Assigned to latest associate - Service date:', serviceDateObj, 'Associate date:', latestAssociateDateObj);
    } else {
      // Buscar el conductor que estaba activo cuando se realizó el servicio
      let correctAssociate = null;

      // Iterar por todos los conductores para encontrar el que estaba activo en la fecha del servicio
      for (let i = 0; i < stockVehicle.associates.length; i++) {
        const currentAssociate = stockVehicle.associates[i];
        const currentAssociateDateObj = new Date(currentAssociate.createdAt);

        // Si la fecha de creación del conductor es anterior a la fecha del servicio
        if (currentAssociateDateObj <= serviceDateObj) {
          correctAssociate = currentAssociate;
        } else {
          // Si encontramos un conductor cuya fecha de creación es posterior al servicio,
          // nos quedamos con el conductor anterior
          break;
        }
      }

      // Si no encontramos un conductor adecuado, usar el primero
      if (!correctAssociate) {
        correctAssociate = stockVehicle.associates[0];
      }

      service.associateId = correctAssociate._id.toString();
      console.log('Assigned to historical associate - Service date:', serviceDateObj, 'Associate date:', new Date(correctAssociate.createdAt));
    }


    console.log('El conductor correcto con id es: ', service.associateId);
    await service.save();
    savedData.push({
      serviceId: service._id,
      stockId: service.stockId,
      associateId: service.associateId,
      currentVehicleAssociateId: stockVehicle.associates[stockVehicle.associates.length - 1]._id.toString(),
      isDifferent: service.associateId !==
        stockVehicle.associates[stockVehicle.associates.length - 1]._id.toString(), // Compare correct associate obtained, with last associate
      reason: 'correct associate',
      serviceDate: serviceDate.toISOString(),
      latestAssociateDate: latestAssociate.createdAt,
      almostLatestAssociateDate: almostLatestAssociate?.createdAt,
    });
    console.log('service updated with correct associate', service._id);
    console.log('--------------------------------------------------------------');

  }));

  const outputFilePath = path.join(__dirname, '../associateIdToServices.json');
  fs.writeFileSync(outputFilePath, JSON.stringify(savedData, null, 2));
  console.log('FINISH ---------------------------------------------------------')
}

// A function to complete, cancel or mark as not-attended appointments based on service existence
// This validates the total number of services for a vehicle against its appointments
// and corrects the status of past appointments based on today's date, to avoid errors
// in obtaining, creating and validating rules for creating maintenance appointments for drivers
export async function fixAppointmentStatuses() {
  const TWO_DAYS_AGO = new Date();
  TWO_DAYS_AGO.setDate(TWO_DAYS_AGO.getDate() - 1);

  console.log('Starting appointment status correction process...');
  console.log(`Finding scheduled/rescheduled appointments before: ${TWO_DAYS_AGO.toISOString()}`);

  // Find all appointments that are still in scheduled or rescheduled status but are in the past
  const oldPendingAppointments = await AppointmentVendor.find({
    status: { $in: [AppointmentVendorStatus.scheduled, AppointmentVendorStatus.rescheduled] },
    startTime: { $lt: TWO_DAYS_AGO },
  })
    .select('stockId startTime status data statusHistory')
    .sort({ startTime: 1 });

  console.log(`Found ${oldPendingAppointments.length} old pending appointments to process`);
  if (oldPendingAppointments.length > 1) {
    return;
  };

  const results = {
    completed: 0,
    notAttended: 0,
    canceled: 0,
    unchanged: 0,
    errors: 0,
  };

  const processedData = [];

  // Process each appointment
  for (const appointment of oldPendingAppointments) {
    try {
      console.log(`Processing appointment ID: ${appointment._id}, Stock ID: ${appointment.stockId}, Date: ${appointment.startTime}, Current Status: ${appointment.status}`);

      // Store the previous status before any changes
      const previousStatus = appointment.status;

      // Find if there's a service record for this appointment
      const service = await ServiceVendorModel.findOne({
        stockId: appointment.stockId,
        // Look for services created around the appointment date (same day or day after)
        createdAt: {
          $gte: new Date(appointment.startTime.getTime() - (24 * 60 * 60 * 1000)), // 1 day before
          $lte: new Date(appointment.startTime.getTime() + (48 * 60 * 60 * 1000)),  // 2 days after
        },
      });

      // Find the next appointment for this vehicle after this one
      const nextAppointment = await AppointmentVendor.findOne({
        stockId: appointment.stockId,
        startTime: { $gt: appointment.startTime },
        _id: { $ne: appointment._id },
      }).sort({ startTime: 1 });

      let newStatus;
      let statusReason;

      // Decision logic for the appointment status
      if (service) {
        // If there's a service record, mark as completed
        newStatus = AppointmentVendorStatus.completed;
        statusReason = "Service record found";
        results.completed++;
      } else if (nextAppointment &&
        (nextAppointment.status === AppointmentVendorStatus.completed ||
          nextAppointment.data?.registeredKm > appointment.data?.registeredKm)) {
        // If there's a later appointment that was completed or has higher km, 
        // this one was likely completed too
        newStatus = AppointmentVendorStatus.completed;
        statusReason = "Later appointment was completed or has higher km";
        results.completed++;
      } else {
        // No service record and no evidence of completion, mark as not-attended
        newStatus = AppointmentVendorStatus['not-attended'];
        statusReason = "No service record found and no evidence of completion";
        results.notAttended++;
      }

      // Update the appointment status
      appointment.status = newStatus;
      appointment.statusHistory.push({
        status: newStatus,
        date: new Date(),
        // reason: `Auto-corrected: ${statusReason}`,
      });

      // await appointment.save();

      processedData.push({
        appointmentId: appointment._id.toString(),
        stockId: appointment.stockId.toString(),
        previousStatus: previousStatus,
        newStatus,
        originalDate: appointment.startTime,
        statusReason,
        isSameStatus: previousStatus === newStatus,
        serviceFound: !!service,
        serviceId: service ? service._id.toString() : null,
        nextAppointmentDate: nextAppointment?.startTime || null,
        nextAppointmentId: nextAppointment ? nextAppointment._id.toString() : null,
        updatedAt: new Date(),
      });

      console.log(`Updated appointment ${appointment._id} from ${previousStatus} to ${newStatus} - Reason: ${statusReason}`);

    } catch (error: any) {
      console.error(`Error processing appointment ${appointment._id}:`, error);
      results.errors++;

      processedData.push({
        appointmentId: appointment._id.toString(),
        stockId: appointment.stockId.toString(),
        previousStatus: appointment.status,
        error: error.message,
      });
    }
  }

  // Save results to a file
  const outputFilePath = path.join(__dirname, '../appointment-status-corrections.json');
  fs.writeFileSync(outputFilePath, JSON.stringify({
    summary: results,
    processedAppointments: processedData,
  }, null, 2));

  console.log('Appointment status correction completed');
  console.log('Summary:', results);
  console.log(`Detailed results saved to: ${outputFilePath}`);

  return results;
}
