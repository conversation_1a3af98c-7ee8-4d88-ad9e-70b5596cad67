import mongoose, { Document, Schema } from 'mongoose';
import crypto from 'crypto';

interface IOTP extends Document {
  associateId: mongoose.Types.ObjectId;
  email: string;
  phone: string;
  otp: string;
  expiresAt: number; // epoch timestamp in milliseconds
  isUsed: boolean;
  createdAt: number; // epoch timestamp in milliseconds
  updatedAt: number; // epoch timestamp in milliseconds

  // Instance methods
  isValid(): boolean;
  markAsUsed(): Promise<void>;
}

interface IOTPModel extends mongoose.Model<IOTP> {
  generateOTP(length?: number): string;
  createNewOTP(params: {
    associateId: string;
    email: string;
    phone: string;
    expiryMinutes: number;
  }): Promise<IOTP>;
  verifyOTP(params: { associateId: string; email: string; phone: string; otp: string }): Promise<IOTP | null>;
}

const otpSchema = new Schema<IOTP, IOTPModel>(
  {
    associateId: {
      type: Schema.Types.ObjectId,
      ref: 'associates',
      required: true,
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    phone: {
      type: String,
      required: true,
      trim: true,
    },
    otp: {
      type: String,
      required: true,
    },
    expiresAt: {
      type: Number, // Store as epoch timestamp
      required: true,
    },
    isUsed: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: {
      currentTime: () => Date.now(), // Use epoch timestamps for timestamps
      createdAt: true,
      updatedAt: true,
    },
  }
);

// Create indexes
otpSchema.index({ email: 1, type: 1 });
otpSchema.index({ phone: 1, type: 1 });
otpSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index

// Instance methods
otpSchema.methods.isValid = function (): boolean {
  return !this.isUsed && this.expiresAt > Date.now();
};

otpSchema.methods.markAsUsed = async function (): Promise<void> {
  this.isUsed = true;
  await this.save();
};

// Static methods
otpSchema.statics.generateOTP = function (length: number = 6): string {
  const digits = '0123456789';
  let otp = '';

  for (let i = 0; i < length; i++) {
    otp += digits[crypto.randomInt(0, digits.length)];
  }

  return otp;
};

otpSchema.statics.createNewOTP = async function (params: {
  associateId: string;
  email: string;
  phone: string;
  expiryMinutes: number;
}): Promise<IOTP> {
  const { associateId, email, phone, expiryMinutes } = params;

  // Invalidate any existing OTPs
  await this.updateMany({ associateId, email, phone, isUsed: false }, { isUsed: true });

  // Create new OTP
  const otp = this.generateOTP();
  const now = Date.now();
  const expiresAt = now + expiryMinutes * 60 * 1000; // Convert minutes to milliseconds

  const newOTP = await this.create({
    associateId,
    email,
    phone,
    otp,
    expiresAt,
  });

  return newOTP;
};

otpSchema.statics.verifyOTP = async function (params: {
  associateId: string;
  email: string;
  phone: string;
  otp: string;
}): Promise<IOTP | null> {
  const { associateId, email, phone, otp } = params;
  const now = Date.now();

  const otpDoc = await this.findOne({
    associateId,
    email,
    phone,
    otp,
    isUsed: false,
    expiresAt: { $gt: now },
  });

  return otpDoc;
};

// Helper methods for date conversion
otpSchema.methods.toJSON = function () {
  const obj = this.toObject();
  return {
    ...obj,
    createdAt: obj.createdAt,
    updatedAt: obj.updatedAt,
    expiresAt: obj.expiresAt,
    // Optional: Add human-readable dates if needed
    createdAtDate: new Date(obj.createdAt).toISOString(),
    updatedAtDate: new Date(obj.updatedAt).toISOString(),
    expiresAtDate: new Date(obj.expiresAt).toISOString(),
  };
};

const OTP = mongoose.model<IOTP, IOTPModel>('OTP', otpSchema);

export default OTP;
