import { Schema, model, Document, Types } from 'mongoose';

export interface IBulkUploadHistory extends Document {
  fileName: string;
  fileData: string;
  success: boolean;
  failureReason?: {
    userMessage: string;
    technicalMessage?: string;
    errorType?: string;
    affectedField?: string;
    suggestedAction?: string;
  };
  uploadedBy: Types.ObjectId;
  vehicleInfo?: {
    brand?: string;
    model?: string;
    version?: string;
    year?: string;
    color?: string;
    vin?: string;
    carNumber?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const bulkUploadHistorySchema = new Schema<IBulkUploadHistory>(
  {
    fileName: {
      type: String,
      required: true,
    },
    fileData: {
      type: String,
      required: true,
    },
    success: {
      type: Boolean,
      required: true,
      default: false,
    },
    failureReason: {
      userMessage: String,
      technicalMessage: String,
      errorType: String,
      affectedField: String,
      suggestedAction: String,
    },
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    vehicleInfo: {
      brand: String,
      model: String,
      version: String,
      year: String,
      color: String,
      vin: String,
      carNumber: String,
    },
  },
  {
    timestamps: true,
  }
);

export default model<IBulkUploadHistory>('BulkUploadHistory', bulkUploadHistorySchema);
