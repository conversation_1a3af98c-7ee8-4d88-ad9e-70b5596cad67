import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const stockServicesSchema = new Schema({
  dateIn: {
    type: String,
    required: true,
  },

  dateOut: {
    type: String,
    required: true,
  },

  dateFinished: {
    type: String,
    default: null,
  },

  description: String,

  comments: String,

  stockId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: true,
  },

  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
    required: true,
  },

  images: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
      },
    ],
  },

  isCanceled: {
    type: Boolean,
    default: false,
  },

  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

const ServiceStock = model('in-service', stockServicesSchema);

export default ServiceStock;
