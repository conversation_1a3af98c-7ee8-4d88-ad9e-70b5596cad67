import mongoose, { Schema, Document } from 'mongoose';

export enum QRActionTokenType {
  QR_SCAN = 'qr_scan',
  CONFIRMATION = 'confirmation',
}

export interface IQRActionToken extends Document {
  token: string;
  vehicleId: mongoose.Schema.Types.ObjectId;
  type: QRActionTokenType;
  intendedNextStatus?: string;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}

const qrActionTokenSchema = new Schema<IQRActionToken>({
  token: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  vehicleId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: true,
  },
  type: {
    type: String,
    enum: Object.values(QRActionTokenType),
    required: true,
  },
  intendedNextStatus: {
    // Only relevant for 'confirmation' type tokens
    type: String,
    required: false,
  },
  expiresAt: {
    type: Date,
    required: true,
  },
  used: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// This will delete documents 3 min (180 seconds) *after* their expiresAt time.

qrActionTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 3 * 60 });

const QRActionToken = mongoose.model<IQRActionToken>('QRActionToken', qrActionTokenSchema);

export default QRActionToken;
