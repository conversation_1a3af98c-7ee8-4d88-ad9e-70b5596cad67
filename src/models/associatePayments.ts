import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const associatePayments = new Schema({
  vehiclesId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: [true, 'vehicleId is required'],
  },

  gigId: {
    type: String,
    default: null,
  },

  suscriptionId: {
    type: String,
    default: null,
  },

  block: { type: Boolean, default: false },

  monexClabe: { type: String, default: null },

  i80Clabe: { type: String, default: null },

  oneCarNowClabe: { type: String, default: null },

  stpClabe: { type: String, default: null },

  soldClabe: { type: String, default: null },

  region: { type: String, default: null },

  balance: { type: Number, default: 0 },

  paymentNumber: {
    type: Number,
    default: 0,
  },

  monexEmail: {
    type: String,
  },

  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
    required: [true, 'associateId is required'],
  },
  contractId: {
    type: Schema.Types.ObjectId,
    ref: 'MainContractSchema',
    required: [true, 'contractId is required'],
  },

  associateEmail: {
    type: String,
    unique: true,
    required: [true, 'associate email is required'],
  },

  model: {
    type: String,
  },

  paymentsArray: {
    type: [
      {
        paymentId: String,
        weeklyCost: Number,
        fee: Number,
        day: String,
        url: String,
        status: String,
        block: Boolean,
        blockObj: {
          date: String,
          cost: Number,
          status: String,
          paymentId: String,
          url: String,
        },
      },
    ],
  },

  newPaymentsArr: {
    type: [
      {
        paymentId: String,
        weeklyCost: Number,
        date: String,
        url: String,
        status: String,
        blockPayment: {
          type: Schema.Types.Mixed,
          default: null, // Permite null como valor predeterminado
        },
      },
    ],
  },

  adendumGenerated: {
    type: Boolean,
    default: false,
  },

  lengthAddedBefore: {
    type: Number,
    default: 0,
  },

  paymentsHistory: {
    type: [
      {
        transactionAmount: Number,
        transactionId: String,
        email: String,
        source: {
          type: String,
          // enum: ['1', '2', '3'],
        },
        date: String,
      },
    ],
    default: [],
  },

  otherPayment: {
    type: [
      {
        transactionId: String,
        transactionAmount: Number,
        date: String,
        url: String,
        status: String,
      },
    ],
  },

  version: {
    //this is just to track what records were updated using this function: addSuscriptionIdToAssociatePayments in changes.ts file
    type: Number,
  },

  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

const AssociatePayments = model('AssociatePayments', associatePayments);

const associatePaymentInstace = new AssociatePayments();

export type AssociatePaymentInstanceType = typeof associatePaymentInstace;

export default AssociatePayments;
