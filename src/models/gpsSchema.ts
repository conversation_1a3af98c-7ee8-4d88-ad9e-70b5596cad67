import { Schema, model } from 'mongoose';

const gps = new Schema({
  gps: {
    type: String,
    required: [true, 'Gps is required'],
  },
  comando: {
    type: String,
    required: [true, 'Comando is required'],
  },
  responseGpsAPI: {
    type: Object,
    required: [true, 'Response is required'],
  },
  ocnUserName: {
    type: String,
    required: [true, 'OcnUser is required'],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});
gps.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const Gps = model('Gps', gps);

export default Gps;
