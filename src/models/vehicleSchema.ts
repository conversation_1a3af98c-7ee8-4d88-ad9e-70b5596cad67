import { Schema, model } from 'mongoose';

const vehicleSchema = new Schema({
  name: {
    type: 'string',
    required: true,
  },
  description: {
    type: 'string',
    required: true,
  },
  exterior: {
    type: 'string',
    required: true,
  },
  interior: {
    type: 'string',
    required: true,
  },
  liters: {
    type: Number,
    required: true,
  },
  payment: {
    type: Number,
  },
  plan: {
    type: 'string',
    enum: ['Personal', 'Platform'],
    required: true,
  },
  seatsNumber: {
    type: 'Number',
    required: true,
  },
  securityDetails: {
    type: 'string',
    required: true,
  },
  specialDetails: {
    type: 'string',
    required: true,
  },
  model: {
    type: String,
  },
  transmission: {
    type: 'string',
    required: true,
  },
  aditionalData: {
    type: 'string',
    required: true,
  },
  paymentOptions: {
    type: [
      {
        label: {
          type: String,
        },
        biweeklyPay: {
          type: Number,
        },
        deposit: {
          type: Number,
        },
      },
    ],
    default: null,
  },
  images: {
    type: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Document',
        default: null,
      },
    ],
    required: true,
  },
});
vehicleSchema.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
  },
});

const Vehicle = model('vehicles', vehicleSchema);

export default Vehicle;
