import { Schema, model } from 'mongoose';

const startPayFlow = new Schema({
  contractNumber: {
    type: String,
    required: [true, 'ContractNumber is required'],
  },

  isCreated: {
    type: Boolean,
    default: false,
  },

  startDate: {
    type: String,
    required: [true, 'StartDate is required'],
  },

  endDate: {
    type: String,
    required: [true, 'EndDate is required'],
  },

  stockId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: [true, 'StockVehicle is required'],
  },
  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
    required: [true, 'Associate is required'],
  },
  clientId: {
    type: String,
    required: [true, 'Client is required'],
  },
  rentingProduct: {
    type: Schema.Types.Mixed,
    required: [true, 'RentingProduct is required'],
  },
  assistanceProduct: {
    type: Schema.Types.Mixed,
    required: [false, 'AssistanceProduct is required'], // required when country is mx
  },
  downPaymentProduct: {
    type: Schema.Types.Mixed,
  },

  depositProduct: {
    type: Schema.Types.Mixed,
  },
});

const StartPayFlow = model('StartPayFlow', startPayFlow);

export default StartPayFlow;
