import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const readmissionSchema = new Schema({
  description: {
    type: String,
    required: true,
  },

  km: {
    type: String,
  },

  contractNumber: {
    type: String,
  },

  extensionContractNumber: {
    type: Number,
  },

  readmissionReason: {
    type: String,
    required: true,
  },

  readmissionDate: {
    type: String,
    required: true,
  },

  kmImgs: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
      },
    ],
  },

  evidenceImgs: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
      },
    ],
  },

  promissoryNote: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },

  readmissionDoc: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },

  contractCanceled: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },

  signedPromissoryNote: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },
  terminationFiles: {
    type: new Schema(
      {
        promissoryNote: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
        agreement: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
        promissoryNoteSigned: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
        agreementSigned: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
        recessionSigned: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
      },
      { _id: false }
    ),
    default: null,
  },

  comments: {
    type: String,
  },

  stockVehicleId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: true,
  },

  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },

  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

const Readmission = model('readmissions', readmissionSchema);

export default Readmission;
