import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const gigPayments = new Schema({
  body: {
    type: Object,
  },
  region: {
    type: String,
  },
  isPaid: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
gigPayments.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const GigPayments = model('GigPayments', gigPayments);

export default GigPayments;
