import { Schema, model } from 'mongoose';
import mt from 'moment-timezone';

const timeZone = 'America/Mexico_City';

function getCurrentDateTime() {
  return mt().tz(timeZone).toDate();
}

const candidateSchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    unique: true,
    required: [true, 'Email is required'],
  },
  city: {
    type: String,
    required: true,
  },
  country: {
    type: String,
    required: true,
  },
  state: {
    type: String,
    required: true,
  },
  birthday: { type: String, required: true },
  document: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
    required: true,
  },
  createdAt: { type: String, default: getCurrentDateTime },
  updatedAt: { type: String, default: getCurrentDateTime },
});

const Candidate = model('Candidate', candidateSchema);

export default Candidate;
