import { Schema, model } from 'mongoose';

const mainContractSchemaHistory = new Schema({
  contractNumber: {
    type: String,
    required: [true, 'Contract number is required'],
  },
  subContracts: {
    type: Array,
    default: [],
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  documentId: {
    type: Schema.Types.ObjectId,
    ref: 'DocumentSchema',
    // required: [true, 'Document is required'],
  },
  stockId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicleSchema',
  },
  associatedId: {
    type: Schema.Types.ObjectId,
    ref: 'associateSchema',
  },
  downPayment: {
    type: Number,
    default: 0,
  },
  finalPrice: {
    type: Number,
    required: [true, 'Finally car price is required'],
  },
  weeklyRent: {
    type: Number,
    required: [true, 'Week payment is required'],
  },
  allPayments: {
    type: Array,
    required: [true, 'All payments is required'],
  },
  totalPrice: {
    type: Number,
    required: [true, 'Total price is required'],
  },
  deliveredDate: {
    type: Date,
    required: [true, 'Delivered date is required'],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updateAt: {
    type: Date,
  },
});

mainContractSchemaHistory.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const MainContractSchemaHistory = model('MainContractSchemaHistory', mainContractSchemaHistory);

const mainContractInstance = new MainContractSchemaHistory();

export type MainContractInstanceType = typeof mainContractInstance;

export default MainContractSchemaHistory;
