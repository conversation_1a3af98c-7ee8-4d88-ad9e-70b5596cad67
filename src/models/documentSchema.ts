import { Schema, Types, model, Document as MongooseDocument } from 'mongoose';
import mt from 'moment-timezone';
import { deleteFileFromS3 } from '../aws/s3';
import { DocumentCategory } from '../types&interfaces/vehicleDocuments';

const timeZone = 'America/Mexico_City';

function getCurrentDateTime() {
  return mt().tz(timeZone).toDate();
}

export interface DocumentMongoI extends MongooseDocument {
  originalName: string;
  path: string;
  status: string;
  type: string;
  mimeType?: string;
  vehicleId: Types.ObjectId; // Legacy
  associateId: Types.ObjectId; // Legacy
  userId: Types.ObjectId; // Will be used for uploadedBy
  candidateId: Types.ObjectId; // Legacy
  createdAt: Date | string; // String for legacy
  updatedAt: Date | string; // String for legacy
  documentCategory?: DocumentCategory;
  vin?: string;
  fileHash?: string;
}

const documentSchema = new Schema({
  originalName: {
    type: String,
    required: true,
  },
  path: {
    type: String,
    required: true,
  },
  mimeType: {
    // Added mimeType
    type: String,
    required: false,
  },
  candidateId: {
    type: Schema.Types.ObjectId,
    ref: 'Candidate',
  },

  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },

  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },

  vehicleId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: false,
  },
  documentCategory: {
    type: String,
    enum: Object.values(DocumentCategory),
    required: false,
  },
  vin: {
    type: String,
    index: true,
    required: false, // Will be populated after OCR
  },
  fileHash: {
    type: String,
    index: true,
    required: false, // Will be populated for duplicate checking
  },

  createdAt: { type: String, default: getCurrentDateTime },
  updatedAt: { type: String, default: getCurrentDateTime },
});

documentSchema.pre('remove', { document: true, query: false }, async function (next) {
  try {
    await deleteFileFromS3(this.path);
    // console.log(`Document ${this.originalName} removed from S3`);
    next();
  } catch (error) {
    console.error('Error:', error);

    next();
  }
});

const Document = model<DocumentMongoI>('Document', documentSchema);

export default Document;
