import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const soldPayment = new Schema({
  isWhatsappSent: {
    type: Boolean,
    default: false,
  },
  isEmailSent: {
    type: Boolean,
    default: false,
  },
  paymentDetail: {
    type: Object,
  },
  wire4Detail: {
    type: Object,
  },
  isPaid: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
soldPayment.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const SoldPayment = model('SoldPayment', soldPayment);

export default SoldPayment;
