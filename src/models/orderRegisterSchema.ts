import { Schema, model } from 'mongoose';

const orderRegisterSchema = new Schema({
  cuentaBeneficiario: {
    type: String,
    required: [true, 'Cuenta beneficiario is required'],
  },
  tipoCuentaOrdenante: {
    type: Number,
    required: [true, 'Tipo cuenta ordenante is required'],
  },
  nombreBeneficiario: {
    type: String,
    required: [true, 'Nombre beneficiario is required'],
  },
  rfcCurpBeneficiario: {
    type: String,
    required: [true, 'RFC CURP beneficiario is required'],
  },
  conceptoPago: {
    type: String,
    required: [true, 'Concepto pago is required'],
  },
  institucionOperante: {
    type: Number,
    required: [true, 'Institucion operante is required'],
  },
  referenciaNumerica: {
    type: Number,
    required: [true, 'Referencia numerica is required'],
  },
  claveRastreo: {
    type: String,
    required: [true, 'Clave rastreo is required'],
  },
  monto: {
    type: Number,
    required: [true, 'Mont<PERSON> is required'],
  },
  tipoCuentaBeneficiario: {
    type: Number,
    required: [true, 'Tipo cuenta beneficiario is required'],
  },
  institucionContraparte: {
    type: Number,
    required: [true, 'Institucion contraparte is required'],
  },
  tipoPago: {
    type: Number,
    required: [true, 'Tipo pago is required'],
  },
  cuentaOrdenante: {
    type: String,
    required: [true, 'Cuenta ordenante is required'],
  },
  empresa: {
    type: String,
    required: [true, 'Empresa is required'],
  },
  firma: {
    type: String,
    required: [true, 'Firma is required'],
  },
  stpConfirmation: {
    type: Object,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updateAt: {
    type: Date,
  },
});
orderRegisterSchema.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const OrderRegister = model('OrderRegister', orderRegisterSchema);

export default OrderRegister;
