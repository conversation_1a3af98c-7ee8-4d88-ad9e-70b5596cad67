import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const gigHistorySchema = new Schema({
  body: {
    type: Object,
    required: true,
  },
  headers: {
    type: Object,
    required: true,
  },
  method: {
    type: String,
    required: true,
  },
  url: {
    type: String,
    required: true,
  },
  status: {
    type: Number,
    required: true,
  },
  response: {
    type: Object,
    required: true,
  },
  date: {
    type: Date,
    default: getCurrentDateTime(),
  },
});

const gigHistory = model('gigHistory', gigHistorySchema);

export default gigHistory;
