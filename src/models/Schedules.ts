import mongoose, { Schema, Document } from 'mongoose';

export interface TimeRange {
  start: string;
  end: string;
}

export interface WeeklySchedule {
  monday?: TimeRange;
  tuesday?: TimeRange;
  wednesday?: TimeRange;
  thursday?: TimeRange;
  friday?: TimeRange;
  saturday?: TimeRange;
  sunday?: TimeRange;
  [key: string]: TimeRange | undefined; // Añade esta línea
}

export interface ISchedule extends Document {
  user: mongoose.Types.ObjectId;
  name: string;
  weeklySchedule: WeeklySchedule;
  maxSimultaneousAppointments: number;
  timezone: string;
  breakTimes?: Array<TimeRange>;
  bufferTime?: number;
  duration: number;
  maxAdvanceBookingDays: number;
  minBookingNoticeHours: number;
}

const ScheduleSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    name: { type: String, required: true },
    weeklySchedule: {
      monday: { start: { type: String }, end: { type: String } },
      tuesday: { start: { type: String }, end: { type: String } },
      wednesday: { start: { type: String }, end: { type: String } },
      thursday: { start: { type: String }, end: { type: String } },
      friday: { start: { type: String }, end: { type: String } },
      saturday: { start: { type: String }, end: { type: String } },
      sunday: { start: { type: String }, end: { type: String } },
    },
    maxSimultaneousAppointments: { type: Number, default: 1 },
    timezone: { type: String, required: true },
    breakTimes: [{ start: { type: String }, end: { type: String } }],
    bufferTime: { type: Number, default: 0 },
    duration: { type: Number, default: 30, required: true },
    maxAdvanceBookingDays: { type: Number, default: 5 },
    minBookingNoticeHours: { type: Number, default: 4 },
  },
  {
    timestamps: true,
  }
);

export const Schedule = mongoose.model<ISchedule>('Schedule', ScheduleSchema);
