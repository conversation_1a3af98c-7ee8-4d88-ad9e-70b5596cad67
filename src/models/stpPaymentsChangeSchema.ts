import { Schema, model } from 'mongoose';

const stpPaymentssChangeSchema = new Schema({
  id: {
    type: Number,
    required: [true, 'Id is required'],
  },
  fechaOperacion: {
    type: Date,
    required: [true, 'Fecha operacion is required'],
  },
  institucionOrdenante: {
    type: Number,
    required: [true, 'Institucion ordenante is required'],
  },
  institucionBeneficiaria: {
    type: Number,
    required: [true, 'Institucion beneficiaria is required'],
  },
  claveRastreo: {
    type: String,
    required: [true, 'Clave rastreo is required'],
  },
  monto: {
    type: Number,
    required: [true, '<PERSON><PERSON> is required'],
  },
  nombreOrdenante: {
    type: String,
  },
  tipoCuentaOrdenante: {
    type: Number,
  },
  cuentaOrdenante: {
    type: Number,
  },
  rfcCurpOrdenante: {
    type: String,
  },
  nombreBeneficiario: {
    type: String,
    required: [true, 'Nombre beneficiario is required'],
  },
  tipoCuentaBeneficiario: {
    type: Number,
    required: [true, 'Tipo cuenta beneficiario is required'],
  },
  cuentaBeneficiario: {
    type: String,
    required: [true, 'Cuenta beneficiario is required'],
  },
  nombreBeneficiario2: {
    type: String,
  },
  tipoCuentaBeneficiario2: {
    type: Number,
  },
  cuentaBeneficiario2: {
    type: String,
  },
  rfcCurpBeneficiario: {
    type: String,
    required: [true, 'Rfc curp beneficiario is required'],
  },
  conceptoPago: {
    type: String,
    required: [true, 'Concepto pago is required'],
  },
  referenciaNumerica: {
    type: Number,
    required: [true, 'Referencia numerica is required'],
  },
  empresa: {
    type: String,
    required: [true, 'Empresa is required'],
  },
  tipoPago: {
    type: Number,
    required: [true, 'Tipo pago is required'],
  },
  tsLiquidacion: {
    type: Date,
    required: [true, 'Ts liquidacion is required'],
  },
  folioCodi: {
    type: String,
  },
});
stpPaymentssChangeSchema.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const StpPaymentssChangeSchema = model('StpPaymentssChangeSchema', stpPaymentssChangeSchema);

export default StpPaymentssChangeSchema;
