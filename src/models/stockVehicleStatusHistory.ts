import mongoose from 'mongoose';
// import { getCurrentDateTime } from '../services/timestamps';
import StockVehicle from './StockVehicleSchema';
import User from './userSchema';

interface StockVehicleStatusHistoryI extends mongoose.Document {
  _id: mongoose.Schema.Types.ObjectId;
  stockId: mongoose.Schema.Types.ObjectId | string;
  associateId: mongoose.Schema.Types.ObjectId | string;
  userId: mongoose.Schema.Types.ObjectId | string;
  status: string;
  step: string;
  comments: string;
  category: string;
  subCategory: string;
  previousStatus: string;
  previousStep: string;
  previousSubCategory: string;
  previousCategory: string;
  isCompleted: boolean;
  dateIn: Date;
  dateOut: Date;
  tentativeDateOut: Date;
  isCanceled: boolean;
  canceledReason?: string;
  canceledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const stockVehicleStatusHistorySchema = new mongoose.Schema<StockVehicleStatusHistoryI>({
  stockId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: StockVehicle.modelName,
    required: true,
  },

  associateId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: StockVehicle.modelName,
    required: true,
  },

  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: User.modelName,
    required: true,
  },

  isCompleted: {
    type: Boolean,
    default: true,
  },

  previousStatus: {
    type: String,
    default: null,
  },

  previousStep: {
    type: String,
    default: null,
  },

  previousCategory: {
    type: String,
    default: null,
  },

  previousSubCategory: {
    type: String,
    default: null,
  },

  status: {
    type: String,
    default: 'active',
  },

  comments: {
    type: String,
    default: '',
  },

  step: {
    type: String,
    default: null,
  },

  category: {
    type: String,
    required: false,
    default: null,
  },

  subCategory: {
    type: String,
    required: false,
    default: null,
  },

  dateIn: {
    type: Date,
    required: false,
    default: null,
  },

  tentativeDateOut: {
    type: Date,
    default: null,
  },

  dateOut: {
    type: Date,
    default: null,
  },

  canceledReason: {
    type: String,
    default: null,
  },

  isCanceled: {
    type: Boolean,
    default: false,
  },

  canceledAt: {
    type: Date,
    default: null,
  },

  createdAt: {
    type: Date,
    default: Date.now,
  },

  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

const StockVehicleStatusHistory = mongoose.model(
  'StockVehicleStatusHistory',
  stockVehicleStatusHistorySchema
);

export default StockVehicleStatusHistory;
