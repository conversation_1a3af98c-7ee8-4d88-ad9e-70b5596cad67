import { Types } from 'mongoose';

import { Document, Schema, model } from 'mongoose';

export interface MetricMongoI extends Document {
  _id: Types.ObjectId;
  platform: string;
  requestId: Types.ObjectId;
  acceptanceRate: number;
  cancellationRate: number;
  rating: number;
  lifetimeTrips: number;
  timeSinceFirstTrip: number;
  activationStatus: string;
  createdAt: Date;
  updatedAt: Date;
}

export const metricsSchema = new Schema(
  {
    platform: { type: String, required: true },
    requestId: {
      type: Types.ObjectId,
      required: true,
      ref: 'AdmissionRequest',
    },
    acceptanceRate: { type: Number },
    cancellationRate: { type: Number },
    rating: { type: Number },
    lifetimeTrips: { type: Number },
    timeSinceFirstTrip: { type: Number },
    activationStatus: { type: String },
  },
  { timestamps: true }
);

export const MetricMongo = model<MetricMongoI>('Metric', metricsSchema, 'metrics');
