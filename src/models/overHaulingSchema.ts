import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const overhaulingSchema = new Schema({
  dateIn: {
    type: String,
    required: true,
  },

  dateOut: {
    type: String,
  },

  inImgs: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
      },
    ],
  },

  outImgs: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
      },
    ],
  },

  invoiceFile: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },

  invoiceAmount: {
    type: Number,
  },

  hasInvoice: {
    type: Boolean,
  },

  quotationDoc: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },

  comments: {
    type: String,
  },

  commentsOut: {
    type: String,
  },

  stockId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: true,
  },

  sameVehicleCount: {
    type: Number,
  },

  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

// before create hook
overhaulingSchema.pre('save', function (next) {
  // Verifica que es nueva inserción, si es nueva inserción y no existe hasInvoice, agregarla y ponerla en false
  if (this.isNew && !this.hasInvoice) {
    this.hasInvoice = false;
  }
  next();
});

const OverHauling = model('overhauling', overhaulingSchema);

export default OverHauling;
