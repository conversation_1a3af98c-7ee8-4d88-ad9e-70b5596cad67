import mongoose, { Schema, Document } from 'mongoose';

// Define an interface for the schema
export interface IFileUploadRetries extends Document {
  requestId: string;
  platform: string;
  uploadType: string;
  fileMetaData: {
    originalName: string;
    mimeType: string;
    contentLength: number;
  };
  count: number;
}

// Create a schema for the file upload
const FileUploadRetriesSchema: Schema = new Schema(
  {
    requestId: { type: String, required: true },
    platform: { type: String, required: true },
    uploadType: { type: String, required: true },
    fileMetaData: {
      originalName: { type: String, required: true },
      mimeType: { type: String, required: true },
      contentLength: { type: Number, required: true },
    },
    count: { type: Number, requied: true },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt fields
  }
);

// Create a model using the schema
export const FileUploadRetriesRep = mongoose.model<IFileUploadRetries>(
  'FileUploadRetries',
  FileUploadRetriesSchema
);
