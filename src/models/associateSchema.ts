import { Schema, model } from 'mongoose';
import mt from 'moment-timezone';
import { deleteFolderContentsRecursive } from '../aws/s3';
import Document from './documentSchema';
import { Types } from 'mongoose';
import { Countries, CountriesEnum } from '../constants';
import { AdmissionRequestMongo } from './admissionRequestSchema';

const timeZone = 'America/Mexico_City';

function getCurrentDateTime() {
  return mt().tz(timeZone).toDate();
}

const associatedSchema = new Schema({
  _id: {
    type: Schema.Types.ObjectId,
    required: true,
  },
  requestId: {
    type: Schema.Types.ObjectId,
    ref: 'AdmissionRequest',
  },
  vehiclesId: [
    {
      type: Schema.Types.ObjectId,
      ref: 'StockVehicle',
    },
  ],
  firstName: {
    type: String,
    required: [true, 'First name is required'],
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
  },
  email: {
    type: String,
    unique: true,
    required: [true, 'Email is required'],
  },
  birthDay: {
    type: String,
    required: [true, 'Birth day is required'],
  },
  curp: {
    type: String, // required when country is Mexico
  },
  phone: {
    type: String,
    required: [true, 'Phone is required'],
  },
  rfc: {
    type: String, // required when country is Mexico
  },

  tax_system: {
    type: String,
    default: '616',
  },

  legal_name: {
    type: String,
  },

  use_cfdi: {
    type: String,
    default: 'G03',
  },

  addressStreet: {
    type: String,
    required: [true, 'Address street is required'],
  },
  exterior: {
    type: String,
    required: [false, 'Exterior is required'], // required when country is Mexico
    default: 'N/A',
  },
  interior: {
    type: String,
  },
  colony: {
    type: String, // required when country is Mexico
  },
  delegation: {
    type: String, // required when country is Mexico
  },
  postalCode: {
    type: String,
    required: [true, 'Postal code is required'],
  },
  city: {
    type: String,
    required: [true, 'City is required'],
  },
  state: {
    type: String,
    required: [true, 'State is required'],
  },
  active: {
    type: Boolean,
    default: true,
  },
  image: {
    type: String,
    default:
      'https://w7.pngwing.com/pngs/81/570/png-transparent-profile-logo-computer-icons-user-user-blue-heroes-logo-thumbnail.png',
  },
  picture: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },
  unSignedContractDoc: {
    type: Schema.Types.ObjectId || null,
    ref: 'Document',
    default: null,
  },
  documents: {
    ineFront: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    ineBack: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    curp: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    taxStatus: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    addressVerification: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    driverLicenseFront: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    driverLicenseBack: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    garage: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
  },
  bankStatement: {
    bankStatementOne: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementTwo: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementThree: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementFour: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementFive: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementSix: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
  },
  deliveredImages: {
    type: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Documents',
        default: [],
      },
    ],
  },
  signDocs: {
    contract: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
    },
    promissoryNote: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
    },
    deliveryReceipt: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
    },
    warranty: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
    },
    invoice: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
    },
    privacy: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
    },
    contactInfo: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
    },
  },
  oldDocuments: {
    type: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Documents',
      },
    ],
    default: [],
  },
  readmissions: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'Readmission',
        },
      },
    ],
  },

  clientId: {
    type: String,
    default: null,
  },

  adendumDocs: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'Document',
        },
      },
    ],
    default: [],
  },

  adendumDigitalSignature: {
    // type: {
    //   participants: {
    //     type: [
    //       {
    //         name: {
    //           type: String,
    //           required: true,
    //         },
    //         email: {
    //           type: String,
    //           required: true,
    //         },
    //         urlSign: {
    //           type: String,
    //           required: true,
    //         },
    //         signed: {
    //           // This is the status of every participant signed status
    //           type: Boolean,
    //           default: false,
    //         },
    //       },
    //     ],
    //   },

    //   documentID: {
    //     type: String,
    //   },
    //   promissoryNoteDocID: {
    //     type: String,
    //   },
    //   url: {
    //     type: String,
    //   },
    //   signed: {
    //     // This is the status of the complete document signed status
    //     type: Boolean,
    //     default: false,
    //   },
    //   isSent: {
    //     type: Boolean,
    //     default: false,
    //   },
    // },
    // select: false,
    // required: false,
    // default: {
    //   documentID: null,
    //   promissoryNoteDocID: null,
    //   url: null,
    //   signed: false,
    //   participants: [],
    // },

    type: [
      {
        participants: [
          {
            name: {
              type: String,
              required: true,
            },
            email: {
              type: String,
              required: true,
            },
            urlSign: {
              type: String,
              required: true,
            },
            signed: {
              // This is the status of every participant signed status
              type: Boolean,
              default: false,
            },
          },
        ],

        documentID: {
          type: String,
          required: true,
          default: '',
        },
        url: {
          type: String,
          // required: true,
          default: '',
        },
        signed: {
          // This is the status of the complete document signed status
          type: Boolean,
          default: false,
        },
        isSent: {
          type: Boolean,
          default: false,
        },
      },
    ],
    required: false,
    default: [],
    select: false,
  },

  avalData: {
    type: {
      name: {
        type: String,
      },
      email: {
        type: String,
      },
      phone: {
        type: String,
      },
      address: {
        type: String,
      },
      ine: {
        type: Schema.Types.ObjectId,
        ref: 'Document',
      },
    },
    select: false,
    default: {
      name: null,
      email: null,
      phone: null,
      address: null,
      ine: null,
    },
  },

  contacts: {
    type: [
      {
        name: {
          type: String,
          required: true,
        },
        kinship: {
          type: String,
          required: true,
        },
        phone: {
          type: String,
          required: true,
        },
        address: {
          type: String,
          required: true,
        },
      },
    ],
    default: [],
    required: false,
  },

  digitalSignature: {
    type: {
      participants: {
        type: [
          {
            name: {
              type: String,
              required: true,
            },
            email: {
              type: String,
              required: true,
            },
            urlSign: {
              type: String,
              required: true,
            },
            signed: {
              // This is the status of every participant signed status
              type: Boolean,
              default: false,
            },
          },
        ],
      },

      documentID: {
        type: String,
      },
      promissoryNoteDocID: {
        type: String,
      },
      url: {
        type: String,
      },
      signed: {
        // This is the status of the complete document signed status
        type: Boolean,
        default: false,
      },
      isSent: {
        type: Boolean,
        default: false,
      },
    },
    select: false,
    default: {
      documentID: null,
      promissoryNoteDocID: null,
      url: null,
      signed: false,
      participants: [],
    },
  },

  /**
   * US fields registeration
   */
  country: {
    type: String,
    enum: Countries,
    default: CountriesEnum.Mexico,
  },
  admissionRequestId: {
    type: Schema.Types.ObjectId,
    ref: 'AdmissionRequest',
  },
  typeOfPreapproval: { type: String },
  isVerified: { type: Boolean, default: false },
  createdAt: { type: String, default: getCurrentDateTime },
  updatedAt: { type: String, default: getCurrentDateTime },
});

associatedSchema.pre('remove', { document: true, query: false }, async function (next) {
  const id = this._id;
  const allDocsRelated = await Document.find({ associateId: id });

  if (allDocsRelated.length === 0) next();
  const carNumber = allDocsRelated[0].path.split('/')[1];
  const associateFolder = `associate/${carNumber}/${this.email}`;
  for (const doc of allDocsRelated) {
    // await deleteFolderContentsRecursive()
    // console.log('DOCUMENTO ELIMINADO: ', doc._id, doc.path);
    await doc.remove();
  }
  // console.log('[CARPETA DE ASSOCIATE] 🧑🧑 ', associateFolder);
  await deleteFolderContentsRecursive(associateFolder);

  // Check if there is an admissionRequest related to this associate
  const admissionRequest = await AdmissionRequestMongo.findOne({
    $or: [
      { associateId: id, convertedToAssociate: true },
      { 'personalData.email': this.email, convertedToAssociate: false },
    ],
  }).select('associateId convertedToAssociate');

  if (admissionRequest) {
    // remove the relation and convertedToAssociate
    admissionRequest.associateId = undefined;
    admissionRequest.convertedToAssociate = false;
    await admissionRequest.save();
  }

  next();
});

const Associate = model('Associate', associatedSchema);

const instance = new Associate();

export type AssociateInstanceType = typeof instance;

// Extender el tipo omitiendo la propiedad y redefiniéndola
export type IAssociate = Omit<AssociateInstanceType, 'unSignedContractDoc'> & {
  unSignedContractDoc: Types.ObjectId | null;
  clientId: string | null;
};

export default Associate as AssociateInstanceType & typeof Associate;
