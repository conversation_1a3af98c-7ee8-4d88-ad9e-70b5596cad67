import mongoose, { Schema, Document } from 'mongoose';

// Define the Variation interface
export interface IVariation {
  version: string;
  year: number;
}

// Define the Model interface
export interface IModel {
  name: string;
  variations: IVariation[];
}

// Define the Car interface
export interface ICar extends Document {
  make: string;
  models: IModel[];
}

// Define the Variation schema
const VariationSchema: Schema = new Schema({
  version: { type: String, required: true },
  year: { type: Number, required: true },
});

// Define the Model schema
const ModelSchema: Schema = new Schema({
  name: { type: String, required: true },
  variations: [VariationSchema],
});

// Define the Car schema
const CarSchema: Schema = new Schema({
  make: { type: String, required: true },
  models: [ModelSchema],
});

// Export the Car model
export const Car = mongoose.model<ICar>('Car', CarSchema);
