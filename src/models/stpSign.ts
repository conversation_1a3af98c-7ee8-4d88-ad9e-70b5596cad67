import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const stpSign = new Schema({
  originalString: {
    type: String,
    required: true,
  },
  signatureB64: {
    type: String,
    required: true,
  },
  user: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
});
stpSign.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const StpSign = model('StpSign', stpSign);

export default StpSign;
