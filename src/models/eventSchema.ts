import { Types } from 'mongoose';

import { Document, Schema, model } from 'mongoose';
import { UserMongoI } from './userSchema';

export interface EventMongoI extends Document {
  id?: string | undefined;
  user: UserMongoI;
  entityId: string;
  entityType: string;
  actionType: string;
  message: string;
  createdAt: Date;
  updatedAt: Date;
}

const eventsSchema = new Schema(
  {
    user: {
      type: Types.ObjectId,
      required: true,
      ref: 'User',
    },
    entityId: {
      type: Types.ObjectId,
      required: true,
    },
    entityType: {
      type: String,
      required: true,
    },
    actionType: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
    toObject: {
      virtuals: true,
    },
  }
);

export const EventMongo = model<EventMongoI>('Event', eventsSchema, 'events');
