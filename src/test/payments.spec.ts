import request from 'supertest';
import app from '../app';
import jwt from 'jsonwebtoken';
import { createAssociateTest /*createRegionTest*/, stockVehicleTestSureste } from './functionts';
import { arrayPayments } from './testFiles/arrayPayments';
import { accessTokenSecret /*associatePaymentsConsts*/, genericMessages } from '../constants';
// import {
//   otherPaymentObject,
//   secondPaymentObject,
//   secondPaymentObjectSucceedeed,
//   secondPaymentObjectUpdated,
// } from './testFiles/testPayments';
let token: string = '';
let carNumber: string = '';
let stockId: string = '';
let associateId: string = '';
let userAdminId: string = '';
let bodyData: any = '';
// let regionCode: any = '';
// let associateEmail: string = '';

beforeAll(async () => {
  try {
    // Generar token
    token = jwt.sign({ userId: userAdminId }, accessTokenSecret, {
      expiresIn: '2m',
    });

    // Crear asociado y obtener datos de vehículo
    const associateTest = await createAssociateTest();
    const stockTest = await stockVehicleTestSureste();
    // const region = await createRegionTest();
    // regionCode = region.region;
    // Asignar valores a las variables
    if (associateTest) {
      associateId = associateTest._id.toString();
      // associateEmail = associateTest.email;
    }

    if (stockTest) {
      carNumber = stockTest.carNumber;
      stockId = stockTest._id.toString();
    }
    bodyData = [
      {
        contractNumber: carNumber,
        stockId: stockId,
        weeklyRent: 1420,
        finalPrice: 150000,
        allPayments: arrayPayments,
        downPayment: 10,
        totalPrice: 123456,
        deliveredDate: '12/12/25',
      },
      {
        contractNumber: carNumber,
        stockId: stockId,
        associatedId: associateId,
        documentId: '6480b25a4ab4af192b1003ac',
        weeklyRent: 1420,
        finalPrice: 150000,
        allPayments: arrayPayments,
        downPayment: 10,
        totalPrice: 123456,
        deliveredDate: '12/12/25',
      },
    ];
  } catch (error) {
    // Manejar errores aquí
    console.error('Error en la inicialización:', error);
  }
});

describe('POST /contract/associated/add', () => {
  let response: request.Response;

  it('should respond with 401 status code if token not provided', async () => {
    try {
      response = await request(app).post('/contract/associated/add').send(bodyData);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('Should response with 400 status code if not body provided', async () => {
    try {
      response = await request(app)
        .post('/contract/associated/add')
        .set('Authorization', `Bearer ${token}`)
        .send(bodyData[0]);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: genericMessages.errors.missingBody });
    }
  });

  it('should response with 200 status code', async () => {
    try {
      response = await request(app)
        .post('/contract/associated/add')
        .set('Authorization', `Bearer ${token}`)
        .send(bodyData[1]);
    } catch (error) {
      throw new Error('Error al crear el contrato');
    }
  });
});

// describe('Should go trough the gigstack flow for a normal week', () => {
//   let response: request.Response;
//   it('Updates the payment array and updates the balance with the total of the payment', async () => {
//     response = await request(app).post(`/gighooks/regularPayment/${regionCode}`).send(secondPaymentObject);
//     expect(response.status).toBe(200);
//     expect(response.body.paymentToUpdate.weeklyCost).toBe(3450);
//     expect(response.body.associatePayments.balance).toBeLessThan(0);
//   });
//   it('Adds a second payment during that specific week and it should be on otherPayments', async () => {
//     response = await request(app).post(`/gighooks/regularPayment/${regionCode}`).send(otherPaymentObject);
//     expect(response.status).toBe(200);
//     expect(response.body.message).toBe(associatePaymentsConsts.errors.duplicatedPayment);
//   });
//   it('Updates the status of the payment', async () => {
//     response = await request(app).post(`/gighooks/update/${regionCode}`).send(secondPaymentObjectUpdated);
//     expect(response.status).toBe(200);
//     expect(response.body.updatedPayment[0].status).toBe('pending_review');
//   });
//   // // it('Recieves money from wire4 webhook and update associate balance')
//   it('Changes the status of the payment to succedeed', async () => {
//     response = await request(app).post(`/gighooks/update/${regionCode}`).send(secondPaymentObjectSucceedeed);
//     expect(response.status).toBe(200);
//     expect(response.body.updatedPayment[0].status).toBe('succeedeed');
//   });
//   it('Adds the payment to the history', async () => {
//     response = await request(app).post('/payments/gigstack/success').send(secondPaymentObjectSucceedeed);
//     expect(response.status).toBe(200);
//     expect(response.body.message).toBe(associatePaymentsConsts.success.historyPaymentAdded);
//     expect(response.body.paymentAdded.transactionAmount).toBe(3450);
//   });
// });
