import request from 'supertest';
import app from '../app';
import bcrypt from 'bcrypt';
import User from '../models/userSchema';

beforeAll(async () => {
  const password = 'mypassword';
  const hashedPassword = await bcrypt.hash(password, 12);
  await User.create({
    email: 'email<PERSON>rue<PERSON>@gmail.com',
    name: '<PERSON><PERSON><PERSON>',
    password: hashedPassword,
    role: 'agent',
    city: 'cdmx',
    settings: {
      allowedRegions: ['cdmx', 'gdl', 'mty', 'tij', 'pbc', 'pbe', 'qro'],
    },
  });
});

describe('GET /vehicles/get', () => {
  it('should respond with 200 status code', async () => {
    const response = await request(app).get('/');
    expect(response.status).toBe(200);
  });
});
