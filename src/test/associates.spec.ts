import app from '../app';
import request from 'supertest';
import jwt from 'jsonwebtoken';
import { createAdminTest, createAdmissionRequestTest, stockVehicleTest } from './functionts';
import { associateText, accessTokenSecret } from '../constants';
import { Types } from 'mongoose';

let userAdminId: string;
let stockVehicleId: string;

beforeAll(async () => {
  const adminUser = await createAdminTest();
  const stockVehicle = await stockVehicleTest();

  userAdminId = adminUser._id.toString();
  stockVehicleId = stockVehicle._id.toString();
});

describe('Get associate by id GET /associate/getAssociate/:id', () => {
  let response: request.Response;
  const token = jwt.sign({ userAdminId }, accessTokenSecret, {
    expiresIn: '2m',
  });
  it('should return 400 status code and associate not found message if associate does not exist', async () => {
    const objectId = new Types.ObjectId();
    response = await request(app)
      .get(`/associate/getAssociate/${objectId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({ message: associateText.errors.associateNotFound });
  });
});

// GET ALL ASSOCIATES

describe('Get all associates GET /associate/getAllAssociate', () => {
  let response: request.Response;
  const token = jwt.sign({ userAdminId }, accessTokenSecret, {
    expiresIn: '2m',
  });
  it('should return 401 status code and not authorized if is not and admin', async () => {
    response = await request(app).get(`/associate/getAllAssociates`).set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({ message: associateText.errors.notAutorized });
  });
});

describe('Assign admission request to vehicle as associate POST /associate/assignAssociate', () => {
  let response: request.Response;
  const token = jwt.sign({ userAdminId }, accessTokenSecret, {
    expiresIn: '2m',
  });

  it('should return 400 status code if body is missing documents analysis array', async () => {
    try {
      const admissionRequest = await createAdmissionRequestTest();
      const postBody = {
        vehicleId: stockVehicleId,
        userId: userAdminId,
        personalData: {
          ...admissionRequest?.personalData.toObject(),
          birthdate: '1995-03-10',
          street: 'street 23',
          streetNumber: '34',
          postalCode: '45000',
          nationalId: '32dsfds',
          taxId: '33ff',
          municipality: 'new',
          neighborhood: 'mexican',
        },
        avalData: {
          name: 'Test',
          location: 'test',
          email: 'test@yopmail',
          phone: '1234567890',
        },
        requestId: admissionRequest._id,
      };
      response = await request(app)
        .post(`/associate/assignAssociate`)
        .set('Authorization', `Bearer ${token}`)
        .send(postBody);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'Missing associate documents' });
    }
  });

  it('should return 400 status code if personal data object is missing required fields', async () => {
    try {
      const admissionRequest = await createAdmissionRequestTest();
      const postBody = {
        vehicleId: stockVehicleId,
        userId: userAdminId,
        personalData: {
          ...admissionRequest?.personalData.toObject(),
          streetNumber: '34',
          nationalId: '32dsfds',
          taxId: '33ff',
          municipality: 'new',
          neighborhood: 'mexican',
        },
        avalData: {
          name: 'Test',
          location: 'test',
          email: 'test@yopmail',
          phone: '1234567890',
        },
        documentsAnalysis: admissionRequest.documentsAnalysis,
        requestId: admissionRequest._id,
      };
      response = await request(app)
        .post(`/associate/assignAssociate`)
        .set('Authorization', `Bearer ${token}`)
        .send(postBody);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Missing required fields in personal data');
      expect(response.body.message).toContain('Please fill in all required information.');
    }
  });

  it('should return 400 status code if body is missing admission request id', async () => {
    try {
      const admissionRequest = await createAdmissionRequestTest();
      const postBody = {
        vehicleId: stockVehicleId,
        userId: userAdminId,
        personalData: {
          ...admissionRequest?.personalData.toObject(),
          birthdate: '1995-03-10',
          street: 'street 23',
          streetNumber: '34',
          postalCode: '45000',
          nationalId: '32dsfds',
          taxId: '33ff',
          municipality: 'new',
          neighborhood: 'mexican',
        },
        avalData: {
          name: 'Test',
          location: 'test',
          email: 'test@yopmail',
          phone: '1234567890',
        },
        documentsAnalysis: admissionRequest.documentsAnalysis,
      };
      response = await request(app)
        .post(`/associate/assignAssociate`)
        .set('Authorization', `Bearer ${token}`)
        .send(postBody);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'Missing associate documents' });
    }
  });

  it('should return 400 status code if admission country is mexico and nationalId is missing', async () => {
    try {
      const admissionRequest = await createAdmissionRequestTest();
      const postBody = {
        vehicleId: stockVehicleId,
        userId: userAdminId,
        personalData: {
          ...admissionRequest?.personalData.toObject(),
          birthdate: '1995-03-10',
          street: 'street 23',
          streetNumber: '34',
          postalCode: '45000',
          municipality: 'new',
          neighborhood: 'mexican',
          country: 'mx',
        },
        avalData: {
          name: 'Test',
          location: 'test',
          email: 'test@yopmail',
          phone: '1234567890',
        },
        documentsAnalysis: admissionRequest.documentsAnalysis,
        requestId: admissionRequest._id,
      };
      response = await request(app)
        .post(`/associate/assignAssociate`)
        .set('Authorization', `Bearer ${token}`)
        .send(postBody);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Missing required fields for Mexico');
      expect(response.body.message).toContain('nationalId');
    }
  });
});
