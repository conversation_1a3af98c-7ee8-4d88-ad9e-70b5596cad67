import app from '../app';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { accessTokenSecret, stockVehiclesText } from '../constants';
import { /* createAassociateTest, */ createAdminTest, stockVehicleTest } from './functionts';

let userAdminId: string = '';
let stockVehicleId: string = '';
// let associateId: string = '';

let token: string = '';

beforeAll(async () => {
  const adminUser = await createAdminTest();
  const stockVehicle = await stockVehicleTest();

  // const associate = await createAassociateTest();

  // associateId = associate._id.toString();
  userAdminId = adminUser._id.toString();
  stockVehicleId = stockVehicle._id.toString();
  token = jwt.sign({ userId: userAdminId }, accessTokenSecret, {
    expiresIn: '2m',
  });
});

describe('POST /stock/add', () => {
  const filePath = `${__dirname}/testFiles/test.pdf`;

  const stockVehicles = [
    {
      model: 'Test model',
      brand: 'brand test',
      color: 'purple',
      vin: '123456' + Math.floor(Math.random() * 1000),
      vehicleState: 'cdmx',
      owner: 'OCN',
      billAmount: '123456',
      km: 12,
      carNumber: 1002,
      region: '1',
      year: '2023',
      version: 'sedan',
      historyData: {
        step: 'test',
        description: 'test vehicle added',
        userId: '645bb4bd4d7cd6f6896632d7',
      },
      country: 'Mexico',
      state: '',
    },
  ];
  let response: request.Response;
  it('should respond with 401 status code if token not provided', async () => {
    try {
      response = await request(app).post('/stock/add').send(stockVehicles[0]);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('Should response with a 400 status code if body params not provided', async () => {
    try {
      response = await request(app).post('/stock/add').set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(400);
    }
  });

  it('should respond with a 400 status code if missing body property', async () => {
    try {
      response = await request(app)
        .post('/stock/add')
        .set('Authorization', `Bearer ${token}`)
        .attach('bill', filePath)
        .field('model', stockVehicles[0].model)
        .field('region', stockVehicles[0].region)
        .field('version', stockVehicles[0].version)
        .field('vin', stockVehicles[0].vin)
        .field('vehicleState', stockVehicles[0].vehicleState)
        .field('owner', stockVehicles[0].owner)
        .field('billAmount', stockVehicles[0].billAmount)
        .field('km', stockVehicles[0].km)
        .field('year', stockVehicles[0].year)
        .field('historyData[userId]', stockVehicles[0].historyData.userId);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors).not.toHaveLength(0);
    }
  });

  it('Should response with a 201 status code', async () => {
    try {
      response = await request(app)
        .post('/stock/add')
        .set('Authorization', `Bearer ${token}`)
        .attach('bill', filePath)
        .field('model', stockVehicles[0].model)
        .field('brand', stockVehicles[0].brand)
        .field('color', stockVehicles[0].color)
        .field('region', stockVehicles[0].region)
        .field('version', stockVehicles[0].version)
        .field('vin', stockVehicles[0].vin)
        .field('vehicleState', stockVehicles[0].vehicleState)
        .field('owner', stockVehicles[0].owner)
        .field('billAmount', stockVehicles[0].billAmount)
        .field('km', stockVehicles[0].km)
        .field('year', stockVehicles[0].year)
        .field('carNumber', stockVehicles[0].carNumber)
        .field('historyData[step]', stockVehicles[0].historyData.step)
        .field('historyData[description]', stockVehicles[0].historyData.description)
        .field('historyData[userId]', stockVehicles[0].historyData.userId)
        .field('country', stockVehicles[0].country);

      expect(response.status).toBe(201);
      expect(response.body.stock.bill).toBeDefined();
    } catch (error) {
      console.log(response.body, 'Error al crear el vehiculo con status 201');
      throw new Error('Fallo al crear el vehiculo');
    }
  });
});

describe('GET /stock/get', () => {
  let response: request.Response;

  it('Should response with a 401 status code if token is not provided', async () => {
    try {
      response = await request(app).get('/stock/get');
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  //todo : fix the verifyToken problem:
  it('should response with a 200 status code', async () => {
    response = await request(app)
      .get('/stock/get')
      .set('Authorization', `Bearer ${token}`)
      .set('userId', userAdminId.toString());
    // console.log(response, '<==== RESPONSE DEL GET');
    expect(response.status).toBe(200);
  });
});

describe('PATCH /stock/description/:id', () => {
  let response: request.Response;

  it('Should response with 401 status code if token not provied', async () => {
    try {
      const patchBody = {
        brand: 'testUpdate',
        year: '2023',
        model: 'testUpdate',
        historyData: {
          step: 'test',
          description: 'test vehicle updated',
          userId: '645bb4bd4d7cd6f6896632d7',
        },
      };
      response = await request(app)
        .patch(`/stock/update/description/${stockVehicleId}`)
        .field('brand', patchBody.brand)
        .field('year', patchBody.year)
        .field('model', patchBody.model);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('Should response with a 400 status code if not body provided', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/description/${stockVehicleId.toString()}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors).not.toHaveLength(0);
    }
  });

  it('Should responde with a 404 status code if the stock Vehicle is not found', async () => {
    try {
      const patchBody = {
        brand: 'testUpdate',
        year: '2023',
        model: 'testUpdate',
        historyData: {
          step: 'test',
          description: 'test vehicle updated',
          userId: '645bb4bd4d7cd6f6896632d7',
        },
      };
      response = await request(app)
        .patch(`/stock/update/description/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .field('brand', patchBody.brand)
        .field('year', patchBody.year)
        .field('model', patchBody.model)
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('Should response with 201 status code', async () => {
    try {
      const patchBody = {
        brand: 'testUpdate',
        year: '2023',
        model: 'testUpdate',
        historyData: {
          step: 'test',
          description: 'test vehicle updated',
          userId: '645bb4bd4d7cd6f6896632d7',
        },
      };
      response = await request(app)
        .patch(`/stock/update/description/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('brand', patchBody.brand)
        .field('year', patchBody.year)
        .field('model', patchBody.model)
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
      expect(response.status).toBe(200);
    } catch (error) {
      console.log('Fallo en el 201 actualizar vehiculo', response.body);
      throw new Error('Fallo al editar el vehiculo');
    }
  });
});

describe('PATCH /stock/update/tenancy/:vehicleId', () => {
  let response: request.Response;
  const filePath = `${__dirname}/testFiles/test.pdf`;

  it('Should response with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/update/tenancy/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });
  it('should response with a 400 if not body provided', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/tenancy/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors).not.toHaveLength(0);
    }
  });

  it('should response with a 404 if stock vehicle not found', async () => {
    try {
      const patchBody = {
        validity: '2023-09-28',
        payment: 1030,
        historyData: {
          step: 'test',
          description: 'test vehicle updated',
          userId: '645bb4bd4d7cd6f6896632d7',
        },
      };
      response = await request(app)
        .patch(`/stock/update/tenancy/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .attach('tenancyDocument', filePath)
        .field('validity', patchBody.validity)
        .field('payment', patchBody.payment)
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('Should create the tenancy', async () => {
    try {
      const patchBody = {
        validity: '2023-09-28',
        payment: 1030,
        historyData: {
          step: 'test',
          description: 'test vehicle updated',
          userId: '645bb4bd4d7cd6f6896632d7',
        },
      };
      response = await request(app)
        .patch(`/stock/update/tenancy/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .attach('tenancyDocument', filePath)
        .field('validity', patchBody.validity)
        .field('payment', patchBody.payment)
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ message: stockVehiclesText.success.vehicleUpdated });
    } catch (error) {
      console.log('Fallo en crear la tenencia con 201', response.body);
      throw new Error('Fallo al crear la tenencia');
    }
  });
});

describe('PATCH stock/update/policy/:vehicleId', () => {
  let response: request.Response;
  const filePath = `${__dirname}/testFiles/test.pdf`;

  it('Should response with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/update/policy/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should response with a 400 if not body provided', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/policy/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors).not.toHaveLength(0);
    }
  });

  it('should response with a 404 if stock vehicle not found', async () => {
    try {
      const patchBody = {
        policyNumber: '5268566',
        insurer: 'Qualitas',
        broker: 'Otro',
        validity: '2023-09-28',
        historyData: {
          step: 'test',
          description: 'test vehicle updated',
          userId: '645bb4bd4d7cd6f6896632d7',
        },
      };
      response = await request(app)
        .patch(`/stock/update/policy/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .attach('policyDocument', filePath)
        .field('policyNumber', patchBody.policyNumber)
        .field('insurer', patchBody.insurer)
        .field('broker', patchBody.broker)
        .field('validity', patchBody.validity)
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('Should create the policy', async () => {
    try {
      const patchBody = {
        policyNumber: '5268566',
        insurer: 'Qualitas',
        broker: 'Otro',
        validity: '2023-09-28',
        historyData: {
          step: 'test',
          description: 'test vehicle updated',
          userId: '645bb4bd4d7cd6f6896632d7',
        },
      };
      response = await request(app)
        .patch(`/stock/update/policy/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .attach('policyDocument', filePath)
        .field('policyNumber', patchBody.policyNumber)
        .field('insurer', patchBody.insurer)
        .field('broker', patchBody.broker)
        .field('validity', patchBody.validity)
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);

      expect(response.status).toBe(200);
      expect(response.body).toEqual({ message: stockVehiclesText.success.vehicleUpdated });
    } catch (error) {
      console.log('Fallo en crear la poliza con 201', response.body);
      throw new Error('Fallo en crear poliza');
    }
  });
});

describe('PATCH /stock/update/circulationCard/:vehicleId', () => {
  let response: request.Response;
  const filePath = `${__dirname}/testFiles/test.png`;
  const filePathTwo = `${__dirname}/testFiles/test2.png`;

  const patchBody = {
    number: 'H331FA',
    validity: '2023-09-28',
    isEditing: '',
    historyData: {
      step: 'test',
      description: 'test vehicle updated',
      userId: '645bb4bd4d7cd6f6896632d7',
    },
  };

  it('Should response with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/update/circulationCard/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should response with a 400 if not body provided', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/circulationCard/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors).not.toHaveLength(0);
    }
  });

  it('should response with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/circulationCard/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .attach('frontImg', filePath)
        .attach('backImg', filePathTwo)
        .field('number', patchBody.number)
        .field('isEditing', JSON.stringify(patchBody.isEditing))
        .field('validity', patchBody.validity)
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should create the circulation card', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/circulationCard/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .attach('frontImg', filePath)
        .attach('backImg', filePathTwo)
        .field('number', patchBody.number)
        .field('isEditing', JSON.stringify(patchBody.isEditing))
        .field('validity', patchBody.validity)
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ message: stockVehiclesText.success.vehicleUpdated });
    } catch (error) {
      console.log('Fallo en crear la poliza con 201', response.body);
      throw new Error('Fallo en crear poliza');
    }
  });
});

describe('PATCH /update/carPlates/:vehicleId', () => {
  let response: request.Response;
  const filePath = `${__dirname}/testFiles/test.png`;
  const filePathTwo = `${__dirname}/testFiles/test2.png`;
  const filePathThree = `${__dirname}/testFiles/test.pdf`;

  const patchBody = {
    plates: 'H331FA',
    isEditing: '',
    historyData: {
      step: 'test',
      description: 'test vehicle updated',
      userId: '645bb4bd4d7cd6f6896632d7',
    },
  };

  it('Should response with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/update/circulationCard/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should response with a 400 if not body provided', async () => {
    response = await request(app)
      .patch(`/stock/update/carPlates/${stockVehicleId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body.errors).toBeDefined();
    expect(response.body.errors).not.toHaveLength(0);
  });

  it('should response with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/carPlates/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .attach('frontImg', filePath)
        .attach('backImg', filePathTwo)
        .attach('platesDocument', filePathThree)
        .field('plates', patchBody.plates)
        .field('isEditing', JSON.stringify(patchBody.isEditing))
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should create the circulation card', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/carPlates/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .attach('frontImg', filePath)
        .attach('backImg', filePathTwo)
        .attach('platesDocument', filePathThree)
        .field('plates', patchBody.plates)
        .field('isEditing', JSON.stringify(patchBody.isEditing))
        .field('historyData[step]', patchBody.historyData.step)
        .field('historyData[description]', patchBody.historyData.description)
        .field('historyData[userId]', patchBody.historyData.userId);
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ message: stockVehiclesText.success.vehicleUpdated });
    } catch (error) {
      console.log('Fallo en crear la carta de circulacion con 201', response.body);
      throw new Error('Fallo en crear carta de circulacion');
    }
  });
});

describe('PATCH /update/contract/:vehicleId', () => {
  let response: request.Response;
  const filePath = `${__dirname}/testFiles/test.pdf`;

  it('Should response with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/docs/contract/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should response with a 400 if not body provided', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/contract/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      console.log(response.body, 'data del unico error');
      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors).not.toHaveLength(0);
    }
  });

  it('should response with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/docs/contract/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .attach('contract', filePath);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should create the contract card', async () => {
    try {
      response = await request(app)
        .patch(`/stock/docs/contract/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .attach('contract', filePath);
      expect(response.status).toBe(200);
    } catch (error) {
      console.log(error);
      console.log(response.body, 'Error al crear el contrato con status 200');
      throw new Error('Create contract failed');
    }
  });
});
