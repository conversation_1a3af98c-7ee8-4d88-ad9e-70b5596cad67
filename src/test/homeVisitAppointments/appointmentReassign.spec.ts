import app from '../../app';
import request from 'supertest';
import jwt from 'jsonwebtoken';
import {
  createAdminTest,
  createAdmissionRequestTest,
  createAppointment,
  createHomeVisitor,
  createSchedule,
  createSlot,
} from '../functionts';
import { accessTokenSecret } from '../../constants';
import { DateTime } from 'luxon';
import { NO_HOME_VISITOR_AVAILABLE } from '@/modules/Calendar/dtos/appointment.dto';

let userAdminId: string;
let userAdminRole: string;
let token: string;

beforeAll(async () => {
  const adminUser = await createAdminTest();
  userAdminId = adminUser._id.toString();
  userAdminRole = adminUser.role;
  token = jwt.sign({ userId: userAdminId, role: userAdminRole }, accessTokenSecret, {
    expiresIn: '5m',
  });
});

describe('Test Users with same available slots /calendar/users-with-same-available-slot/:slotId', () => {
  let homeVisitor1: any;
  let schedule1: any;
  let slot1: any;

  let homeVisitor2: any;
  let schedule2: any;
  let slot2: any;
  let slot3: any;
  beforeAll(async () => {
    homeVisitor1 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 1',
    });
    schedule1 = await createSchedule(homeVisitor1._id);
    slot1 = await createSlot({
      userId: homeVisitor1._id,
      scheduleId: schedule1._id,
      date: DateTime.utc().startOf('day').toJSDate(),
      isAvailable: false,
      startTime: '2025-02-25T04:00:00+00:00',
      endTime: '2025-02-25T04:30:00+00:00',
    });

    homeVisitor2 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 2',
    });
    schedule2 = await createSchedule(homeVisitor2._id);
    slot2 = await createSlot({
      userId: homeVisitor2._id,
      scheduleId: schedule2._id,
      date: DateTime.utc().startOf('day').toJSDate(),
      isAvailable: true,
      startTime: '2025-02-25T04:00:00+00:00',
      endTime: '2025-02-25T04:30:00+00:00',
    });
    slot3 = await createSlot({
      userId: homeVisitor2._id,
      scheduleId: schedule2._id,
      date: DateTime.utc().startOf('day').toJSDate(),
      isAvailable: false,
      startTime: '2025-02-25T05:00:00+00:00',
      endTime: '2025-02-25T05:30:00+00:00',
    });
  });

  let response: request.Response;

  it('should return 400 status code and error message if slotId is invalid', async () => {
    response = await request(app)
      .get(`/calendar/users-with-same-available-slot/67bd5cbfca7410f4d7bf038c`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body.success).toEqual(false);
    expect(response.body.error).toEqual({ code: 'slot_not_found', errors: 'Slot not found' });
  });

  it('should return 200 status code and users Array with same available slot', async () => {
    response = await request(app)
      .get(`/calendar/users-with-same-available-slot/${slot1._id}`)
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(200);
    expect(response.body.success).toEqual(true);
    expect(response.body.data).toHaveLength(1);
    expect(response.body.data[0].id).toEqual(slot2._id.toString());
    expect(response.body.data[0].user.id).toEqual(homeVisitor2._id.toString());
  });

  it('should return 200 status code and empty array if no users with same available slot', async () => {
    response = await request(app)
      .get(`/calendar/users-with-same-available-slot/${slot3._id}`)
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(200);
    expect(response.body.success).toEqual(true);
    expect(response.body.data).toHaveLength(0);
  });
});

describe('Test Appointment home visitor change with same available slots /calendar/event/home-visitor-change', () => {
  let admissionRequest: Record<string, any>;

  let homeVisitor1: any;
  let schedule1: any;
  let slot1: any;

  let homeVisitor2: any;
  let schedule2: any;
  let slot2: any;

  let appointment: any;

  beforeAll(async () => {
    admissionRequest = await createAdmissionRequestTest();
    homeVisitor1 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 1',
    });
    schedule1 = await createSchedule(homeVisitor1._id);
    slot1 = await createSlot({
      userId: homeVisitor1._id,
      scheduleId: schedule1._id,
      date: DateTime.utc().startOf('day').toJSDate(),
      isAvailable: false,
      startTime: '2025-02-25T04:00:00+00:00',
      endTime: '2025-02-25T04:30:00+00:00',
    });

    homeVisitor2 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 2',
    });
    schedule2 = await createSchedule(homeVisitor2._id);
    slot2 = await createSlot({
      userId: homeVisitor2._id,
      scheduleId: schedule2._id,
      date: DateTime.utc().startOf('day').toJSDate(),
      isAvailable: true,
      startTime: '2025-02-25T04:00:00+00:00',
      endTime: '2025-02-25T04:30:00+00:00',
    });

    appointment = await createAppointment({
      admissionRequestId: admissionRequest._id,
      user: homeVisitor1._id,
      slot: slot1._id,
      startTime: slot1.startTime,
      endTime: slot1.endTime,
      date: slot1.date,
    });
  });

  let response: request.Response;

  it('should return 400 status code for invalid payload when not sending homeVisitorId', async () => {
    response = await request(app)
      .patch(`/calendar/event/home-visitor-change`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        appointmentId: appointment._id,
        slotId: '67bd5cbfca7410f4d7bf038c',
      });
    expect(response.status).toBe(400);
    expect(response.body.success).toEqual(false);
    expect(response.body.error.code).toEqual('unprocessable_entity');
  });

  it('should return 400 status code for invalid payload when not sending slotId', async () => {
    response = await request(app)
      .patch(`/calendar/event/home-visitor-change`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        appointmentId: appointment._id,
        homeVisitorId: '67bd5cbfca7410f4d7bf038c',
      });
    expect(response.status).toBe(400);
    expect(response.body.success).toEqual(false);
    expect(response.body.error.code).toEqual('unprocessable_entity');
  });

  it('should return 400 status code when appointment Id is invalid', async () => {
    response = await request(app)
      .patch(`/calendar/event/home-visitor-change`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        appointmentId: '67bd5cbfca7410f4d7bf038c',
        homeVisitorId: homeVisitor2._id,
        slotId: slot2._id,
      });
    expect(response.status).toBe(404);
    expect(response.body.error).toEqual({ code: 'appointment_not_found', errors: 'Appointment not found' });
  });

  it('should return 400 status code when slot Id is invalid', async () => {
    response = await request(app)
      .patch(`/calendar/event/home-visitor-change`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        appointmentId: appointment._id,
        homeVisitorId: homeVisitor2._id,
        slotId: '67bd5cbfca7410f4d7bf038c',
      });
    expect(response.status).toBe(404);
    expect(response.body.error).toEqual({ code: 'slot_not_found', errors: 'Slot not found' });
  });

  it('should return 200 status code and send Home Visit Appointment apology to customer when homeVisitor Id is no-home-visitor-available', async () => {
    response = await request(app)
      .patch(`/calendar/event/home-visitor-change`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        appointmentId: appointment._id,
        homeVisitorId: NO_HOME_VISITOR_AVAILABLE,
        slotId: slot2._id,
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toEqual(true);
    expect(response.body.data.id).toEqual(appointment._id.toString());
    expect(response.body.data.user).toEqual(homeVisitor1._id.toString());
    expect(response.body.data.user).not.toEqual(homeVisitor2._id.toString());
  });

  it('should return 200 status code and change home Visitor Id', async () => {
    response = await request(app)
      .patch(`/calendar/event/home-visitor-change`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        appointmentId: appointment._id,
        homeVisitorId: homeVisitor2._id,
        slotId: slot2._id,
      });
    expect(response.status).toBe(200);
    expect(response.body.success).toEqual(true);
    expect(response.body.data.id).toEqual(appointment._id.toString());

    expect(response.body.data.user).not.toEqual(homeVisitor1._id.toString());
    expect(response.body.data.slot).not.toEqual(slot1._id.toString());

    expect(response.body.data.user).toEqual(homeVisitor2._id.toString());
    expect(response.body.data.slot).toEqual(slot2._id.toString());
  });
});
