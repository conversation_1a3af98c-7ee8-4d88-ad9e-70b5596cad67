export const secondPaymentObject = {
  data: {
    fid: 'payment_Rb2upwXXx7',
    hasStripe: false,
    shortURL: 'https://gigstack.xyz/payment_ELgtplwPh0',
    shortUrl: 'https://gigstack.xyz/payment_ELgtplwPh0',
    payments: [],
    discount: 0,
    skc: false,
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    custom_method_types: [
      {
        manualConfirmation: true,
        name: 'Transferencia bancaria',
        logo: 'https://pro-gigstack.s3.us-east-2.amazonaws.com/icons/+bank.png',
        details: 'Pago con transferencia desde tu cuenta bancaria',
        id: 'bank',
      },
    ],
    relatedTo: '',
    invoices: null,
    client: {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      country: 'MEX',
      metadata: {
        internalId: null,
      },
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      phoneNumbers: [
        {
          value: '************',
        },
      ],
      names: [
        {
          displayName: 'Prueba OCN',
        },
      ],
      emailAddresses: [
        {
          value: '<EMAIL>',
        },
      ],
      defaults: {},
      phone: '************',
      name: 'Prueba OCN',
      company: '',
      from: 'gigstack',
      legal_name: '',
      id: 'client_TlDG86MqCf',
      email: '<EMAIL>',
      timestamp: *************,
    },
    currency: 'MXN',
    from: 'API',
    id: 'payment_Rb2upwXXx7',
    timestamp: *************,
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    paidId: 'manual',
    amount: 0,
    clientId: 'client_TlDG86MqCf',
    created: *************,
    internalItems: [
      {
        total: 86,
        quantity: 1,
        product_key: '********',
        name: 'Cargo por reactivacion',
        description: '',
      },
    ],
    recurringEventNumber: null,
    team: 'team_sK9y9Z5oJ7BUMk1',
    processor: '',
    token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************.gDxyXld3icuTJXjh9Z9_oAgMT5SSi-Jvh4OIFpg5DKU',
    binnacle: '',
    v: 2,
    exchange: 1,
    items: [
      {
        owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
        hours: null,
        quantity: 1,
        taxes: [
          {
            withholding: false,
            inclusive: true,
            rate: '0.16',
            type: 'IVA',
            factor: 'Tasa',
          },
        ],
        team: 'team_sK9y9Z5oJ7BUMk1',
        billingAccount: 'ba_t6fKgpgOQBarI9K',
        paymentType: {
          label: 'Precio fijo',
          value: 'fixed',
        },
        unit_name: 'Unidad de servicio',
        total: 3450,
        product_key: '********',
        unit_key: 'E48',
        name: 'Renta Semanal',
        id: 'service_wLLzJcpFek',
        timestamp: *************,
      },
    ],
    conceptReference: '701204',
    totalItemsAmounts: {
      taxesIncluded: 0,
      retentionsString: '$0.00',
      feeAddedString: '$0.00',
      totalWithoutFeeString: '$86.00',
      taxes: 0,
      totalWithoutFee: 86,
      taxesString: '$0.00',
      retentions: 0,
      total: 86,
      feeAdded: 0,
      subtotalString: '$86.00',
      masterDiscountString: '$0.00',
      masterDiscount: 0,
      canSend: true,
      totalString: '$86.00',
    },
    clientID: 'client_TlDG86MqCf',
    updatedOnCreate: *************,
    internalStatus: 'viewed',
    lastViewed: *************,
    viewed: 1,
    lastEmail: *************,
    emailID: ['ScimmHLqSvGx6Wq0vIptsw', 'HkU8MVZKQdaAzmsrBozAvA'],
    bank: {
      completed: false,
    },
    review_created_at: *************,
    clientVoucher: null,
    paidIn: 'bank',
    voucherMeta: null,
    manualSuccess: true,
    payment_form: '03',
    review_status: '',
    status: 'payment_created',
    succeededTimestamp: *************,
    realAmountPaid: 86,
    exchangeRateDate: 'Fri, 10 Nov 2023 00:00:01 +0000',
    exchange_rate: 1,
    exchangeRate: 1,
    emails: {
      lastEventAt: **********,
      lastEvent: 'dropped',
    },
  },
};
export const otherPaymentObject = {
  data: {
    fid: 'payment_Rb2upwXXx7',
    hasStripe: false,
    shortURL: 'https://gigstack.xyz/payment_ELgtplwPh0',
    shortUrl: 'https://gigstack.xyz/payment_ELgtplwPh0',
    payments: [],
    discount: 0,
    skc: false,
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    custom_method_types: [
      {
        manualConfirmation: true,
        name: 'Transferencia bancaria',
        logo: 'https://pro-gigstack.s3.us-east-2.amazonaws.com/icons/+bank.png',
        details: 'Pago con transferencia desde tu cuenta bancaria',
        id: 'bank',
      },
    ],
    relatedTo: '',
    invoices: null,
    client: {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      country: 'MEX',
      metadata: {
        internalId: null,
      },
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      phoneNumbers: [
        {
          value: '************',
        },
      ],
      names: [
        {
          displayName: 'Prueba OCN',
        },
      ],
      emailAddresses: [
        {
          value: '<EMAIL>',
        },
      ],
      defaults: {},
      phone: '************',
      name: 'Prueba OCN',
      company: '',
      from: 'gigstack',
      legal_name: '',
      id: 'client_TlDG86MqCf',
      email: '<EMAIL>',
      timestamp: *************,
    },
    currency: 'MXN',
    from: 'API',
    id: 'payment_Rb2upwXXx7',
    timestamp: *************,
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    paidId: 'manual',
    amount: 0,
    clientId: 'client_TlDG86MqCf',
    created: *************,
    internalItems: [
      {
        total: 86,
        quantity: 1,
        product_key: '********',
        name: 'Cargo por reactivacion',
        description: '',
      },
    ],
    recurringEventNumber: null,
    team: 'team_sK9y9Z5oJ7BUMk1',
    processor: '',
    token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************.gDxyXld3icuTJXjh9Z9_oAgMT5SSi-Jvh4OIFpg5DKU',
    binnacle: '',
    v: 2,
    exchange: 1,
    items: [
      {
        owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
        hours: null,
        quantity: 1,
        taxes: [
          {
            withholding: false,
            inclusive: true,
            rate: '0.16',
            type: 'IVA',
            factor: 'Tasa',
          },
        ],
        team: 'team_sK9y9Z5oJ7BUMk1',
        billingAccount: 'ba_t6fKgpgOQBarI9K',
        paymentType: {
          label: 'Precio fijo',
          value: 'fixed',
        },
        unit_name: 'Unidad de servicio',
        total: 3450,
        product_key: '********',
        unit_key: 'E48',
        name: 'Renta Semanal',
        id: 'service_LlLzJcpFek',
        timestamp: *************,
      },
    ],
    conceptReference: '701204',
    totalItemsAmounts: {
      taxesIncluded: 0,
      retentionsString: '$0.00',
      feeAddedString: '$0.00',
      totalWithoutFeeString: '$86.00',
      taxes: 0,
      totalWithoutFee: 86,
      taxesString: '$0.00',
      retentions: 0,
      total: 86,
      feeAdded: 0,
      subtotalString: '$86.00',
      masterDiscountString: '$0.00',
      masterDiscount: 0,
      canSend: true,
      totalString: '$86.00',
    },
    clientID: 'client_TlDG86MqCf',
    updatedOnCreate: *************,
    internalStatus: 'viewed',
    lastViewed: *************,
    viewed: 1,
    lastEmail: *************,
    emailID: ['ScimmHLqSvGx6Wq0vIptsw', 'HkU8MVZKQdaAzmsrBozAvA'],
    bank: {
      completed: false,
    },
    review_created_at: *************,
    clientVoucher: null,
    paidIn: 'bank',
    voucherMeta: null,
    manualSuccess: true,
    payment_form: '03',
    review_status: '',
    status: 'payment_created',
    succeededTimestamp: *************,
    realAmountPaid: 86,
    exchangeRateDate: 'Fri, 10 Nov 2023 00:00:01 +0000',
    exchange_rate: 1,
    exchangeRate: 1,
    emails: {
      lastEventAt: **********,
      lastEvent: 'dropped',
    },
  },
};

export const secondPaymentObjectUpdated = {
  data: {
    fid: 'payment_Rb2upwXXx7',
    hasStripe: false,
    shortURL: 'https://gigstack.xyz/payment_ELgtplwPh0',
    shortUrl: 'https://gigstack.xyz/payment_ELgtplwPh0',
    payments: [],
    discount: 0,
    skc: false,
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    custom_method_types: [
      {
        manualConfirmation: true,
        name: 'Transferencia bancaria',
        logo: 'https://pro-gigstack.s3.us-east-2.amazonaws.com/icons/+bank.png',
        details: 'Pago con transferencia desde tu cuenta bancaria',
        id: 'bank',
      },
    ],
    relatedTo: '',
    invoices: null,
    client: {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      country: 'MEX',
      metadata: {
        internalId: null,
      },
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      phoneNumbers: [
        {
          value: '************',
        },
      ],
      names: [
        {
          displayName: 'Prueba OCN',
        },
      ],
      emailAddresses: [
        {
          value: '<EMAIL>',
        },
      ],
      defaults: {},
      phone: '************',
      name: 'Prueba OCN',
      company: '',
      from: 'gigstack',
      legal_name: '',
      id: 'client_TlDG86MqCf',
      email: '<EMAIL>',
      timestamp: *************,
    },
    currency: 'MXN',
    from: 'API',
    id: 'payment_Rb2upwXXx7',
    timestamp: *************,
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    paidId: 'manual',
    amount: 0,
    clientId: 'client_TlDG86MqCf',
    created: *************,
    internalItems: [
      {
        total: 86,
        quantity: 1,
        product_key: '********',
        name: 'Cargo por reactivacion',
        description: '',
      },
    ],
    recurringEventNumber: null,
    team: 'team_sK9y9Z5oJ7BUMk1',
    processor: '',
    token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************.gDxyXld3icuTJXjh9Z9_oAgMT5SSi-Jvh4OIFpg5DKU',
    binnacle: '',
    v: 2,
    exchange: 1,
    items: [
      {
        owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
        hours: null,
        quantity: 1,
        taxes: [
          {
            withholding: false,
            inclusive: true,
            rate: '0.16',
            type: 'IVA',
            factor: 'Tasa',
          },
        ],
        team: 'team_sK9y9Z5oJ7BUMk1',
        billingAccount: 'ba_t6fKgpgOQBarI9K',
        paymentType: {
          label: 'Precio fijo',
          value: 'fixed',
        },
        unit_name: 'Unidad de servicio',
        total: 3450,
        product_key: '********',
        unit_key: 'E48',
        name: 'Renta Semanal',
        id: 'service_wLLzJcpFek',
        timestamp: *************,
      },
    ],
    conceptReference: '701204',
    totalItemsAmounts: {
      taxesIncluded: 0,
      retentionsString: '$0.00',
      feeAddedString: '$0.00',
      totalWithoutFeeString: '$86.00',
      taxes: 0,
      totalWithoutFee: 86,
      taxesString: '$0.00',
      retentions: 0,
      total: 86,
      feeAdded: 0,
      subtotalString: '$86.00',
      masterDiscountString: '$0.00',
      masterDiscount: 0,
      canSend: true,
      totalString: '$86.00',
    },
    clientID: 'client_TlDG86MqCf',
    updatedOnCreate: *************,
    internalStatus: 'viewed',
    lastViewed: *************,
    viewed: 1,
    lastEmail: *************,
    emailID: ['ScimmHLqSvGx6Wq0vIptsw', 'HkU8MVZKQdaAzmsrBozAvA'],
    bank: {
      completed: false,
    },
    review_created_at: *************,
    clientVoucher: null,
    paidIn: 'bank',
    voucherMeta: null,
    manualSuccess: true,
    payment_form: '03',
    review_status: '',
    status: 'pending_review',
    succeededTimestamp: *************,
    realAmountPaid: 86,
    exchangeRateDate: 'Fri, 10 Nov 2023 00:00:01 +0000',
    exchange_rate: 1,
    exchangeRate: 1,
    emails: {
      lastEventAt: **********,
      lastEvent: 'dropped',
    },
  },
};
export const secondPaymentObjectSucceedeed = {
  data: {
    fid: 'payment_Rb2upwXXx7',
    hasStripe: false,
    shortURL: 'https://gigstack.xyz/payment_ELgtplwPh0',
    shortUrl: 'https://gigstack.xyz/payment_ELgtplwPh0',
    payments: [],
    discount: 0,
    skc: false,
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    custom_method_types: [
      {
        manualConfirmation: true,
        name: 'Transferencia bancaria',
        logo: 'https://pro-gigstack.s3.us-east-2.amazonaws.com/icons/+bank.png',
        details: 'Pago con transferencia desde tu cuenta bancaria',
        id: 'bank',
      },
    ],
    relatedTo: '',
    invoices: null,
    client: {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      country: 'MEX',
      metadata: {
        internalId: null,
      },
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      phoneNumbers: [
        {
          value: '************',
        },
      ],
      names: [
        {
          displayName: 'Prueba OCN',
        },
      ],
      emailAddresses: [
        {
          value: '<EMAIL>',
        },
      ],
      defaults: {},
      phone: '************',
      name: 'Prueba OCN',
      company: '',
      from: 'gigstack',
      legal_name: '',
      id: 'client_TlDG86MqCf',
      email: '<EMAIL>',
      timestamp: *************,
    },
    currency: 'MXN',
    from: 'API',
    id: 'payment_Rb2upwXXx7',
    timestamp: *************,
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    paidId: 'manual',
    amount: 0,
    clientId: 'client_TlDG86MqCf',
    created: *************,
    internalItems: [
      {
        total: 86,
        quantity: 1,
        product_key: '********',
        name: 'Cargo por reactivacion',
        description: '',
      },
    ],
    recurringEventNumber: null,
    team: 'team_sK9y9Z5oJ7BUMk1',
    processor: '',
    token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************.gDxyXld3icuTJXjh9Z9_oAgMT5SSi-Jvh4OIFpg5DKU',
    binnacle: '',
    v: 2,
    exchange: 1,
    items: [
      {
        owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
        hours: null,
        quantity: 1,
        taxes: [
          {
            withholding: false,
            inclusive: true,
            rate: '0.16',
            type: 'IVA',
            factor: 'Tasa',
          },
        ],
        team: 'team_sK9y9Z5oJ7BUMk1',
        billingAccount: 'ba_t6fKgpgOQBarI9K',
        paymentType: {
          label: 'Precio fijo',
          value: 'fixed',
        },
        unit_name: 'Unidad de servicio',
        total: 3450,
        product_key: '********',
        unit_key: 'E48',
        name: 'Renta Semanal',
        id: 'service_wLLzJcpFek',
        timestamp: *************,
      },
    ],
    conceptReference: '701204',
    totalItemsAmounts: {
      taxesIncluded: 0,
      retentionsString: '$0.00',
      feeAddedString: '$0.00',
      totalWithoutFeeString: '$86.00',
      taxes: 0,
      totalWithoutFee: 86,
      taxesString: '$0.00',
      retentions: 0,
      total: 86,
      feeAdded: 0,
      subtotalString: '$86.00',
      masterDiscountString: '$0.00',
      masterDiscount: 0,
      canSend: true,
      totalString: '$86.00',
    },
    clientID: 'client_TlDG86MqCf',
    updatedOnCreate: *************,
    internalStatus: 'viewed',
    lastViewed: *************,
    viewed: 1,
    lastEmail: *************,
    emailID: ['ScimmHLqSvGx6Wq0vIptsw', 'HkU8MVZKQdaAzmsrBozAvA'],
    bank: {
      completed: false,
    },
    review_created_at: *************,
    clientVoucher: null,
    paidIn: 'bank',
    voucherMeta: null,
    manualSuccess: true,
    payment_form: '03',
    review_status: '',
    status: 'succeedeed',
    succeededTimestamp: *************,
    realAmountPaid: 86,
    exchangeRateDate: 'Fri, 10 Nov 2023 00:00:01 +0000',
    exchange_rate: 1,
    exchangeRate: 1,
    emails: {
      lastEventAt: **********,
      lastEvent: 'dropped',
    },
  },
};

export const paymentObject = {
  fid: 'payment_26WL6Q9PFI44pKt',
  metadata: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    internalID: 'payment_26WL6Q9PFI44pKt',
    items: 1,
  },
  clientID: 'client_TlDG86MqCf',
  livemode: true,
  paymentLimit: null,
  canceled_at: null,
  discount: 0,
  skc: false,
  source: null,
  billingAccount: 'ba_t6fKgpgOQBarI9K',
  relatedTo: '',
  statement_descriptor: '',
  shipping: null,
  invoices: null,
  review: null,
  from: 'manual',
  id: 'payment_26WL6Q9PFI44pKt',
  payment_method: null,
  capture_method: 'automatic',
  paidId: 'manual',
  transfer_group: null,
  on_behalf_of: null,
  created: *************,
  amount_received: 0,
  isManual: false,
  internalItems: [
    {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      hours: null,
      quantity: 1,
      taxes: [
        {
          withholding: false,
          inclusive: true,
          rate: '0.16',
          type: 'IVA',
          factor: 'Tasa',
        },
      ],
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      paymentType: {
        label: 'Precio fijo',
        value: 'fixed',
      },
      unit_name: 'Unidad de servicio',
      total: 3450,
      product_key: '********',
      unit_key: 'E48',
      name: 'Renta Semanal',
      id: 'service_wLLzJcpFek',
      timestamp: *************,
    },
  ],
  confirmation_method: '',
  cancellation_reason: null,
  processor: '',
  charges: null,
  last_payment_error: null,
  payment_intent: null,
  exchange: 1,
  proposals: null,
  application_fee_amount: null,
  items: [
    {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      hours: null,
      quantity: 1,
      taxes: [
        {
          withholding: false,
          inclusive: true,
          rate: '0.16',
          type: 'IVA',
          factor: 'Tasa',
        },
      ],
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      paymentType: {
        label: 'Precio fijo',
        value: 'fixed',
      },
      unit_name: 'Unidad de servicio',
      total: 3450,
      product_key: '********',
      unit_key: 'E48',
      name: 'Renta Semanal',
      id: 'service_wLLzJcpFek',
      timestamp: *************,
    },
  ],
  object: 'payment',
  hasStripe: false,
  automations: null,
  shortURL: 'https://gigstack.xyz/payment_26WL6Q9PFI44pKt',
  shortUrl: 'https://gigstack.xyz/payment_26WL6Q9PFI44pKt',
  payments: [],
  amount_capturable: 0,
  description: '',
  custom_method_types: [
    {
      manualConfirmation: true,
      name: 'Transferencia bancaria',
      logo: 'https://pro-gigstack.s3.us-east-2.amazonaws.com/icons/+bank.png',
      details: 'Pago con transferencia desde tu cuenta bancaria',
      id: 'bank',
    },
  ],
  automatic_payment_methods: null,
  sms: false,
  client: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    country: 'MEX',
    metadata: {
      internalId: null,
    },
    team: 'team_sK9y9Z5oJ7BUMk1',
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    phoneNumbers: [
      {
        value: '************',
      },
    ],
    names: [
      {
        displayName: 'Prueba OCN',
      },
    ],
    emailAddresses: [
      {
        value: '<EMAIL>',
      },
    ],
    defaults: {},
    phone: '************',
    name: 'Prueba OCN',
    company: '',
    from: 'gigstack',
    legal_name: '',
    id: 'client_TlDG86MqCf',
    email: '<EMAIL>',
    timestamp: *************,
  },
  currency: 'mxn',
  timestamp: *************,
  owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
  amount: 345000,
  clientId: 'client_TlDG86MqCf',
  payment_method_types: [],
  recurringEventNumber: null,
  team: 'team_sK9y9Z5oJ7BUMk1',
  token:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.6A9YA99Zm7dKbtdKKyl3D6iRoy9pSztq3kUW5NeDl8Q',
  binnacle: '',
  limitDaysToPay: null,
  application: null,
  receipt_email: '<EMAIL>',
  v: 2,
  next_action: null,
  conceptReference: '465016',
  customer: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    country: 'MEX',
    metadata: {
      internalId: null,
    },
    team: 'team_sK9y9Z5oJ7BUMk1',
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    phoneNumbers: [
      {
        value: '************',
      },
    ],
    names: [
      {
        displayName: 'Prueba OCN',
      },
    ],
    emailAddresses: [
      {
        value: '<EMAIL>',
      },
    ],
    defaults: {},
    phone: '************',
    name: 'Prueba OCN',
    from: 'gigstack',
    company: '',
    legal_name: '',
    id: 'client_TlDG86MqCf',
    email: '<EMAIL>',
    timestamp: *************,
  },
  totalItemsAmounts: {
    taxesIncluded: 475.*************,
    retentionsString: '$0.00',
    feeAddedString: '$0.00',
    totalWithoutFeeString: '$2,974.14',
    taxes: 475.*************,
    totalWithoutFee: 3450,
    taxesString: '$475.86',
    retentions: 0,
    total: 3450,
    feeAdded: 0,
    subtotalString: '$2,974.14',
    masterDiscountString: '$0.00',
    masterDiscount: 0,
    canSend: true,
    totalString: '$3,450.00',
  },
  updatedOnCreate: *************,
  internalStatus: 'viewed',
  lastViewed: *************,
  viewed: 1,
  lastEmail: *************,
  emailID: ['-OI0hX09Q4i6rfzvWmRTWQ', 'dEk7lartTJ2vepS-VvLLmA'],
  bank: {
    completed: false,
  },
  review_created_at: *************,
  clientVoucher: null,
  paidIn: 'bank',
  voucherMeta: null,
  emails: {
    lastEventAt: **********,
    lastEvent: 'dropped',
  },
  manualSuccess: true,
  payment_form: '03',
  review_status: '',
  status: 'payment_created',
  succeededTimestamp: *************,
  realAmountPaid: 3450,
  exchangeRateDate: 'Thu, 09 Nov 2023 00:00:01 +0000',
  exchange_rate: 1,
  exchangeRate: 1,
};
export const paymentObjectReviewPending = {
  fid: 'payment_26WL6Q9PFI44pKt',
  metadata: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    internalID: 'payment_26WL6Q9PFI44pKt',
    items: 1,
  },
  clientID: 'client_TlDG86MqCf',
  livemode: true,
  paymentLimit: null,
  canceled_at: null,
  discount: 0,
  skc: false,
  source: null,
  billingAccount: 'ba_t6fKgpgOQBarI9K',
  relatedTo: '',
  statement_descriptor: '',
  shipping: null,
  invoices: null,
  review: null,
  from: 'manual',
  id: 'payment_26WL6Q9PFI44pKt',
  payment_method: null,
  capture_method: 'automatic',
  paidId: 'manual',
  transfer_group: null,
  on_behalf_of: null,
  created: *************,
  amount_received: 0,
  isManual: false,
  internalItems: [
    {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      hours: null,
      quantity: 1,
      taxes: [
        {
          withholding: false,
          inclusive: true,
          rate: '0.16',
          type: 'IVA',
          factor: 'Tasa',
        },
      ],
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      paymentType: {
        label: 'Precio fijo',
        value: 'fixed',
      },
      unit_name: 'Unidad de servicio',
      total: 3450,
      product_key: '********',
      unit_key: 'E48',
      name: 'Renta Semanal',
      id: 'service_wLLzJcpFek',
      timestamp: *************,
    },
  ],
  confirmation_method: '',
  cancellation_reason: null,
  processor: '',
  charges: null,
  last_payment_error: null,
  payment_intent: null,
  exchange: 1,
  proposals: null,
  application_fee_amount: null,
  items: [
    {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      hours: null,
      quantity: 1,
      taxes: [
        {
          withholding: false,
          inclusive: true,
          rate: '0.16',
          type: 'IVA',
          factor: 'Tasa',
        },
      ],
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      paymentType: {
        label: 'Precio fijo',
        value: 'fixed',
      },
      unit_name: 'Unidad de servicio',
      total: 3450,
      product_key: '********',
      unit_key: 'E48',
      name: 'Renta Semanal',
      id: 'service_wLLzJcpFek',
      timestamp: *************,
    },
  ],
  object: 'payment',
  hasStripe: false,
  automations: null,
  shortURL: 'https://gigstack.xyz/payment_26WL6Q9PFI44pKt',
  shortUrl: 'https://gigstack.xyz/payment_26WL6Q9PFI44pKt',
  payments: [],
  amount_capturable: 0,
  description: '',
  custom_method_types: [
    {
      manualConfirmation: true,
      name: 'Transferencia bancaria',
      logo: 'https://pro-gigstack.s3.us-east-2.amazonaws.com/icons/+bank.png',
      details: 'Pago con transferencia desde tu cuenta bancaria',
      id: 'bank',
    },
  ],
  automatic_payment_methods: null,
  sms: false,
  client: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    country: 'MEX',
    metadata: {
      internalId: null,
    },
    team: 'team_sK9y9Z5oJ7BUMk1',
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    phoneNumbers: [
      {
        value: '************',
      },
    ],
    names: [
      {
        displayName: 'Prueba OCN',
      },
    ],
    emailAddresses: [
      {
        value: '<EMAIL>',
      },
    ],
    defaults: {},
    phone: '************',
    name: 'Prueba OCN',
    company: '',
    from: 'gigstack',
    legal_name: '',
    id: 'client_TlDG86MqCf',
    email: '<EMAIL>',
    timestamp: *************,
  },
  currency: 'mxn',
  timestamp: *************,
  owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
  amount: 345000,
  clientId: 'client_TlDG86MqCf',
  payment_method_types: [],
  recurringEventNumber: null,
  team: 'team_sK9y9Z5oJ7BUMk1',
  token:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.6A9YA99Zm7dKbtdKKyl3D6iRoy9pSztq3kUW5NeDl8Q',
  binnacle: '',
  limitDaysToPay: null,
  application: null,
  receipt_email: '<EMAIL>',
  v: 2,
  next_action: null,
  conceptReference: '465016',
  customer: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    country: 'MEX',
    metadata: {
      internalId: null,
    },
    team: 'team_sK9y9Z5oJ7BUMk1',
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    phoneNumbers: [
      {
        value: '************',
      },
    ],
    names: [
      {
        displayName: 'Prueba OCN',
      },
    ],
    emailAddresses: [
      {
        value: '<EMAIL>',
      },
    ],
    defaults: {},
    phone: '************',
    name: 'Prueba OCN',
    from: 'gigstack',
    company: '',
    legal_name: '',
    id: 'client_TlDG86MqCf',
    email: '<EMAIL>',
    timestamp: *************,
  },
  totalItemsAmounts: {
    taxesIncluded: 475.*************,
    retentionsString: '$0.00',
    feeAddedString: '$0.00',
    totalWithoutFeeString: '$2,974.14',
    taxes: 475.*************,
    totalWithoutFee: 3450,
    taxesString: '$475.86',
    retentions: 0,
    total: 3450,
    feeAdded: 0,
    subtotalString: '$2,974.14',
    masterDiscountString: '$0.00',
    masterDiscount: 0,
    canSend: true,
    totalString: '$3,450.00',
  },
  updatedOnCreate: *************,
  internalStatus: 'viewed',
  lastViewed: *************,
  viewed: 1,
  lastEmail: *************,
  emailID: ['-OI0hX09Q4i6rfzvWmRTWQ', 'dEk7lartTJ2vepS-VvLLmA'],
  bank: {
    completed: false,
  },
  review_created_at: *************,
  clientVoucher: null,
  paidIn: 'bank',
  voucherMeta: null,
  emails: {
    lastEventAt: **********,
    lastEvent: 'dropped',
  },
  manualSuccess: true,
  payment_form: '03',
  review_status: '',
  status: 'pending_review',
  succeededTimestamp: *************,
  realAmountPaid: 3450,
  exchangeRateDate: 'Thu, 09 Nov 2023 00:00:01 +0000',
  exchange_rate: 1,
  exchangeRate: 1,
};
export const paymentObjectSucceedeed = {
  fid: 'payment_26WL6Q9PFI44pKt',
  metadata: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    internalID: 'payment_26WL6Q9PFI44pKt',
    items: 1,
  },
  clientID: 'client_TlDG86MqCf',
  livemode: true,
  paymentLimit: null,
  canceled_at: null,
  discount: 0,
  skc: false,
  source: null,
  billingAccount: 'ba_t6fKgpgOQBarI9K',
  relatedTo: '',
  statement_descriptor: '',
  shipping: null,
  invoices: null,
  review: null,
  from: 'manual',
  id: 'payment_26WL6Q9PFI44pKt',
  payment_method: null,
  capture_method: 'automatic',
  paidId: 'manual',
  transfer_group: null,
  on_behalf_of: null,
  created: *************,
  amount_received: 0,
  isManual: false,
  internalItems: [
    {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      hours: null,
      quantity: 1,
      taxes: [
        {
          withholding: false,
          inclusive: true,
          rate: '0.16',
          type: 'IVA',
          factor: 'Tasa',
        },
      ],
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      paymentType: {
        label: 'Precio fijo',
        value: 'fixed',
      },
      unit_name: 'Unidad de servicio',
      total: 3450,
      product_key: '********',
      unit_key: 'E48',
      name: 'Renta Semanal',
      id: 'service_wLLzJcpFek',
      timestamp: *************,
    },
  ],
  confirmation_method: '',
  cancellation_reason: null,
  processor: '',
  charges: null,
  last_payment_error: null,
  payment_intent: null,
  exchange: 1,
  proposals: null,
  application_fee_amount: null,
  items: [
    {
      owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
      hours: null,
      quantity: 1,
      taxes: [
        {
          withholding: false,
          inclusive: true,
          rate: '0.16',
          type: 'IVA',
          factor: 'Tasa',
        },
      ],
      team: 'team_sK9y9Z5oJ7BUMk1',
      billingAccount: 'ba_t6fKgpgOQBarI9K',
      paymentType: {
        label: 'Precio fijo',
        value: 'fixed',
      },
      unit_name: 'Unidad de servicio',
      total: 3450,
      product_key: '********',
      unit_key: 'E48',
      name: 'Renta Semanal',
      id: 'service_wLLzJcpFek',
      timestamp: *************,
    },
  ],
  object: 'payment',
  hasStripe: false,
  automations: null,
  shortURL: 'https://gigstack.xyz/payment_26WL6Q9PFI44pKt',
  shortUrl: 'https://gigstack.xyz/payment_26WL6Q9PFI44pKt',
  payments: [],
  amount_capturable: 0,
  description: '',
  custom_method_types: [
    {
      manualConfirmation: true,
      name: 'Transferencia bancaria',
      logo: 'https://pro-gigstack.s3.us-east-2.amazonaws.com/icons/+bank.png',
      details: 'Pago con transferencia desde tu cuenta bancaria',
      id: 'bank',
    },
  ],
  automatic_payment_methods: null,
  sms: false,
  client: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    country: 'MEX',
    metadata: {
      internalId: null,
    },
    team: 'team_sK9y9Z5oJ7BUMk1',
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    phoneNumbers: [
      {
        value: '************',
      },
    ],
    names: [
      {
        displayName: 'Prueba OCN',
      },
    ],
    emailAddresses: [
      {
        value: '<EMAIL>',
      },
    ],
    defaults: {},
    phone: '************',
    name: 'Prueba OCN',
    company: '',
    from: 'gigstack',
    legal_name: '',
    id: 'client_TlDG86MqCf',
    email: '<EMAIL>',
    timestamp: *************,
  },
  currency: 'mxn',
  timestamp: *************,
  owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
  amount: 345000,
  clientId: 'client_TlDG86MqCf',
  payment_method_types: [],
  recurringEventNumber: null,
  team: 'team_sK9y9Z5oJ7BUMk1',
  token:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.6A9YA99Zm7dKbtdKKyl3D6iRoy9pSztq3kUW5NeDl8Q',
  binnacle: '',
  limitDaysToPay: null,
  application: null,
  receipt_email: '<EMAIL>',
  v: 2,
  next_action: null,
  conceptReference: '465016',
  customer: {
    owner: 'vJOCrkaWUuM9Pp287nl8gielqmu2',
    country: 'MEX',
    metadata: {
      internalId: null,
    },
    team: 'team_sK9y9Z5oJ7BUMk1',
    billingAccount: 'ba_t6fKgpgOQBarI9K',
    phoneNumbers: [
      {
        value: '************',
      },
    ],
    names: [
      {
        displayName: 'Prueba OCN',
      },
    ],
    emailAddresses: [
      {
        value: '<EMAIL>',
      },
    ],
    defaults: {},
    phone: '************',
    name: 'Prueba OCN',
    from: 'gigstack',
    company: '',
    legal_name: '',
    id: 'client_TlDG86MqCf',
    email: '<EMAIL>',
    timestamp: *************,
  },
  totalItemsAmounts: {
    taxesIncluded: 475.*************,
    retentionsString: '$0.00',
    feeAddedString: '$0.00',
    totalWithoutFeeString: '$2,974.14',
    taxes: 475.*************,
    totalWithoutFee: 3450,
    taxesString: '$475.86',
    retentions: 0,
    total: 3450,
    feeAdded: 0,
    subtotalString: '$2,974.14',
    masterDiscountString: '$0.00',
    masterDiscount: 0,
    canSend: true,
    totalString: '$3,450.00',
  },
  updatedOnCreate: *************,
  internalStatus: 'viewed',
  lastViewed: *************,
  viewed: 1,
  lastEmail: *************,
  emailID: ['-OI0hX09Q4i6rfzvWmRTWQ', 'dEk7lartTJ2vepS-VvLLmA'],
  bank: {
    completed: false,
  },
  review_created_at: *************,
  clientVoucher: null,
  paidIn: 'bank',
  voucherMeta: null,
  emails: {
    lastEventAt: **********,
    lastEvent: 'dropped',
  },
  manualSuccess: true,
  payment_form: '03',
  review_status: 'succeeded',
  status: 'succeeded',
  succeededTimestamp: *************,
  realAmountPaid: 3450,
  exchangeRateDate: 'Thu, 09 Nov 2023 00:00:01 +0000',
  exchange_rate: 1,
  exchangeRate: 1,
};

export const wire4SpeiIncoming = {
  _id: {
    $oid: '654bbba4429509c9f490ca66',
  },
  id: 'evt_4286142d5b0622382c0bc604e44a78b2ab0556a6be3bd81f56609bb5ff158776',
  object: 'spei_incoming',
  api_version: '1.0.0',
  created: {
    $date: '2023-11-08T16:47:32.819Z',
  },
  data: {
    beneficiary_account: '1111111',
    amount: 999.99,
    currency_code: 'MXP',
    deposit_date: {
      $date: '2023-11-08T16:47:31.000Z',
    },
    confirm_date: {
      $date: '2023-11-08T16:47:31.000Z',
    },
    depositant: 'Prueba',
    depositant_clabe: '112680009511111111',
    depositant_email: '<EMAIL>',
    depositant_rfc: 'XAXX010101000',
    monex_description:
      'Emisor: HSBC | Fecha Confirmacion de Liquidacion: 08-11-2023 10:47:31 | Monto: 999.99 | Cuenta Ordenante: ********** | Nombre del Ordenante: Pablo Solana | Clave de Rastreo: *************** | Referencia Numerica: 2222 | Concepto de Pago: Pago semanal',
    monex_transaction_id: '**********',
    sender_account: '**********',
    sender_name: 'Pablo Solana',
    sender_rfc: 'XAXX010101000',
    description: 'Pago semanal',
    reference: '2222',
    clave_rastreo: '***************',
  },
  livemode: false,
  pending_webhooks: 0,
  type: 'TRANSACTION.INCOMING.UPDATED',
  __v: 0,
};
