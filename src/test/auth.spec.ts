import request from 'supertest';
import app from '../app';
import User from '../models/userSchema';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { genericMessages, invitationSecret } from '../constants';

beforeAll(async () => {
  const password = 'mypassword';
  const hashedPassword = await bcrypt.hash(password, 12);
  await User.create({
    email: '<EMAIL>',
    name: '<PERSON><PERSON><PERSON>',
    password: hashedPassword,
    role: 'agent',
    city: 'cdmx',
    settings: {
      allowedRegions: [
        'cdmx',
        'gdl',
        'mty',
        'tij',
        'pbc',
        'qro',
        'pbe',
        'tol',
        'ptv',
        'tep',
        'col',
        'sal',
        'torr',
        'dur',
        'mxli',
        'her',
        'chi',
        'leo',
        'ags',
        'slp',
        'mer',
      ],
    },
  });
});

describe('User Login with POST /auth/login', () => {
  let response: request.Response;
  const body = {
    email: 'email<PERSON><EMAIL>',
    password: 'mypassword',
  };
  it(`should return user not found if user doesn't exists`, async () => {
    const notExists = {
      email: '<EMAIL>',
      password: 'anything',
    };
    response = await request(app).post(`/auth/login`).send(notExists);
    expect(response.body).toEqual({ message: 'Usuario no encontrado' });
    expect(response.status).toBe(400);
  });

  it('should return 200 status code', async () => {
    response = await request(app).post(`/auth/login`).send(body);
    expect(response.status).toBe(200);
  });

  it('should return the data and accessToken', async () => {
    expect(response.body).toEqual({
      message: genericMessages.success.users.loggin,
      accessToken: response.body.accessToken,
      user: response.body.user,
    });
    expect(response.status).toBe(200);
  });
});

describe('Create User with Invitation  with POST /auth/acceptInvitation', () => {
  let response: request.Response;
  const body = {
    email: '<EMAIL>',
    password: 'mypassword',
  };

  let invitationToken = jwt.sign({ email: body.email }, invitationSecret, {
    expiresIn: '2m',
  });

  const body2 = {
    password: 'mypassword',
  };

  it('should return 200 status code', async () => {
    const url = `/auth/acceptInvitation?code=${invitationToken}`;
    response = await request(app).post(url).send(body2);
    expect(response.status).toBe(200);
  });

  it('should return "Usuario creado con exito" message', async () => {
    expect(response.body).toEqual({ message: 'Usuario creado con exito' });
  });
});
