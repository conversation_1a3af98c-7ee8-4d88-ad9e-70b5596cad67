import { logger } from '@/clean/lib/logger';
import { AllowedMimeType, parseImageText, Source } from '@/services/ocr/llmClients/anthropicClient';
import { getDidiEarningsData } from '@/services/ocr/didiEarningsExtractor';
import { getDidiProfileData } from '@/services/ocr/didiProfileExtractor';
import { getLyftProfileData } from '@/services/ocr/lyftProfileExtractor';
import { getUberEarningsData } from '@/services/ocr/uberEarningsExtractor';
import { getUberProfileData } from '@/services/ocr/uberProfileExtractor';
import Anthropic from '@anthropic-ai/sdk';

// Mock the logger
jest.mock('@/clean/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock the parseImageText function
jest.mock('@/services/ocr/anthropicClient', () => ({
  parseImageText: jest.fn(),
  AllowedMimeType: {
    JPEG: 'image/jpeg',
    PNG: 'image/png',
    GIF: 'image/gif',
    WEBP: 'image/webp',
  },
}));

describe('parseImageText', () => {
  const client = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY, // This is the default and can be omitted
  });

  const mockImage = {
    buffer: Buffer.from('mockImageData'),
    mimetype: 'image/jpeg' as AllowedMimeType,
  } as Express.Multer.File;

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should throw an error if the MIME type is not supported', async () => {
    const invalidSource = {
      data: 'base64encodeddata',
      media_type: 'image/bmp' as AllowedMimeType, // Unsupported MIME type
      prompt: 'Describe this image',
    };

    await expect(parseImageText(invalidSource)).rejects.toThrow(
      `Unsupported MIME type: ${invalidSource.media_type}`
    );
    expect(logger.info).toHaveBeenCalledWith(`[parseImageText] - Source: ${JSON.stringify(invalidSource)}`);
  });

  it('should call the Anthropic API with the correct parameters', async () => {
    const validSource = {
      data: 'base64encodeddata',
      media_type: 'image/jpeg' as AllowedMimeType,
      prompt: 'Describe this image',
    };

    const mockResponse = {
      content: [
        {
          type: 'text',
          text: '{"description": "A beautiful landscape"}',
        },
      ],
    };

    (client.messages.create as jest.Mock).mockResolvedValue(mockResponse);

    const result = await parseImageText(validSource);

    expect(logger.info).toHaveBeenCalledWith(`[parseImageText] - Source: ${JSON.stringify(validSource)}`);
    expect(client.messages.create).toHaveBeenCalledWith({
      model: expect.any(String), // Assuming ANTHROPIC_MODEL is a string
      max_tokens: 1024,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'image',
              source: {
                type: 'base64',
                media_type: validSource.media_type,
                data: validSource.data,
              },
            },
            { type: 'text', text: validSource.prompt },
          ],
        },
      ],
    });
    expect(logger.info).toHaveBeenCalledWith(`[parseImageText] - Response: ${JSON.stringify(mockResponse)}`);
    expect(result).toEqual({ description: 'A beautiful landscape' });
  });

  it('should throw an error if the Anthropic API call fails', async () => {
    const validSource = {
      data: 'base64encodeddata',
      media_type: 'image/jpeg' as AllowedMimeType,
      prompt: 'Describe this image',
    };

    (client.messages.create as jest.Mock).mockRejectedValue(new Error('API Error'));

    await expect(parseImageText(validSource)).rejects.toThrow('API Error');
  });

  it('should call parseImageText with the correct parameters', async () => {
    const mockResponse = {
      weeklyEarnings: [
        {
          startDate: '2024-12-02',
          endDate: '2024-12-09',
          amount: 100,
        },
      ],
      currentMonth: 'December',
      currentYear: 2024,
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getDidiEarningsData(mockImage);

    const expectedSource: Source = {
      data: mockImage.buffer.toString('base64'),
      media_type: mockImage.mimetype as AllowedMimeType,
      prompt: expect.any(String),
    };

    expect(parseImageText).toHaveBeenCalledWith(expectedSource);
    expect(result).toEqual(mockResponse);
  });

  it('should throw an error if parseImageText throws an error', async () => {
    const mockError = new Error('API Error');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getDidiEarningsData(mockImage)).rejects.toThrow('API Error');
  });

  it('should handle unsupported MIME types gracefully', async () => {
    const invalidImage = {
      buffer: Buffer.from('mockImageData'),
      mimetype: 'image/bmp', // Unsupported MIME type
    } as Express.Multer.File;

    const mockError = new Error('Mime type not supported');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getDidiEarningsData(invalidImage)).rejects.toThrow('Mime type not supported');
  });

  it('should handle empty or invalid responses from parseImageText', async () => {
    const mockResponse = null; // Simulate an invalid response
    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getDidiEarningsData(mockImage);
    expect(result).toBeNull();
  });

  it('should call parseImageText with the correct parameters', async () => {
    const mockResponse = {
      fullName: 'John Doe',
      acceptanceRate: 95,
      completedTripsRate: 90,
      rating: 4.8,
      registrationCity: 'New York',
      completedTripsThisWeek: 25,
      yearsActive: 3,
      passengersServed: 1500,
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getDidiProfileData(mockImage);

    const expectedSource: Source = {
      data: mockImage.buffer.toString('base64'),
      media_type: mockImage.mimetype as AllowedMimeType,
      prompt: expect.any(String),
    };

    expect(parseImageText).toHaveBeenCalledWith(expectedSource);
    expect(result).toEqual(mockResponse);
  });

  it('should throw an error if parseImageText throws an error', async () => {
    const mockError = new Error('API Error');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getDidiProfileData(mockImage)).rejects.toThrow('API Error');
  });

  it('should handle unsupported MIME types gracefully', async () => {
    const invalidImage = {
      buffer: Buffer.from('mockImageData'),
      mimetype: 'image/bmp', // Unsupported MIME type
    } as Express.Multer.File;

    const mockError = new Error('Mime type not supported');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getDidiProfileData(invalidImage)).rejects.toThrow('Mime type not supported');
  });

  it('should handle empty or invalid responses from parseImageText', async () => {
    const mockResponse = null; // Simulate an invalid response
    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getDidiProfileData(mockImage);
    expect(result).toBeNull();
  });

  it('should handle partial data in the response', async () => {
    const mockResponse = {
      fullName: 'John Doe',
      acceptanceRate: 95,
      completedTripsRate: null, // Missing field
      rating: 4.8,
      registrationCity: 'New York',
      completedTripsThisWeek: 25,
      yearsActive: null, // Missing field
      passengersServed: 1500,
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getDidiProfileData(mockImage);
    expect(result).toEqual(mockResponse);
  });

  it('should handle special characters in names', async () => {
    const mockResponse = {
      fullName: 'Jöhn Dœ',
      acceptanceRate: 95,
      completedTripsRate: 90,
      rating: 4.8,
      registrationCity: 'New York',
      completedTripsThisWeek: 25,
      yearsActive: 3,
      passengersServed: 1500,
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getDidiProfileData(mockImage);
    expect(result.fullName).toBe('Jöhn Dœ'); // Ensure special characters are preserved
  });

  it('should call parseImageText with the correct parameters', async () => {
    const mockResponse = {
      name: 'Joe Pierce',
      vehicle: 'Kia K5',
      rides: 12714,
      rating: 5,
      experience: '10 years',
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getLyftProfileData(mockImage);

    const expectedSource: Source = {
      data: mockImage.buffer.toString('base64'),
      media_type: mockImage.mimetype as AllowedMimeType,
      prompt: expect.any(String),
    };

    expect(parseImageText).toHaveBeenCalledWith(expectedSource);
    expect(result).toEqual(mockResponse);
  });

  it('should throw an error if parseImageText throws an error', async () => {
    const mockError = new Error('API Error');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getLyftProfileData(mockImage)).rejects.toThrow('API Error');
  });

  it('should handle unsupported MIME types gracefully', async () => {
    const invalidImage = {
      buffer: Buffer.from('mockImageData'),
      mimetype: 'image/bmp', // Unsupported MIME type
    } as Express.Multer.File;

    const mockError = new Error('Mime type not supported');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getLyftProfileData(invalidImage)).rejects.toThrow('Mime type not supported');
  });

  it('should handle empty or invalid responses from parseImageText', async () => {
    const mockResponse = null; // Simulate an invalid response
    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getLyftProfileData(mockImage);
    expect(result).toBeNull();
  });

  it('should handle partial data in the response', async () => {
    const mockResponse = {
      name: 'Joe Pierce',
      vehicle: null, // Missing field
      rides: 12714,
      rating: 5,
      experience: null, // Missing field
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getLyftProfileData(mockImage);
    expect(result).toEqual(mockResponse);
  });

  it('should handle special characters in names', async () => {
    const mockResponse = {
      name: 'Jöhn Dœ',
      vehicle: 'Kia K5',
      rides: 12714,
      rating: 5,
      experience: '10 years',
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getLyftProfileData(mockImage);
    expect(result.name).toBe('Jöhn Dœ'); // Ensure special characters are preserved
  });

  it('should handle experience range values correctly', async () => {
    const mockResponse = {
      name: 'Joe Pierce',
      vehicle: 'Kia K5',
      rides: 12714,
      rating: 5,
      experience: '7 months', // Higher range value from "3 - 7 months"
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getLyftProfileData(mockImage);
    expect(result.experience).toBe('7 months');
  });

  it('should handle experience in years and months correctly', async () => {
    const mockResponse = {
      name: 'Joe Pierce',
      vehicle: 'Kia K5',
      rides: 12714,
      rating: 5,
      experience: '3.7 years', // Converted from "3 years and 7 months"
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getLyftProfileData(mockImage);
    expect(result.experience).toBe('3.7 years');
  });

  it('should call parseImageText with the correct parameters', async () => {
    const mockResponse = {
      weeklyEarnings: [
        {
          startDate: '2024-12-02',
          endDate: '2024-12-09',
          amount: 500,
        },
      ],
      currentMonth: 'December',
      currentYear: 2024,
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberEarningsData(mockImage);

    const expectedSource: Source = {
      data: mockImage.buffer.toString('base64'),
      media_type: mockImage.mimetype as AllowedMimeType,
      prompt: expect.any(String),
    };

    expect(parseImageText).toHaveBeenCalledWith(expectedSource);
    expect(result).toEqual(mockResponse);
  });

  it('should throw an error if parseImageText throws an error', async () => {
    const mockError = new Error('API Error');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getUberEarningsData(mockImage)).rejects.toThrow('API Error');
  });

  it('should handle unsupported MIME types gracefully', async () => {
    const invalidImage = {
      buffer: Buffer.from('mockImageData'),
      mimetype: 'image/bmp', // Unsupported MIME type
    } as Express.Multer.File;

    const mockError = new Error('Mime type not supported');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getUberEarningsData(invalidImage)).rejects.toThrow('Mime type not supported');
  });

  it('should handle empty or invalid responses from parseImageText', async () => {
    const mockResponse = null; // Simulate an invalid response
    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberEarningsData(mockImage);
    expect(result).toBeNull();
  });

  it('should handle partial data in the response', async () => {
    const mockResponse = {
      weeklyEarnings: [
        {
          startDate: '2024-12-02',
          endDate: '2024-12-09',
          amount: 500,
        },
      ],
      currentMonth: null, // Missing field
      currentYear: 2024,
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberEarningsData(mockImage);
    expect(result).toEqual(mockResponse);
  });

  it('should handle date ranges across years correctly', async () => {
    const mockResponse = {
      weeklyEarnings: [
        {
          startDate: '2024-12-30',
          endDate: '2025-01-06',
          amount: 600,
        },
      ],
      currentMonth: 'January',
      currentYear: 2025,
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberEarningsData(mockImage);
    expect(result.weeklyEarnings[0].startDate).toBe('2024-12-30');
    expect(result.weeklyEarnings[0].endDate).toBe('2025-01-06');
  });

  it('should handle year transition for months before the current month', async () => {
    const mockResponse = {
      weeklyEarnings: [
        {
          startDate: '2023-12-25',
          endDate: '2023-12-31',
          amount: 400,
        },
      ],
      currentMonth: 'January',
      currentYear: 2024,
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberEarningsData(mockImage);
    expect(result.weeklyEarnings[0].startDate).toBe('2023-12-25');
    expect(result.weeklyEarnings[0].endDate).toBe('2023-12-31');
  });

  it('should call parseImageText with the correct parameters', async () => {
    const mockResponse = {
      fullName: 'John Doe',
      rating: 4.8,
      completedTrips: 1200,
      acceptanceRate: 95,
      cancellationRate: 2,
      timeUsingApp: '5.3 years',
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberProfileData(mockImage);

    const expectedSource: Source = {
      data: mockImage.buffer.toString('base64'),
      media_type: mockImage.mimetype as AllowedMimeType,
      prompt: expect.any(String),
    };

    expect(parseImageText).toHaveBeenCalledWith(expectedSource);
    expect(result).toEqual(mockResponse);
  });

  it('should throw an error if parseImageText throws an error', async () => {
    const mockError = new Error('API Error');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getUberProfileData(mockImage)).rejects.toThrow('API Error');
  });

  it('should handle unsupported MIME types gracefully', async () => {
    const invalidImage = {
      buffer: Buffer.from('mockImageData'),
      mimetype: 'image/bmp', // Unsupported MIME type
    } as Express.Multer.File;

    const mockError = new Error('Mime type not supported');
    (parseImageText as jest.Mock).mockRejectedValue(mockError);

    await expect(getUberProfileData(invalidImage)).rejects.toThrow('Mime type not supported');
  });

  it('should handle empty or invalid responses from parseImageText', async () => {
    const mockResponse = null; // Simulate an invalid response
    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberProfileData(mockImage);
    expect(result).toBeNull();
  });

  it('should handle partial data in the response', async () => {
    const mockResponse = {
      fullName: 'John Doe',
      rating: null, // Missing field
      completedTrips: 1200,
      acceptanceRate: null, // Missing field
      cancellationRate: 2,
      timeUsingApp: '5.3 years',
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberProfileData(mockImage);
    expect(result).toEqual(mockResponse);
  });

  it('should handle special characters in names', async () => {
    const mockResponse = {
      fullName: 'Jöhn Dœ',
      rating: 4.8,
      completedTrips: 1200,
      acceptanceRate: 95,
      cancellationRate: 2,
      timeUsingApp: '5.3 years',
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberProfileData(mockImage);
    expect(result.fullName).toBe('Jöhn Dœ'); // Ensure special characters are preserved
  });

  it('should handle time using app as a range', async () => {
    const mockResponse = {
      fullName: 'John Doe',
      rating: 4.8,
      completedTrips: 1200,
      acceptanceRate: 95,
      cancellationRate: 2,
      timeUsingApp: '7 months', // Higher range value from "3 - 7 months"
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberProfileData(mockImage);
    expect(result.timeUsingApp).toBe('7 months');
  });

  it('should handle time using app in years and months', async () => {
    const mockResponse = {
      fullName: 'John Doe',
      rating: 4.8,
      completedTrips: 1200,
      acceptanceRate: 95,
      cancellationRate: 2,
      timeUsingApp: '5.3 years', // Converted from "5 years and 3 months"
    };

    (parseImageText as jest.Mock).mockResolvedValue(mockResponse);

    const result = await getUberProfileData(mockImage);
    expect(result.timeUsingApp).toBe('5.3 years');
  });
});
