name: Security Scan

on:
  push:
    branches: [ "master", "develop", "staging" ]
  pull_request:
    branches: [ "master", "develop", "staging" ]

jobs:
  security_checks:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '22.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm install

    - name: Check for Dependency Vulnerabilities
      run: |
        npm audit --production || true
        echo "Audit for production dependencies completed."

    - name: Lint Code
      run: npm run lint
