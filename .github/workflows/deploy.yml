name:  Deploy CI/CD Pipeline 🚀
on:
  push:
    branches: [ "master", "develop", "staging" ]
  pull_request:
    branches: [ "master", "staging" ]

env:
  MONGODB_URI: '${{ vars.MONGODB_URI }}'
  ACCESS_TOKEN_SECRET: '${{ vars.ACCESS_TOKEN_SECRET }}'
  RECOVER_PASSWORD_SECRET: '${{ vars.RECOVER_PASSWORD_SECRET }}'
  INVITATION_SECRET: '${{ vars.INVITATION_SECRET }}'
  EMAIL_SENDER_PASSWORD: '${{ vars.VITE_MESSAGING_SENDER_ID }}'
  EMAIL_SENDER: '${{ vars.EMAIL_SENDER }}'
  AWS_BUCKET_NAME: '${{ vars.AWS_BUCKET_NAME }}'
  AWS_BUCKET_REGION: '${{ vars.AWS_BUCKET_REGION }}'
  AWS_BUCKET_PUBLIC_KEY: '${{ vars.AWS_BUCKET_PUBLIC_KEY }}'
  AWS_BUCKET_SECRET_KEY: '${{ vars.AWS_BUCKET_SECRET_KEY }}'

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [22.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - run: npm i
    - run: npm run lint
    - run: npm run build --if-present
    # - run: npm test --if-present

  devDeploy:
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    needs: build

    steps:
     - name: Checkout code
       uses: actions/checkout@v2

     - name: Set up SSH
       run: |
          mkdir -p ~/.ssh
          echo "${{ vars.PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -t rsa dev-api.onecarnow.com >> ~/.ssh/known_hosts

     - name: SSH to remote server and execute script
       run: |
        ssh -i ~/.ssh/id_rsa -o "StrictHostKeyChecking=no" ${{ vars.USER_NAME }}@dev-api.onecarnow.com 'bash deploy.sh'

  prodDeploy:
    if: github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
    needs: build

    steps:
     - name: Checkout code
       uses: actions/checkout@v2

     - name: Set up SSH
       run: |
          mkdir -p ~/.ssh
          echo "${{ vars.PRIVATE_PROD_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -t rsa ${{ vars.PROD_HOST }} >> ~/.ssh/known_hosts

     - name: SSH to remote server and execute script
       run: |
        ssh -i ~/.ssh/id_rsa -o "StrictHostKeyChecking=no" ${{ vars.PROD_AUTH }} 'bash deploy.sh'
        
  DRDeploy:
    if: github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
    needs: build

    steps:
     - name: Checkout code
       uses: actions/checkout@v2

     - name: Set up SSH
       run: |
          mkdir -p ~/.ssh
          echo "${{ vars.PRIVATE_KEY_DR }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -t rsa ${{ vars.DR_HOST }} >> ~/.ssh/known_hosts

     - name: SSH to remote server and execute script
       run: |
        ssh -i ~/.ssh/id_rsa -o "StrictHostKeyChecking=no" ${{ vars.DR_AUTH }} 'bash deploy.sh'
  
  StagingDeploy:
    if: github.ref == 'refs/heads/staging'
    runs-on: ubuntu-latest
    needs: build

    steps:
     - name: Checkout code
       uses: actions/checkout@v2

     - name: Set up SSH
       run: |
          mkdir -p ~/.ssh
          echo "${{ vars.PRIVATE_KEY_STAGING }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -t rsa ${{ vars.STAGING_HOST }} >> ~/.ssh/known_hosts

     - name: SSH to remote server and execute script
       run: |
        ssh -i ~/.ssh/id_rsa -o "StrictHostKeyChecking=no" ${{ vars.STAGING_AUTH }} 'bash deploy.sh'
        
